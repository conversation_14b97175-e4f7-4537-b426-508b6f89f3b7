package com.sibuqu.work.commom.result;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> @Description:
 * @date 2021年4月12日13:40:44
 */
@Getter
@AllArgsConstructor
public enum UserResultCode {

    // 用户模块
    E_11001(11001, "参数异常"),
    E_11002(11002, "手机号码输入错误，请再核实一下吧~"),
    E_11003(11003, "手机号或验证码不能为空"),
    E_11004(11004, "手机号已注册"),
    E_11005(11005, "验证码已过期，请重新获取"),
    E_11006(11006, "username不能为空"),
    E_11007(11007, "未查询到验证码"),
    E_11008(11008, "验证码错误，请重新接收或通过语音接听。本日验证码获取剩余{0}次"),
    E_11009(11009, "抱歉，您已经输错5次，请明天再试"),
    E_11010(11010, "Header请使用：application/json"),
    E_11011(11011, "认证token不合法"),
    //E_11012(11012,"认证token已过期"),
    E_11012(11012, "网络安全验证通过,需要您重新登录账号"),
    E_11013(11013, "该手机号已存在,不能变更为您的登录新手机号"),
    E_11014(11014, "该手机号是您正在使用的登录手机号，不需要变更"),
    E_11015(11015, "获取登录用户失败，请联系管理员"),
    E_11016(11016, "验证码不匹配，请重新输入"),
    E_11017(11017, "抱歉，验证码今日获取已满5次，请明天再试"),
    E_11018(11018, "抱歉，该账号已被使用"),
    E_11019(11019, "账号不存在"),
    E_11020(11020, "账号密码不匹配"),
    E_11021(11021, "该账号已注销"),
    E_11022(11022, "该账号已绑定微信，请解除原先的微信"),
    E_11023(11023, "新旧密码不可相同"),
    E_11024(11024, "原密码不正确"),
    E_11025(11025, "您未绑定账号，无法修改密码"),
    E_11026(11026, "当前账号已绑定账号"),
    E_11027(11027, "当前账号已绑定微信"),
    E_11028(11028, "当前手机号已被别人绑定，请更换手机号"),
    E_11029(11029,  "您未绑定账号，无法重置密码"),
    E_11030(11030,  "该手机号未注册"),
    E_11031(11031,  "手机号已与其他微信号绑定。如要重新绑定，请使用该手机号登录，解绑微信后，与本微信号绑定。"),
    E_70001(70001, "请补齐用户姓名，家庭结构，年龄阶段，性别信息");

    private Integer code;
    private String msg;

    public static String getMsg(Integer code) {
        for (UserResultCode userResultCode : UserResultCode.values()) {
            if (userResultCode.getCode().equals(code)) {
                return userResultCode.msg;
            }
        }
        return null;
    }
}
