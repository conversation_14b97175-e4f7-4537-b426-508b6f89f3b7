package com.sibuqu.work.commom.constants;


import com.sibuqu.base.common.constant.CacheKeyConfig;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> @LocalDateTime 2021-03-09 18:02
 * @description Redis缓存key 常量类
 */
public class RedisConstants {

    /**
     * 作业答题结果缓存key  classId:currId:stuId
     */
    public static final CacheKeyConfig QUESTION_STU_ANSWER_KEY = new CacheKeyConfig("work:QUESTION_STU_ANSWER:%s:%s:%s",30, TimeUnit.MINUTES);
    public static final String RANK = "userRank:";
    public static final String RANK_GENERATE = "RANK_GENERATE:";
    public static final String HEART_PERCEPTION_LIST = "heartPerceptionList:";
    public static final String CLASS_STATISTICS = "classStatistics:";
    public static final String CLASS_STATISTICS_NEW = "classStatisticsNew:";
    public static final String COURSE_OLD_USER = "courseOldUser:";
}
