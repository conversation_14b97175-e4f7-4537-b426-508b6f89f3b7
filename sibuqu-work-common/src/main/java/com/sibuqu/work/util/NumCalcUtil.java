package com.sibuqu.work.util;

import java.math.RoundingMode;

/**
 * <AUTHOR> @date 2021-04-22 10:28
 * @description 数值计算工具类
 */
public class NumCalcUtil {

    public static Integer getRate(Integer num1, Integer num2) {
        java.text.NumberFormat numberFormat = java.text.DecimalFormat.getNumberInstance();
        //设置精确到小数点后0位
        numberFormat.setMaximumFractionDigits(0);
        numberFormat.setRoundingMode(RoundingMode.FLOOR);
        String result = numberFormat.format((float) num1 / (float) num2 * 100);
        return Integer.parseInt(result);
    }

    public static void main(String[] args) {
        getRate(8, 9);
    }
}
