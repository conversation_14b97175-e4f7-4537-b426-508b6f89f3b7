package com.sibuqu.work.util;



import java.time.LocalDate;
import java.time.LocalDateTime;

public class WeekUtil {
    public static LocalDate getDayByWeek(int week) {
        LocalDateTime localDateTime = LocalDateTime.now();
        // 当前日期星期数
        int currWeek = localDateTime.getDayOfWeek().getValue();
        if (currWeek == week) {
            return LocalDate.now();
        } else if (currWeek < week) {
            for (int i = 1; i <= 7; i++) {
                localDateTime = localDateTime.plusDays(1);
                if (week == localDateTime.getDayOfWeek().getValue()) {
                    break;
                }
            }
            // atZone()方法返回在指定时区从此Instant生成的ZonedDateTime。
            return localDateTime.toLocalDate();

        } else {
            for (int i = 1; i <= 7; i++) {
                localDateTime = localDateTime.plusDays(-1);
                if (week == localDateTime.getDayOfWeek().getValue()) {
                    break;
                }
            }
            // atZone()方法返回在指定时区从此Instant生成的ZonedDateTime。
            return localDateTime.toLocalDate();
        }
    }
}
