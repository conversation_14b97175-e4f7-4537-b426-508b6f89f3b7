package com.sibuqu.work.util;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Version 1.0.0
 * @Description 手动获取spring bean
 * @CreateTime 2022年07月18日 19:57:00
 */
@Component
public class ApplicationContextUtil implements ApplicationContextAware {
    private static ApplicationContext act;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        act = applicationContext;
    }

    /**
     * 根据bean的名字获取工厂中对应的bean对象
     *
     * @param beanName
     * @return
     */
    public static Object getBean(String beanName) {
        return act.getBean(beanName);
    }

    /**
     * 根据bean的类型获取工厂中对应的bean对象
     *
     * @param beanClass
     * @return
     */
    public static <T> T getBean(Class<T> beanClass) {
        return act.getBean(beanClass);
    }
}
