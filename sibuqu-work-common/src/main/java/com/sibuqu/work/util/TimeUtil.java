package com.sibuqu.work.util;

import com.sibuqu.base.common.utils.LocalDateTimeUtil;
import com.sibuqu.common.enums.CommentTypeEnum;
import com.sibuqu.common.enums.TimeEnums;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class TimeUtil {

    private static DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");


    public static String getCreateTimeStr(LocalDateTime createTime) {
        String result = "";
        if (createTime.plusSeconds(60).isAfter(LocalDateTime.now())) {
            result = TimeEnums.JUST_NOW.getTitle();
        } else if (createTime.plusSeconds(60).isBefore(LocalDateTime.now()) && createTime.plusHours(1).isAfter(LocalDateTime.now())) {
            result = Duration.between(createTime, LocalDateTime.now()).toMinutes() + TimeEnums.MANY_MINUTES.getTitle();
        } else if (createTime.plusHours(1).isBefore(LocalDateTime.now()) && createTime.plusDays(1).isAfter(LocalDateTime.now())) {
            result = Duration.between(createTime, LocalDateTime.now()).toHours() + TimeEnums.MANY_HOUR.getTitle();
        } else if (createTime.plusDays(1).isBefore(LocalDateTime.now())) {
            if (createTime.getYear() < LocalDateTime.now().getYear()) {
                result = createTime.format(formatter);
            } else {
                result = createTime.getMonthValue() + "-" + createTime.getDayOfMonth();
            }
        }
        return result;
    }

}
