package com.sibuqu.work.util;

import com.sibuqu.base.common.result.HeaderUserInfo;
import com.sibuqu.work.interceptor.HeaderUserInfoHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Optional;

@Slf4j
public class HeaderUserInfoUtil {

    public static HeaderUserInfo get() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String userTokenInfoStr = request.getHeader("userTokenInfo");
        String authorizationValue = request.getHeader("Authorization");
        String appOs = request.getHeader("appOs");
        String appCurrentCompanyId = request.getHeader("currentCompanyId");
        return HeaderUserInfoHelper.getHeaderUserInfoByHeader(userTokenInfoStr, authorizationValue, appOs, appCurrentCompanyId);
    }

    /**
     * 用户是否未登录
     *
     * @return 未登录返回 true
     */
    public static boolean isNotLogin(HeaderUserInfo headerUserInfo) {
        return Optional.ofNullable(headerUserInfo).map(HeaderUserInfo::getId).map(HeaderUserInfoUtil::isNotLogin).orElse(false);
    }

    /**
     * 用户是否未登录
     *
     * @return 未登录返回 true
     */
    public static boolean isNotLogin(Integer userId) {
        return userId == null || userId == -1 || userId == 0;
    }

    /**
     * 用户是否未登录
     *
     * @return 登录返回 true
     */
    public static boolean isLogin(Integer userId) {
        return !isNotLogin(userId);
    }
}
