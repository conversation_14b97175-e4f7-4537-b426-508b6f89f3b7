package com.sibuqu.work.interceptor.crypt.impl;


import com.sibuqu.work.interceptor.crypt.ICrypt;
import com.sibuqu.work.util.AESUtil;

public class AESCryptImpl implements ICrypt {

    @Override
    public String encrypt(String plain) {
        try {
            return AESUtil.encrypt(plain);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return plain;
    }

    @Override
    public String decrypt(String cipher) {
        try {
            return AESUtil.decrypt(cipher);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return cipher;
    }
}
