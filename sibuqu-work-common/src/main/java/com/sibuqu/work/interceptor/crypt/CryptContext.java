package com.sibuqu.work.interceptor.crypt;


import com.sibuqu.work.enums.EncryptTypeEnum;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 加密方式上下文
 **/
@Component
public class CryptContext {

    private static final Map<EncryptTypeEnum, ICrypt> cryptMap = new HashMap<>(EncryptTypeEnum.values().length);

    /**
     * 获取加密方式（默认AES加密）
     * @param encryptTypeEnum 加密方式枚举
     * @return 加密方式实现类
     */
    public static ICrypt getCrypt(EncryptTypeEnum encryptTypeEnum) {
        ICrypt crypt = cryptMap.get(encryptTypeEnum);
        if (crypt == null) {
            crypt = cryptMap.get(EncryptTypeEnum.AES);
        }

        return crypt;
    }

    /**
     * 设置加密方式
     * @param encryptTypeEnum 加密类型
     * @param crypt         加载方式
     */
    public static void setCrypt(EncryptTypeEnum encryptTypeEnum, ICrypt crypt) {
        cryptMap.put(encryptTypeEnum, crypt);
    }

}
