package com.sibuqu.work.interceptor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.sibuqu.base.common.result.HeaderUserCompanyInfo;
import com.sibuqu.base.common.result.HeaderUserInfo;
import com.sibuqu.starter.logging.common.utils.LogUtils;
import com.sibuqu.starter.redis.util.RedisUtil;
import com.sibuqu.work.annation.RequestHeaderUser;
import com.sibuqu.work.util.ApplicationContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import java.net.URLDecoder;
import java.util.Objects;
import java.util.regex.Pattern;

@Slf4j
public class HeaderUserMethodArgumentResolver implements HandlerMethodArgumentResolver {

    @Override
    public boolean supportsParameter(MethodParameter methodParameter) {
        if (methodParameter.getParameterType().isAssignableFrom(HeaderUserInfo.class)
                && methodParameter.hasParameterAnnotation(RequestHeaderUser.class)) {
            return true;
        }
        return false;
    }

    /**
     * 获取请求头中的userTokenInfo，处理之后返回用户信息
     */
    @Override
    public Object resolveArgument(MethodParameter methodParameter,
                                  ModelAndViewContainer modelAndViewContainer,
                                  NativeWebRequest nativeWebRequest,
                                  WebDataBinderFactory webDataBinderFactory) throws Exception {
        String userTokenInfo = nativeWebRequest.getHeader("userTokenInfo");
        String appOs = nativeWebRequest.getHeader("appOs");
        String appCurrentCompanyId = nativeWebRequest.getHeader("currentCompanyId");
        String appVersionCode = nativeWebRequest.getHeader("appVersionCode");
        HeaderUserInfo headerUserInfo;
        if (StrUtil.isBlank(userTokenInfo)) {
            // 网关没有解析用户信息  从redis中获取
            String token = getToken(nativeWebRequest);
            if (Objects.equals(appOs, "pc")) {
                // PC端token
                token = "pcToken:".concat(token);
            }
            headerUserInfo = getUserInfoByToken(token, appOs);
        } else {
            headerUserInfo = JSON.parseObject(userTokenInfo, HeaderUserInfo.class);
        }
        if (Objects.nonNull(headerUserInfo)) {
            try {
                if (ObjectUtil.isNotEmpty(headerUserInfo.getUserFullName())) {
                    headerUserInfo.setUserFullName(URLDecoder.decode(headerUserInfo.getUserFullName(), "UTF-8"));
                }
                if (ObjectUtil.isNotEmpty(headerUserInfo.getCompanyUserName())) {
                    headerUserInfo.setCompanyUserName(URLDecoder.decode(headerUserInfo.getCompanyUserName(), "UTF-8"));
                }
                if (StrUtil.isNotBlank(appVersionCode) && Pattern.matches("\\d+", appVersionCode)) {
                    headerUserInfo.setAppVersionCode(Integer.parseInt(appVersionCode));
                }
                if (StrUtil.isNotBlank(appCurrentCompanyId) && Pattern.matches("^-?\\d+$", appCurrentCompanyId)) {
                    headerUserInfo.setCurrentCompanyId(Integer.parseInt(appCurrentCompanyId));
                }
                if (Objects.nonNull(headerUserInfo.getCurrentCompanyId()) && headerUserInfo.getCurrentCompanyId() > 0 && CollUtil.isNotEmpty(headerUserInfo.getCompanyInfoVOList())) {
                    for (HeaderUserCompanyInfo companyInfoVO : headerUserInfo.getCompanyInfoVOList()) {
                        if (StrUtil.isNotBlank(companyInfoVO.getCompanyName())) {
                            companyInfoVO.setCompanyName(URLDecoder.decode(companyInfoVO.getCompanyName(), "UTF-8"));
                        }
                    }
                }
            } catch (Exception e) {
                log.error("HeaderUserMethodArgumentResolver error:{}", e.getMessage(), e);
            }
        }
        log.info("userTokenInfo:{},appOs:{},currentCompanyId:{},appVersionCode:{},userInfo:{}", userTokenInfo, appOs, appCurrentCompanyId, appVersionCode, JSON.toJSONString(headerUserInfo));
        return headerUserInfo;
    }

    /**
     * 获取请求头中的 token串
     */
    public String getToken(NativeWebRequest nativeWebRequest) {
        //验证是否带token
        String token = nativeWebRequest.getHeader("Authorization");
        if (StringUtils.isNotBlank(token)) {
            if (token.startsWith("Bearer")) {
                token = StringUtils.substring(token, "Bearer".length()).trim();
            } else {
                token = nativeWebRequest.getHeader("Authorization");
            }
        }
        return token;
    }

    /**
     * 获取请求头中的 token串
     */
    public HeaderUserInfo getUserInfoByToken(String token, String appOs) {
        if (Objects.isNull(token)){
            return null;
        }
        HeaderUserInfo userInfoResult = new HeaderUserInfo();
        RedisUtil redisUtil = ApplicationContextUtil.getBean(RedisUtil.class);
        Object tokenCacheData = redisUtil.get(token);
        // 从redis中获取用户信息
        if (ObjectUtil.isNotEmpty(token) && ObjectUtil.isNotNull(tokenCacheData)) {
            String tokenUserInfoJson = (String) tokenCacheData;
            // 处理redis格式问题
            tokenUserInfoJson = tokenUserInfoJson.replaceAll("\\\\", "");
            userInfoResult = JSON.parseObject(tokenUserInfoJson, HeaderUserInfo.class);
            if(ObjectUtil.isNull(userInfoResult.getCurrentCompanyId()) ||
                    Objects.equals(userInfoResult.getCurrentCompanyId(), 0)){
                // 转换默认个人版
                userInfoResult.setCurrentCompanyId(-1);
            }
        }else{
            userInfoResult.setId(0);
            userInfoResult.setCurrentCompanyId(-1);
        }
        userInfoResult.setAppOs(appOs);
        LogUtils.info("userInfo:"+ JSON.toJSONString(userInfoResult));
        return userInfoResult;
    }
}
