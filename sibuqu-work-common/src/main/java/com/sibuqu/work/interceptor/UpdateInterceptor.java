package com.sibuqu.work.interceptor;

import com.baomidou.mybatisplus.annotation.TableId;
import com.sibuqu.work.annation.CryptEntity;
import com.sibuqu.work.annation.CryptField;
import com.sibuqu.work.interceptor.crypt.CryptContext;
import org.apache.ibatis.binding.MapperMethod;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.session.defaults.DefaultSqlSession;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 数据库更新操作拦截器
 * 一、支持的使用场景
 * ①场景一：通过mybatis-plus BaseMapper自动映射的方法
 * ②场景一：通过mapper接口自定义的方法，更新对象为实体类
 * 二、使用方法
 * 查询条件加密使用方式：①使用添加注解的实体类作为参数 ②使用 @Param("crypt")注解的自定义类型
 * 返回结果解密使用方式：①在实体类上加上注解 CryptEntity  ②在需要加解密的字段属性上加上CryptField
 */
@Component
@Intercepts({@Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class})})
public class UpdateInterceptor implements Interceptor {
    /**
     * 使用 @Param("crypt")注解的参数（只支持自定义对象 且同时添加CryptEntity和CryptField注解）
     */
    private static final String paramVal = "sibuqu";
 
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        //代理类方法参数，该拦截器拦截的update方法有两个参数args = {MappedStatement.class, Object.class}
        Object[] args = invocation.getArgs();
        //获取方法参数
        MappedStatement mappedStatement = (MappedStatement) args[0];
        Object parameter = args[1];
        if (Objects.isNull(parameter)) {
            //无参数，直接放行
            return invocation.proceed();
        }
        // 如果是多个参数或使用Param注解（Param注解会将参数放置在ParamMap中）
        if (parameter instanceof MapperMethod.ParamMap) {
            Map paramMap = (Map) parameter;
            if (paramMap.containsKey(paramVal)) {
                Object updateParameter = paramMap.get(paramVal);
                if (needToCrypt(updateParameter)) {
                    //执行sql，还原加解密后的报文
                    return proceed(invocation, mappedStatement, updateParameter);
                }
            }
            if (paramMap.containsKey("et")) {
                Object updateParameter = paramMap.get("et");
                if (needToCrypt(updateParameter)) {
                    //执行sql，还原加解密后的报文
                    return proceed(invocation, mappedStatement, updateParameter);
                }
            }

        } else if (parameter instanceof DefaultSqlSession.StrictMap) {
            return invocation.proceed();
        } else if (needToCrypt(parameter)) {
            //执行sql，还原加解密后的报文
            return proceed(invocation, mappedStatement, parameter);
        }
        //其他场景直接放行
        return invocation.proceed();
    }
 
    /**
     * 执行sql，还原加解密后的报文
     *
     * @param invocation
     * @param mappedStatement
     * @param parameter
     * @return
     * @throws IllegalAccessException
     * @throws InstantiationException
     * @throws InvocationTargetException
     */
    Object proceed(Invocation invocation, MappedStatement mappedStatement, Object parameter) throws IllegalAccessException, InstantiationException, InvocationTargetException {
        //先复制一个对象备份数据
        Object newInstance = newInstance(parameter);
        //调用加解密服务
        try {
            encrypt(parameter);
        } catch (Exception e) {
            e.printStackTrace();
        }
        //执行操作，得到返回结果
        Object result = invocation.proceed();
        //把加解密后的字段还原
        reductionParameter(mappedStatement, newInstance, parameter);
        //返回结果
        return result;
    }
 
    /**
     * 先复制一个对象备份数据,便于加解密后还原原报文
     *
     * @param parameter
     * @return
     * @throws IllegalAccessException
     * @throws InstantiationException
     */
    private Object newInstance(Object parameter) throws IllegalAccessException, InstantiationException {
        Object newInstance = parameter.getClass().newInstance();
        BeanUtils.copyProperties(parameter, newInstance);
        return newInstance;
    }
 
    /**
     * 把加解密后的字段还原，同时把mybatis返回的tableId返回给参数对象
     *
     * @param mappedStatement
     * @param newInstance
     * @param parameter
     * @throws IllegalAccessException
     */
    private void reductionParameter(MappedStatement mappedStatement, Object newInstance, Object parameter) throws IllegalAccessException {
        //获取映射语句命令类型
        SqlCommandType sqlCommandType = mappedStatement.getSqlCommandType();
        if (SqlCommandType.INSERT == sqlCommandType) {
            //从参数属性中找到注解是TableId的字段
            Field[] parameterFields = parameter.getClass().getDeclaredFields();
            Optional<Field> optional = Arrays.stream(parameterFields).filter(field -> field.isAnnotationPresent(TableId.class)).findAny();
            if (optional.isPresent()) {
                Field field = optional.get();
                field.setAccessible(true);
                Object id = field.get(parameter);
                //覆盖参数加解密的值
                BeanUtils.copyProperties(newInstance, parameter);
                field.set(parameter, id);
            } else {
                //覆盖参数加解密的值
                BeanUtils.copyProperties(newInstance, parameter);
            }
        } else {
            //覆盖参数加解密的值
            BeanUtils.copyProperties(newInstance, parameter);
        }
    }
 
    /**
     * 是否需要加解密：
     * ① 是否属于基本类型，void类型和String类型，如果是，不加解密
     * ② 实体类上是否有注解
     * ③ 属性是否有注解
     *
     * @param object
     * @return
     */
    public boolean needToCrypt(Object object) {
        if (object == null) {
            return false;
        }
        Class<?> clazz = object.getClass();
        if (clazz.isPrimitive() || object instanceof String) {
            //基本类型和字符串不加解密
            return false;
        }
        //获取实体类注解
        boolean annotationPresent = clazz.isAnnotationPresent(CryptEntity.class);
        if (!annotationPresent) {
            //无注解不加解密
            return false;
        }
        //获取属性注解
        Field[] fields = clazz.getDeclaredFields();
        return Arrays.stream(fields).anyMatch(field -> field.isAnnotationPresent(CryptField.class));
    }


    /**
     *
     * @param parameterObject 需要加密的对象
     * @return
     * @throws Exception
     */
    public Object encrypt(Object parameterObject) throws Exception {
        Field[] declaredFields = parameterObject.getClass().getDeclaredFields();
        for (Field field : declaredFields) {
            // 取出所有被encryptDecryptField注解的字段
            CryptField cryptAnnotation = field.getAnnotation(CryptField.class);
            if (cryptAnnotation != null) {
                // accessible指示反射的对象爱使用时取消java语言访问检查
                field.setAccessible(true);
                Object obj = field.get(parameterObject);
                // 只对String类型进行加密
                if (obj instanceof String) {
                    String val = obj.toString();
                    // 如果有标识则不加密，没有则加密并加上标识前缀
                    field.set(parameterObject, CryptContext.getCrypt(cryptAnnotation.type()).encrypt(val));
                }
            }
        }
        return parameterObject;
    }
}