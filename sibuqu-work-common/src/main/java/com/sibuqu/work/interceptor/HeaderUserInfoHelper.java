package com.sibuqu.work.interceptor;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.sibuqu.base.common.result.HeaderUserCompanyInfo;
import com.sibuqu.base.common.result.HeaderUserInfo;
import com.sibuqu.starter.redis.util.RedisUtil;
import com.sibuqu.work.util.ApplicationContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.net.URLDecoder;
import java.util.Objects;
import java.util.regex.Pattern;

@Slf4j
public class HeaderUserInfoHelper {

    /**
     * 根据请求的 header 信息获取用户信息
     *
     * @param userTokenInfoStr   用户信息 json 串
     * @param authorizationValue Authorization 对应的 value
     * @param appOs              客户端来源
     * @return 用户信息实体类
     */
    public static HeaderUserInfo getHeaderUserInfoByHeader(String userTokenInfoStr, String authorizationValue, String appOs, String appCurrentCompanyId) {
        // 网关没有解析用户信息  从redis中获取
        if (StringUtils.isBlank(userTokenInfoStr)) {
            String token = getTokenByAuthorization(authorizationValue);
            userTokenInfoStr = getUserInfoByToken(token, appOs);
        }
        if (StringUtils.isBlank(userTokenInfoStr)) {
            HeaderUserInfo headerUserInfo = new HeaderUserInfo();
            if (appOs != null) {
                headerUserInfo.setAppOs(appOs);
            }
            return headerUserInfo;
        }
        HeaderUserInfo headerUserInfo = JSON.parseObject(userTokenInfoStr, HeaderUserInfo.class);
        if (StrUtil.isNotBlank(appCurrentCompanyId) && Pattern.matches("^\\d+$", appCurrentCompanyId)) {
            headerUserInfo.setCurrentCompanyId(Integer.valueOf(appCurrentCompanyId));
        }
        try {
            if (ObjectUtil.isNotNull(headerUserInfo) && ObjectUtil.isNotEmpty(headerUserInfo.getUserFullName())) {
                headerUserInfo.setUserFullName(URLDecoder.decode(headerUserInfo.getUserFullName(), "UTF-8"));
            }
            if (Objects.nonNull(headerUserInfo) && Objects.nonNull(headerUserInfo.getCurrentCompanyId()) && headerUserInfo.getCurrentCompanyId() > 0) {
                for (HeaderUserCompanyInfo companyInfoVO : headerUserInfo.getCompanyInfoVOList()) {
                    if (StrUtil.isNotBlank(companyInfoVO.getCompanyName())) {
                        companyInfoVO.setCompanyName(URLDecoder.decode(companyInfoVO.getCompanyName(), "UTF-8"));
                    }
                }
            }
        } catch (Exception e) {
            log.error("Exception don't throw, error:{}", e.getMessage(), e);
        }
        if (appOs != null) {
            headerUserInfo.setAppOs(appOs);
        }
        return headerUserInfo;
    }

    /**
     * 获取请求头中的 token串
     */
    private static String getTokenByAuthorization(String authorizationValue) {
        // 验证是否带token
        if (StringUtils.isBlank(authorizationValue)) return null;
        String bearer = "Bearer";
        if (authorizationValue.startsWith(bearer)) {
            authorizationValue = authorizationValue.substring(bearer.length()).trim();
        }
        return authorizationValue;
    }

    /**
     * 通过 token 从 redis 中获取用户信息 json 串
     */
    private static String getUserInfoByToken(String token, String appOs) {
        if (StringUtils.isBlank(token)) return null;

        // 从redis中获取用户信息
        RedisUtil redisUtil = ApplicationContextUtil.getBean(RedisUtil.class);
        // pc 端额外处理下
        if ("pc".equals(appOs)) token = "pcToken:" + token;
        String tokenUserInfoJson = (String) redisUtil.get(token);
        if (StringUtils.isBlank(tokenUserInfoJson)) return null;
        return tokenUserInfoJson.replaceAll("\\\\", "");
    }

}
