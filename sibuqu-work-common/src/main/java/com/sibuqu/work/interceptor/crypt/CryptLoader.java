package com.sibuqu.work.interceptor.crypt;


import com.sibuqu.work.enums.EncryptTypeEnum;
import com.sibuqu.work.interceptor.crypt.impl.AESCryptImpl;
import org.springframework.stereotype.Component;

/**
 * 加密实现类加载器（当前已有AES BASE64两种方式）
 * 后续如果有新增 需要同步添加
 **/
@Component
public class CryptLoader {

    /**
     * 加载所有加密方式实现类
     */
    public CryptLoader() {
        CryptContext.setCrypt(EncryptTypeEnum.AES, new AESCryptImpl());
    }
}