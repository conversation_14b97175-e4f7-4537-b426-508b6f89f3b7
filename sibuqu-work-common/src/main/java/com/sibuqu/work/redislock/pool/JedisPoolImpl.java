
package com.sibuqu.work.redislock.pool;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

/**
 * Redis连接池实现
 * @author： zsj
 */
@Slf4j
@Service
public class JedisPoolImpl {

	private static JedisPool pool;
	private RedisLockProperties redisLockProperties;


	public JedisPoolImpl(RedisLockProperties redisLockProperties) {
		this.redisLockProperties = redisLockProperties;
		try {
			JedisPoolConfig config = new JedisPoolConfig();
			config.setMaxTotal(redisLockProperties.getMaxTotal());//setMaxActive
			config.setMaxIdle(redisLockProperties.getMaxIdle());
			config.setMaxWaitMillis(redisLockProperties.getMaxWaitMillis());//setMaxWait
			config.setMinIdle(redisLockProperties.getMinIdle());
			config.setTestOnBorrow(redisLockProperties.isTestOnBorrow());
			config.setTestOnReturn(redisLockProperties.isTestOnReturn());
			String ip = redisLockProperties.getHost();//读取配置文件
			int port = redisLockProperties.getPort();//读取配置文件
			String password = redisLockProperties.getPassword();
			int timeOut = redisLockProperties.getTimeout();
			if (StringUtils.isNotBlank(ip) && port > 1000) {
				pool = new JedisPool(config, ip,
						port, timeOut,password);
				log.info("jedisPool ready on : host " + ip + " port "
						+ port + " ...");
			} else {
				log.error("jedisPool init false ...");
			}
		} catch (Exception e) {
			log.error("jedisPool init false ...");
		}
	}

//	public static final void returnResource(Jedis redis) {
//		if (redis == null) {
//			return;
//		}
//		if (pool != null) {
//			pool.returnResource(redis);
//			return;
//		}
//		throw new RuntimeException("pool is null");
//	}
//
//	public static final void returnBrokenResource(Jedis redis) {
//		if (redis == null) {
//			return;
//		}
//		if (pool == null) {
//			throw new RuntimeException("poll is null");
//		}
//		pool.returnBrokenResource(redis);
//	}
//
	public static final Jedis getResource() {
		Jedis jedis = null;
		if (pool == null) {
			throw new RuntimeException("poll is null");
		}
		try {
			jedis = pool.getResource();
		} catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return jedis;
	}

	public static final void destory() {
		if (pool != null) {
			pool.destroy();
		}
	}



}
