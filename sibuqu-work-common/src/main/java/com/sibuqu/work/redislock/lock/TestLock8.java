package com.sibuqu.work.redislock.lock;

import com.sibuqu.work.redislock.JedisUtil;
import com.sibuqu.work.redislock.pool.JedisPoolImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.exceptions.JedisException;

@Service
@Slf4j
public class TestLock8 {

	public void test() {
		Jedis jedis = JedisPoolImpl.getResource();
		JedisDistributedLock lock = new JedisDistributedLock(jedis, "testlock", 15000, 10000);//acquireTimeoutMillis:超过该时间没有获取到锁，直接返回false; expiryTimeMillis:锁过期时间)
		boolean broken = false;
		try {
			lock.acquireEp();//lock.acquire();
			// do some stuff    这里执行您的业务
			System.out.println("=================================业务执行完成");
		} catch (InterruptedException | JedisException e) {
			log.error(e.getMessage(), e);
		} finally {
			lock.release();
			JedisUtil.close(jedis);
		}

	}

//	public void test2() {
//
//		Jedis jedis = JedisPoolImpl.getResource();
//		JedisDistributedLock lock = new JedisDistributedLock(jedis, "testlock", 15000, 10000);//acquireTimeoutMillis:超过该时间没有获取到锁，直接返回false; expiryTimeMillis:锁过期时间)
//		boolean broken = false;
//		try {
//			lock.acquireEp();//lock.acquire();
//			// do some stuff    这里执行您的业务
//			System.out.println("=================================业务执行完成");
//		} catch (InterruptedException e) {
//			JedisPoolImpl.returnBrokenResource(jedis);//异常时调用returnBrokenResource回收
//			e.printStackTrace();
//		} catch (JedisException jedisException) {
//			jedisException.printStackTrace();
//			broken = JedisUtil.handleJedisException(jedisException);
//			throw new JedisException(jedisException.getMessage());
//		} finally {
//			lock.release();
//			//JedisPoolImpl.returnResource(jedis);//用完调用returnResource回收
//			JedisUtil.returnResource(jedis, broken);
//		}
//
//	}

}
