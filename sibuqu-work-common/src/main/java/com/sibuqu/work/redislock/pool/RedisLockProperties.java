package com.sibuqu.work.redislock.pool;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(
        value = "spring.redis",
        ignoreInvalidFields = true
)
public class RedisLockProperties {
    private String host;
    private Integer port;
    private String password;
    private Integer timeout;
    private Integer maxTotal;
    private Integer maxIdle;
    private Integer minIdle;
    private Integer maxWaitMillis;
    private boolean testOnBorrow;
    private boolean testOnReturn;
}
