package com.sibuqu.work.redislock.lock;

import com.sibuqu.work.redislock.pool.JedisPoolImpl;
import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.Jedis;

import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

@Slf4j
public class LockUtil {

    /**
     * 使用分布式锁 处理事务逻辑 并带有返回值
     */
    public static <V> DistributedLockWithReturnValueBuilder<V> getDistributedLockWithReturnValueBuilder(Supplier<V> getSome) {
        return new DistributedLockWithReturnValueBuilder<>(getSome);
    }

    public static class DistributedLockWithReturnValueBuilder<T> {

        public DistributedLockWithReturnValueBuilder(Supplier<T> getSome) {
            this.getSome = getSome;
        }

        /**
         * 获取数据的锁key
         */
        private String key;
        /**
         * 获取数据的方法
         */
        private final Supplier<T> getSome;
        /**
         * 获得锁失败的异常
         */
        private RuntimeException getLockFailed;
        /**
         * 获得锁失败的默认返回值
         */
        private T lockFailedReturn;
        /**
         * 若redis分布式锁获取时连接中断，可处理异常并返回默认值
         */
        private Function<Exception, T> exceptionHandler;

        public DistributedLockWithReturnValueBuilder<T> lockKey(String key) {
            this.key = key;
            return this;
        }

        /**
         * 获取锁失败 异常处理
         */
        public DistributedLockWithReturnValueBuilder<T> getLockFailedException(RuntimeException getLockFailed) {
            this.getLockFailed = getLockFailed;
            return this;
        }

        /**
         * 获取锁失败 默认返回值
         */
        public DistributedLockWithReturnValueBuilder<T> getLockFailedReturn(T lockFailedReturn) {
            this.lockFailedReturn = lockFailedReturn;
            return this;
        }

        /**
         * 若redis分布式锁获取时连接中断，可处理异常并返回默认值
         */
        public DistributedLockWithReturnValueBuilder<T> exceptionHandler(Function<Exception, T> exceptionHandler) {
            this.exceptionHandler = exceptionHandler;
            return this;
        }

        public T getResult() {
            if (key == null) {
                throw new IllegalArgumentException("key不能为空");
            }
            if (getSome == null) {
                throw new IllegalArgumentException("获取内容的定义不可为空");
            }
            Jedis jedis = JedisPoolImpl.getResource();
            JedisDistributedLock lock = new JedisDistributedLock(jedis, key, 15000, 10000);//acquireTimeoutMillis:超过该时间没有获取到锁，直接返回false; expiryTimeMillis:锁过期时间)
            try {
                if (lock.acquireEp()) {
                    return getSome.get();
                } else {
                    log.error("获取锁失败");
                    if (getLockFailed != null) {
                        throw getLockFailed;
                    }
                    return lockFailedReturn;
                }
            } catch (InterruptedException e) {
                log.error("处理出错");
                if (exceptionHandler != null) {
                    return exceptionHandler.apply(e);
                }
                return null;
            } finally {
                lock.release();
            }
        }
    }

    /**
     * 使用分布式锁 处理事务逻辑 没有返回值
     */
    public static DistributedLockBuilder getDistributedLockBuilder() {
        return new DistributedLockBuilder();
    }

    public static class DistributedLockBuilder {
        /**
         * 获取数据的锁key
         */
        private String key;
        /**
         * 获取数据的方法
         */
        private Executor executor;
        /**
         * 获得锁失败的异常
         */
        private RuntimeException getLockFailed;

        /**
         * 若redis分布式锁获取时连接中断，可处理异常
         */
        private Consumer<Exception> exceptionHandler;

        public DistributedLockBuilder lockKey(String key) {
            this.key = key;
            return this;
        }

        public DistributedLockBuilder businessLogic(Executor executor) {
            this.executor = executor;
            return this;
        }

        public DistributedLockBuilder getLockFailedException(RuntimeException getLockFailed) {
            this.getLockFailed = getLockFailed;
            return this;
        }

        public DistributedLockBuilder exceptionHandler(Consumer<Exception> exceptionHandler) {
            this.exceptionHandler = exceptionHandler;
            return this;
        }

        public void execute() {
            if (key == null) {
                throw new IllegalArgumentException("key不能为空");
            }
            if (executor == null) {
                throw new IllegalArgumentException("执行的逻辑不可为空");
            }
            Jedis jedis = JedisPoolImpl.getResource();
            JedisDistributedLock lock = new JedisDistributedLock(jedis, key, 15000, 10000);//acquireTimeoutMillis:超过该时间没有获取到锁，直接返回false; expiryTimeMillis:锁过期时间)
            try {
                if (lock.acquireEp()) {
                    executor.execute();
                } else {
                    if (getLockFailed != null) {
                        throw getLockFailed;
                    }
                    log.error("获取锁失败");
                }
            } catch (InterruptedException e) {
                if (exceptionHandler != null) {
                    exceptionHandler.accept(e);
                }
                log.error("处理出错");
            } finally {
                lock.release();
            }
        }
    }
    
}
