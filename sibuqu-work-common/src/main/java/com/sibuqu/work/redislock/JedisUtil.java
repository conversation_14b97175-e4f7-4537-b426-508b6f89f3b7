package com.sibuqu.work.redislock;

import com.sibuqu.work.redislock.pool.JedisPoolImpl;
import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.exceptions.JedisConnectionException;
import redis.clients.jedis.exceptions.JedisDataException;
import redis.clients.jedis.exceptions.JedisException;

import java.text.MessageFormat;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * jedis操作工具类
 * @author： zsj
 * @version
 */
@Slf4j
public class JedisUtil {

	public static final long EXCEPTION_VALUE = -9999;

	public static void flushAll() {
		Jedis jedis = null;
		try {
			jedis = getResource();
			jedis.flushAll();
		} catch (JedisException jedisException) {
			log.warn("jedis.incr(key()) execption", jedisException);
		} finally {
			close(jedis);
		}
	}

	public static String set(String key, String value) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			return jedis.set(key, value);
		} catch (JedisException jedisException) {
			return null;
		} finally {
			close(jedis);
		}

	}
	
	public static String setAndExpire(String key, String value) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			int timeOut = jedis.pttl(key).intValue();
			String var = jedis.set(key, value);
			jedis.pexpire(key, timeOut);
			return var;
		} catch (JedisException jedisException) {
			log.warn("jedis.set(key(), value) exception", jedisException);
			return null;
		} finally {
			close(jedis);
		}

	}

	public static String get(String key) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			return jedis.get(key);
		} catch (JedisException jedisException) {
			log.warn("jedis.get(key()) exception", jedisException);
			return null;
		} finally {
			close(jedis);
		}
	}

	public static long del(String... keys) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			return jedis.del(keys);
		} catch (JedisException jedisException) {
			log.warn("jedis.get(key()) exception", jedisException);
			return EXCEPTION_VALUE;
		} finally {
			close(jedis);
		}
	}

	public static boolean exists(String key) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			return jedis.exists(key);
		} catch (JedisException jedisException) {
			log.warn("jedis.exists(key()) execption", jedisException);
			return false;
		} finally {
			close(jedis);
		}

	}
	
	/**
	 * 判断某值是否属于集合的元素  
	 * @param key
	 * @param member
	 * @return
	 */
	public static boolean sismember(String key, String member) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			return jedis.sismember(key, member);
		} catch (JedisException jedisException) {
			log.warn("jedis.sismember(key(),member()) execption", jedisException);
			return false;
		} finally {
			close(jedis);
		}
	}
	
	/**
	 * 向SET集合插入元素(个别重复的元素将被忽略)
	 * 如果执行该命令之前，该Key并不存在，该命令将会创建一个新的Set，此后再将参数中的成员陆续插入。如果该Key的Value不是Set类型，该命令将返回相关的错误信息
	 * @param key
	 * @param members
	 * @return
	 */
	public static Long sadd(final String key, final String... members) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			return jedis.sadd(key, members);
		} catch (JedisException jedisException) {
			log.warn("jedis.sadd(key()) execption", jedisException);
			return 1L;
		} finally {
			close(jedis);
		}
	}
	
	/**
	 * 获取与该Key关联的Set中所有的成员
	 * @param key
	 * @return
	 */
	public static Set<String> smembers(final String key) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			return jedis.smembers(key);
		} catch (JedisException jedisException) {
			log.warn("jedis.sadd(key()) execption", jedisException);
			return null;
		} finally {
			close(jedis);
		}
	}
	
	
	/**
	 * 从与Key关联的Set中删除参数中指定的成员，不存在的参数成员将被忽略，如果该Key并不存在，将视为空Set处理
	 * @param key
	 * @param members
	 * @return
	 */
	public static Long srem(final String key, final String... members) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			return jedis.srem(key, members);
		} catch (JedisException jedisException) {
			log.warn("jedis.srem(key(),members()) execption", jedisException);
			return null;
		} finally {
			close(jedis);
		}
	}
	
	
	
	
	/**
	 * 原子性递增  
	 * @param key
	 * @return
	 */
	public static Long incr(String key) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			return jedis.incr(key);
		} catch (JedisException jedisException) {
			return -1L;
		} finally {
			close(jedis);
		}
	}
	
	/**
	 * 原子性递减
	 * @param key
	 * @return
	 */
	public static Long decr(String key) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			return jedis.decr(key);
		} catch (JedisException jedisException) {
			return -1L;
		} finally {
			close(jedis);
		}
	}
	
	
	/**
	 * 设置某个key的过期时间（单位：秒）, 在超过该时间后，Key被自动的删除。如果该Key在超时之前被修改，与该键关联的超时将被移除。
	 * @param key
	 * @param seconds
	 * @return
	 */
	public static Long expire(final String key, final int seconds) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			return jedis.expire(key, seconds);
		} catch (JedisException jedisException) {
			log.warn("jedis.expire(key(),seconds()) execption", jedisException);
			return -1L;
		} finally {
			close(jedis);
		}
	}
	
	
	/**
	 * 设置某个key的过期时间（单位：毫秒）, 在超过该时间后，Key被自动的删除。如果该Key在超时之前被修改，与该键关联的超时将被移除。
	 * @param key
	 * @param milliseconds
	 * @return
	 */
	public static Long pexpire(final String key, final int milliseconds) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			return jedis.pexpire(key, milliseconds);
		} catch (JedisException jedisException) {
			log.warn("jedis.expire(key(),seconds()) execption", jedisException);
			return -1L;
		} finally {
			close(jedis);
		}
	}
	
	/**
	 * 获取键到期的剩余时间
	 * TTL以毫秒为单位
	 * -1, 如果key没有到期超时
	 * -2, 如果键不存在
	 * @param key
	 * @return
	 */
	public static Long pttl(final String key) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			return jedis.pttl(key);
		} catch (JedisException jedisException) {
			log.warn("jedis.pttl(key()) execption", jedisException);
			return -1L;
		} finally {
			close(jedis);
		}
	}
	
	/**
	 * 获取键到期的剩余时间
	 * TTL以毫秒为单位。
     * -1, 如果key没有到期超时。
	 * -2, 如果键不存在。
	 * @param key
	 * @return
	 */
	public static Long ttl(final String key) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			return jedis.ttl(key);
		} catch (JedisException jedisException) {
			log.warn("jedis.ttl(key()) execption", jedisException);
			return -1L;
		} finally {
			close(jedis);
		}
	}
	
	

	// redis hset operation
	public static String hmset(String key, Map<String, String> hash) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			return jedis.hmset(key, hash);
		} catch (JedisException jedisException) {
			log.warn("jedis.hmset(key, hash) execption", jedisException);
			return null;
		} finally {
			close(jedis);
		}
	}
	
	public static long hdel(String key, final String... fields) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			return jedis.hdel(key, fields);
		} catch (JedisException jedisException) {
			log.warn("jedis.hdel(key, fields) execption", jedisException);
			return EXCEPTION_VALUE;
		} finally {
			close(jedis);
		}
	}
	
	public static List<String> hmget(String key, final String... fields) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			return jedis.hmget(key, fields);
		} catch (JedisException jedisException) {
			log.warn("jedis.hmget(key, fields) execption", jedisException);
			return null;
		} finally {
			close(jedis);
		}
	}

	public static Map<String, String> hgetAll(String key) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			return jedis.hgetAll(key);
		} catch (JedisException jedisException) {

			log.warn("jedis.hgetAll(key) execption", jedisException);
			return null;
		} finally {
			close(jedis);
		}
	}

	public static long hset( String key,  String field,  String value) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			return jedis.hset(key, field, value);
		} catch (JedisException jedisException) {
			log.warn("jedis.hset(key, field, value) execption", jedisException);
			return EXCEPTION_VALUE;
		} finally {
			close(jedis);
		}
	}
	
	public static String hget(String key, String field) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			return jedis.hget(key, field);
		} catch (JedisException jedisException) {
			log.warn("jedis.hget(key, field) execption", jedisException);
			return null;
		} finally {
			close(jedis);
		}
	}

	public static Long hdel(String key, String field) {
		Jedis jedis = null;
		boolean broken = false;
		try {
			jedis = getResource();
			return jedis.hdel(key, field);
		} catch (JedisException jedisException) {
			jedisException.printStackTrace();
			broken = handleJedisException(jedisException);
			log.warn("jedis.hdel(key, field) execption", jedisException);
			return EXCEPTION_VALUE;
		} finally {
			close(jedis);
		}
	}

	public static Long hlen(String key) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			return jedis.hlen(key);
		} catch (JedisException jedisException) {
			log.warn("jedis.hlen(key) execption", jedisException);
			return EXCEPTION_VALUE;
		} finally {
			close(jedis);
		}
	}

	public static Set<String> hkeys(String key) {
		Jedis jedis = null;
		try {
			jedis = getResource();
			return jedis.hkeys(key);
		} catch (JedisException jedisException) {
			log.warn("jedis.hkeys(key) execption", jedisException);
			return null;
		} finally {
			close(jedis);
		}
	}

//	public static final void returnResource(final Jedis jedis) {
//		JedisPoolImpl.returnResource(jedis);
//	}
	
//	/**
//	 * 返回一个redis资源
//	 * @param jedis
//	 * @param
//	 */
//	public static final void returnResource(final Jedis jedis,boolean conectionBroken) {
//		try {
//			if (conectionBroken) {
//				JedisPoolImpl.returnBrokenResource(jedis);
//			}else{
//				JedisPoolImpl.returnResource(jedis);
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//			log.error("return back jedis failed, will fore close the jedis.", e);
//			destroyJedis(jedis);
//		}
//	}
	
	/**
	 * 在Pool以外强行销毁Jedis.
	 */
	public static void destroyJedis(Jedis jedis) {
		if ((jedis != null) && jedis.isConnected()) {
			try {
				try {
					jedis.quit();
				} catch (Exception e) {
				}
				jedis.disconnect();
			} catch (Exception e) {
			}
		}
	}

	public static final Jedis getResource() {
		return JedisPoolImpl.getResource();
	}

//	public static final void returnBrokenResource(final Jedis jedis) {
//		JedisPoolImpl.returnBrokenResource(jedis);
//	}
	
	public static boolean handleJedisException(JedisException jedisException) {
		if (jedisException instanceof JedisConnectionException) {
			log.error("JedisConnectionException", jedisException);
		} else if (jedisException instanceof JedisDataException) {
			if ((jedisException.getMessage() != null) && (jedisException.getMessage().indexOf("READONLY") != -1)) {
				log.error("JedisDataException", jedisException);
			} else {
				// dataException, isBroken=false
				return false;
			}
		} else {
			log.error("Jedis exception happen.", jedisException);
		}
		return true;
	}

	public static final void close(Jedis jedis) {
		if (jedis != null) {
			jedis.close();
			return;
		}
		throw new RuntimeException("jedis is null");
	}
	public static void main(String[] args) {
		/*long incr = JedisUtil.incr("abcdm");
		
		System.out.println(incr);*/
		JedisUtil.exists(MessageFormat.format("esfsed", "1000000005"));
		
	}
}
