package com.sibuqu.work.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.http.client.OkHttp3ClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Collections;

@Component
@RefreshScope
public class RestTemplateConfig {

    @Value("${restTemplate.readTimeout:60000}")
    private int readTimeout;
    @Value("${restTemplate.connectTimeout:10000}")
    private int connectTimeout;
    @Value("${restTemplate.socketTimeout:10000}")
    private int socketTimeout;

    @Resource
    private RestTemplateBuilder builder;
    @Resource
    private TrackInterceptor trackInterceptor;

    @Bean
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = builder.build();
        // 升级为okhttp3
        OkHttp3ClientHttpRequestFactory httpRequestFactory = new OkHttp3ClientHttpRequestFactory();
        httpRequestFactory.setReadTimeout(readTimeout);
        httpRequestFactory.setConnectTimeout(connectTimeout);
        restTemplate.setRequestFactory(httpRequestFactory);
        restTemplate.setInterceptors(Collections.singletonList(trackInterceptor));
        return restTemplate;
    }

}
