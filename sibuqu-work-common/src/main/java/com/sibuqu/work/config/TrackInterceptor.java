package com.sibuqu.work.config;

import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.UUID;

/**
 * resttemplate的自定义拦截器
 * <AUTHOR>
 *
 */
@Component
public class TrackInterceptor implements ClientHttpRequestInterceptor {
	private static String HEADER_REQUEST_ID="requestId";
	@Override
	public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution)
			throws IOException {
		HttpHeaders headers = request.getHeaders();
		//优先从request里取requestId，便于追踪请求
		String requestId=null;
		ServletRequestAttributes servletRequestAttributes =
                (ServletRequestAttributes)RequestContextHolder.getRequestAttributes();
		if(servletRequestAttributes!=null) {
			HttpServletRequest mvcRequest = servletRequestAttributes.getRequest();
			if(mvcRequest!=null) {
				requestId=mvcRequest.getHeader(HEADER_REQUEST_ID);
			}
		}

		if(requestId==null) {
			requestId=UUID.randomUUID().toString();
		}
	    // 加入自定义字段
	    headers.add("requestId", requestId);
	    // 保证请求继续被执行
	    return execution.execute(request, body);
	}

}
