package com.sibuqu.work.config;

import com.sibuqu.base.common.result.HeaderUserInfo;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.paths.AbstractPathProvider;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * <AUTHOR> @Date 2021/10/22 14:44
 * @Description 配置swagger
 */
@Configuration
@EnableSwagger2
public class SwaggerConfig {


    @Bean("作业相关接口")
    public Docket createSysRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("appwork")
                .apiInfo(apiInfo())
                .ignoredParameterTypes(HeaderUserInfo.class)
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.sibuqu.work.controller"))
                .paths(PathSelectors.any())
                .build().pathProvider(new CustRelativePathProvider(""));
    }

    /**
     * 创建该API的基本信息（这些基本信息会展现在文档页面中）
     * 访问地址：http://项目实际地址/swagger-ui.html
     *
     * @return
     */
    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("四部曲 RESTful APIs")
                .description("更多请关注http://www.baidu.com")
                .termsOfServiceUrl("http://www.baidu.com")
                .version("2.0")
                .build();
    }

    public class CustRelativePathProvider extends AbstractPathProvider {
        public static final String ROOT = "/";
        public String prefix = "";

        public CustRelativePathProvider(String prefix) {
            super();
            this.prefix = prefix;
        }

        @Override
        public String getOperationPath(String operationPath) {
            return prefix + operationPath;
        }

        @Override
        protected String applicationPath() {
            return ROOT;
        }

        @Override
        protected String getDocumentationPath() {
            return ROOT;
        }
    }


}
