package com.sibuqu.work.exception;

import com.sibuqu.base.common.exception.BusinessException;
import com.sibuqu.base.common.result.BaseResultCode;
import com.sibuqu.base.common.result.ResultInfo;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;

/**
 * 全局异常处理
 *
 * <AUTHOR> @date 2021年4月12日13:49:46
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    @ExceptionHandler(LogicControlException.class)
    @ResponseStatus(HttpStatus.OK)
    public ResultInfo businessException(LogicControlException ex) {
        log.info("业务判断未通过:{},{}", ex.getCode(), ex.getMessage());
        return ResultInfo.error(ex.getCode(), ex.getMessage());
    }

    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.OK)
    public ResultInfo businessException(BusinessException e) {
        log.error("{}", e.getMessage(), e);
        return ResultInfo.error(e.getCode(), e.getMessage());
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseStatus(HttpStatus.OK)
    public ResultInfo businessException(HttpMessageNotReadableException e) {
        log.error("{}", e.getMessage(), e);
        String[] message = e.getMessage().split("\"");
        if (message.length > 1) {
            return ResultInfo.error(BaseResultCode.ERROR.getCode(), "字段格式错误：" + message[5]);
        } else {
            return ResultInfo.error(BaseResultCode.ERROR.getCode(), "参数格式错误");
        }
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseStatus(HttpStatus.OK)
    public ResultInfo businessException(HttpRequestMethodNotSupportedException e) {
        log.error("{}", e.getMessage(), e);
        return ResultInfo.error("请求方式错误");
    }

    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.OK)
    public ResultInfo exception(Exception e) {
        log.error("{}", e.getMessage(), e);
        return ResultInfo.error(BaseResultCode.ERROR.getCode(), "哎呦，服务出错啦！");
    }


}