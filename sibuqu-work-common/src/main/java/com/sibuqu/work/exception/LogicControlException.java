package com.sibuqu.work.exception;

import com.sibuqu.base.common.exception.BusinessException;
import com.sibuqu.base.common.result.BaseResultCode;

/**
 * 逻辑控制用,方便判断的逻辑从大方法里移出来. 用统一的异常处理器统一返回.
 */
public class LogicControlException extends BusinessException {
    public LogicControlException(String msg) {
        super(msg);
    }

    public LogicControlException(String msg, Throwable cause) {
        super(msg, cause);
    }

    public LogicControlException(int code, String msg) {
        super(code, msg);
    }

    public LogicControlException(BaseResultCode baseResultCode) {
        super(baseResultCode);
    }

    public LogicControlException(int code, String msg, Throwable cause) {
        super(code, msg, cause);
    }
}
