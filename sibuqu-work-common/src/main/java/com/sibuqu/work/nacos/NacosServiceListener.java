package com.sibuqu.work.nacos;

import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import com.alibaba.cloud.nacos.NacosServiceManager;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import com.netflix.loadbalancer.ILoadBalancer;
import com.netflix.loadbalancer.Server;
import com.netflix.loadbalancer.ZoneAwareLoadBalancer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.ribbon.CachingSpringLoadBalancerFactory;
import org.springframework.cloud.openfeign.ribbon.FeignLoadBalancer;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class NacosServiceListener {

    @Autowired
    private CachingSpringLoadBalancerFactory cachingSpringLoadBalancerFactory;
    @Autowired
    private NacosDiscoveryProperties nacosDiscoveryProperties; // 这个配置不能去
    @Autowired
    private NacosServiceManager nacosServiceManager;
    @Autowired
    private ApplicationContext applicationContext;

    @PostConstruct
    public void init() {
        try {
            NamingService namingService = nacosServiceManager.getNamingService();
            Map<String, Object> beans = applicationContext.getBeansWithAnnotation(FeignClient.class);
            HashSet<String> serviceList = new HashSet<>();
            for (Object bean : beans.values()) {
                Class<?> beanClass = bean.getClass();
                Class<?>[] interfaces = beanClass.getInterfaces();
                for (Class<?> face : interfaces) {
                    if (face.isAnnotationPresent(FeignClient.class)) {
                        FeignClient feignClient = face.getAnnotation(FeignClient.class);
                        String name = feignClient.name();
                        if (StringUtils.isBlank(name)) {
                            name = feignClient.value();
                        }

                        if (!StringUtils.isBlank(name)) {
                            serviceList.add(name);
                        }
                    }
                }
            }
            serviceList.forEach(service -> {
                try {
                    namingService.subscribe(service, (event -> {
                        FeignLoadBalancer feignLoadBalancer = cachingSpringLoadBalancerFactory.create(service);
                        if (feignLoadBalancer != null) {
                            ILoadBalancer iLoadBalancer = feignLoadBalancer.getLoadBalancer();
                            ZoneAwareLoadBalancer loadBalancer = (ZoneAwareLoadBalancer) iLoadBalancer;
                            loadBalancer.updateListOfServers();
                            List<Server> servers = loadBalancer.getAllServers();
                            for (Server server : servers) {
                                System.out.println(server.getHost() + ":" + server.getPort());
                            }
                            System.out.println("-----------" + service + "------------");
                        }
                    }));
                } catch (NacosException e) {
                    log.error("订阅nacos服务失败,error:{}", e.getErrMsg());
                }
            });
        } catch (Exception e) {
            log.error("获取nacos服务信息失败,error:{}", e.getMessage());
        }
    }
}