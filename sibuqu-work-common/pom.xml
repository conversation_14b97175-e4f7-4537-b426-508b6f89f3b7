<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>sibuqu-app-work</artifactId>
        <groupId>com.sibuqu</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>sibuqu-work-common</artifactId>

    <dependencies>


        <!--    作业服务module    -->
        <dependency>
            <groupId>com.sibuqu</groupId>
            <artifactId>sibuqu-work-module</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <!--    活动服务module    -->
        <dependency>
            <groupId>com.sibuqu</groupId>
            <artifactId>sibuqu-activity-module</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <!--    公共服务module    -->
        <dependency>
            <groupId>com.sibuqu</groupId>
            <artifactId>sibuqu-common-module</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <!--    订单服务module    -->
        <dependency>
            <groupId>com.sibuqu</groupId>
            <artifactId>sibuqu-order-module</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <!--    用户服务module    -->
        <dependency>
            <groupId>com.sibuqu</groupId>
            <artifactId>sibuqu-user-module</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.sibuqu</groupId>
                    <artifactId>sibuqu-course-module</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sibuqu</groupId>
                    <artifactId>sibuqu-activity-module</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sibuqu</groupId>
                    <artifactId>sibuqu-order-module</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sibuqu</groupId>
                    <artifactId>sibuqu-classes-module</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sibuqu</groupId>
                    <artifactId>sibuqu-prefecture-module</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sibuqu</groupId>
                    <artifactId>sibuqu-common-module</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sibuqu</groupId>
                    <artifactId>sibuqu-meeting-module</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--    课程服务module    -->
        <dependency>
            <groupId>com.sibuqu</groupId>
            <artifactId>sibuqu-course-module</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <!--    班级服务module    -->
        <dependency>
            <groupId>com.sibuqu</groupId>
            <artifactId>sibuqu-classes-module</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.inno</groupId>
            <artifactId>inno-user-module</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <!--    data服务module    -->
        <dependency>
            <groupId>com.sibuqu</groupId>
            <artifactId>sibuqu-bidata-module</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <!--    专区服务module    -->
        <dependency>
            <groupId>com.sibuqu</groupId>
            <artifactId>sibuqu-prefecture-module</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.sibuqu</groupId>
                    <artifactId>sibuqu-course-module</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sibuqu</groupId>
                    <artifactId>sibuqu-activity-module</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sibuqu</groupId>
                    <artifactId>sibuqu-order-module</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sibuqu</groupId>
                    <artifactId>sibuqu-classes-module</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sibuqu</groupId>
                    <artifactId>sibuqu-user-module</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sibuqu</groupId>
                    <artifactId>sibuqu-common-module</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <version>2.2.9.RELEASE</version>
            <exclusions>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
            <version>2.1.3.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <version>2.1.2.RELEASE</version>
            <exclusions>
                <exclusion>
                    <artifactId>archaius-core</artifactId>
                    <groupId>com.netflix.archaius</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.vdurmont</groupId>
            <artifactId>emoji-java</artifactId>
            <version>5.1.1</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>3.9.1</version>
        </dependency>

        <!-- 公共基础包 -->
        <dependency>
            <groupId>com.sibuqu.base</groupId>
            <artifactId>base-common</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>mybatis-plus-boot-starter</artifactId>
                    <groupId>com.baomidou</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis-plus-generator</artifactId>
                    <groupId>com.baomidou</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sibuqu</groupId>
            <artifactId>sibuqu-spring-boot-starter-mongodb</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <!-- 日志 starter -->
        <dependency>
            <groupId>com.sibuqu</groupId>
            <artifactId>sibuqu-spring-boot-starter-logging</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <!-- redis starter -->
        <dependency>
            <groupId>com.sibuqu</groupId>
            <artifactId>sibuqu-spring-boot-starter-redis</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <!-- mysql starter -->
        <dependency>
            <groupId>com.sibuqu</groupId>
            <artifactId>sibuqu-spring-boot-starter-mysql</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <!--    knife4j    -->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-micro-spring-boot-starter</artifactId>
            <version>2.0.8</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.kafka</groupId>
                    <artifactId>kafka-clients</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
            <version>2.4.0</version>
        </dependency>


        <dependency>
            <groupId>cglib</groupId>
            <artifactId>cglib-nodep</artifactId>
            <version>3.2.4</version>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>3.2.0</version>
        </dependency>
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>1.9.3</version>
        </dependency>
        <dependency>
            <groupId>com.sibuqu</groupId>
            <artifactId>sibuqu-spring-boot-starter-mysql-multiple</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
    </dependencies>


</project>
