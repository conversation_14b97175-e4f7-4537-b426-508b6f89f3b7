# 功能:合并分支,前提:当前分支没有未提交的代码.
# 获取当前分支
from_branch=`git rev-parse --abbrev-ref HEAD`
# 指定要合并的分支
to_branch=demo-main

# 在当前分支拉一下代码,保证代码是最新的
git pull
# 切换到要合并的分支
git checkout $to_branch
# 拉一下
git pull
# 合并代码
merge_result=`git merge $from_branch`
# 如果有冲突就回退并停留在当前分支
if [[ $merge_result =~ fail ]]; then
    git merge --abort
    echo 有冲突,已停留在 $to_branch 分支需要手动合并
    exit 0
fi
# 提交代码
git push
# 切换回合并之前的分支
git checkout $from_branch

