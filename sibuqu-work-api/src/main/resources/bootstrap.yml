server:
  port: 11005
spring:
  application:
    name: sibuqu-work-api
  profiles:
    active: sibuqu_local
feign:
  hystrix:
    enabled: true
hystrix:
  command: #指令
    default: #全局的
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 3000 #配置全局超时时长为3秒
  threadPool: # 线程池
    default:
      #默认为10,基本得原则时保持线程池尽可能小，他主要是为了释放压力，防止资源被阻塞
      coreSize: 10
      #BlockingQueue的最大队列数，默认值-1
      maxQueueSize: 1500
      #动态控制线程池队列的上限，即使maxQueueSize没有达到，达到queueSizeRejectionThreshold该值后，请求会被拒绝，默认值5
      queueSizeRejectionThreshold: 1000
ribbon: # ribbon 的超时时间
  readTimeout: 3000
  connectTimeout: 3000

tlog:
  enable-invoke-time-print: true
  pattern: '[$preApp] [$preIp] [$spanId] [$traceId]'

---
# 本地环境
spring:
  profiles: sibuqu_local
  cloud:
    nacos:
      discovery:
        server-addr: mse-54b870b2-nacos-ans.mse.aliyuncs.com
        namespace: dev
      config:
        server-addr: mse-54b870b2-nacos-ans.mse.aliyuncs.com
        namespace: test
        file-extension: yml

---
# 开发外网
spring:
  profiles: dev-domain
  cloud:
    nacos:
      config:
        server-addr: nacos-test.sibuqu.com:80
        #        group: DEFAULT_GROUP
        namespace: 0b09173a-ae3b-4537-9899-389725d0feec
        file-extension: yml
      discovery:
        server-addr: nacos-test.sibuqu.com:80
        namespace: 0b09173a-ae3b-4537-9899-389725d0feec
---

# 测试外网
spring:
  profiles: test-domain
  cloud:
    nacos:
      discovery:
        server-addr: nacos-test.sibuqu.cn:80
        namespace: 0c7531aa-a622-4270-884c-cf842d15bbb1
      config:
        server-addr: nacos-test.sibuqu.cn:80
        namespace: 0c7531aa-a622-4270-884c-cf842d15bbb1
        file-extension: yml
---

# 测试001外网
spring:
  profiles: test001-domain
  cloud:
    nacos:
      discovery:
        server-addr: nacos-test.sibuqu.cn:80
        namespace: 7d007062-09fc-42cf-9c03-0c990a121a31
      config:
        server-addr: nacos-test.sibuqu.cn:80
        namespace: 7d007062-09fc-42cf-9c03-0c990a121a31
        file-extension: yml
---

# 体验外网
spring:
  profiles: tiyan-domain
  cloud:
    nacos:
      discovery:
        server-addr: nacos-test.sibuqu.com:80
        namespace: aae05af8-0be9-4d25-ac00-86cbcdd5ab1e
      config:
        server-addr: nacos-test.sibuqu.com:80
        namespace: aae05af8-0be9-4d25-ac00-86cbcdd5ab1e
        file-extension: yml
---

# 体验001外网
spring:
  profiles: tiyan001-domain
  cloud:
    nacos:
      discovery:
        server-addr: nacos-test.sibuqu.com:80
        namespace: bc0cdfdb-4539-4111-954c-2e00d232122a
      config:
        server-addr: nacos-test.sibuqu.com:80
        namespace: bc0cdfdb-4539-4111-954c-2e00d232122a
        file-extension: yml
---

# 线上外网
spring:
  profiles: prod-domain
  cloud:
    nacos:
      config:
        server-addr: nacos-prod.sibuqu.com:80
        namespace: aae05af8-0be9-4d25-ac00-86cbcdd5ab1e
        file-extension: yml

---
# k8s开发内网
spring:
  profiles: k8s-dev-domain
  cloud:
    nacos:
      config:
        server-addr: nacos-service.kube-system:80
        namespace: 0b09173a-ae3b-4537-9899-389725d0feec
        file-extension: yml
      discovery:
        server-addr: nacos-service.kube-system:80
        namespace: 0b09173a-ae3b-4537-9899-389725d0feec
---

# k8s测试内网
spring:
  profiles: k8s-test-domain
  cloud:
    nacos:
      discovery:
        server-addr: nacos-service.kube-system:80
        namespace: 0c7531aa-a622-4270-884c-cf842d15bbb1
      config:
        server-addr: nacos-service.kube-system:80
        namespace: 0c7531aa-a622-4270-884c-cf842d15bbb1
        file-extension: yml
---

# k8s测试001内网
spring:
  profiles: k8s-test001-domain
  cloud:
    nacos:
      discovery:
        server-addr: nacos-service.kube-system:80
        namespace: 7d007062-09fc-42cf-9c03-0c990a121a31
      config:
        server-addr: nacos-service.kube-system:80
        namespace: 7d007062-09fc-42cf-9c03-0c990a121a31
        file-extension: yml
---

# k8s体验内网
spring:
  profiles: k8s-tiyan-domain
  cloud:
    nacos:
      discovery:
        server-addr: nacos-test.sibuqu.com:80
        namespace: aae05af8-0be9-4d25-ac00-86cbcdd5ab1e
      config:
        server-addr: nacos-test.sibuqu.com:80
        namespace: aae05af8-0be9-4d25-ac00-86cbcdd5ab1e
        file-extension: yml
---

# k8s体验001内网
spring:
  profiles: k8s-tiyan001-domain
  cloud:
    nacos:
      discovery:
        server-addr: nacos-service.kube-system:80
        namespace: bc0cdfdb-4539-4111-954c-2e00d232122a
      config:
        server-addr: nacos-service.kube-system:80
        namespace: bc0cdfdb-4539-4111-954c-2e00d232122a
        file-extension: yml
---

# k8s线上内网
spring:
  profiles: k8s-prod-domain
  cloud:
    nacos:
      discovery:
        server-addr: nacos-service.default:80
        namespace: f7f3da39-7a93-46c2-823f-d9875d441df4
      config:
        server-addr: nacos-service.default:80
        namespace: f7f3da39-7a93-46c2-823f-d9875d441df4
        file-extension: yml