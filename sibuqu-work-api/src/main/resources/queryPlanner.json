{
  "mongosPlannerVersion": new
  NumberInt(
  "1"
  ),
  "winningPlan": {
    "stage": "SINGLE_SHARD",
    "shards": [
      {
        "shardName": "d-2zea9b8537b77894",
        "connectionString": "mgset-81046431/***********:3000,***********:3000,***********:3000,***********:3000",
        "serverInfo": {
          "host": "cn-beijing.i-2zecwahxaf4mll10shuv",
          "port": new
          NumberInt(
          "3000"
          ),
          "version": "4.2.23",
          "gitVersion": "57ebf4541dafaf608b30b7dd857edb245ee3fad8"
        },
        "plannerVersion": new
        NumberInt(
        "1"
        ),
        "namespace": "serverdb_prod.work_info_second",
        "indexFilterSet": false,
        "parsedQuery": {
          "_id": {
            "$eq": new
            NumberInt(
            "1627"
            )
          }
        },
        "winningPlan": {
          "stage": "SHARDING_FILTER",
          "inputStage": {
            "stage": "IDHACK"
          }
        },
        "rejectedPlans": []
      }
    ]
  }
}