package com.sibuqu.work;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.base.common.result.HeaderUserInfo;
import com.sibuqu.work.bo.DoWorkSendKafkaBO;
import com.sibuqu.work.controller.WorkController;
import com.sibuqu.work.dto.*;
import com.sibuqu.work.entity.WorkInfo;
import com.sibuqu.work.enums.PrefectureTypeEnums;
import com.sibuqu.work.enums.ScreeningTypeEnums;
import com.sibuqu.work.mapper.WorkInfoMapper;
import com.sibuqu.work.mongo.WorkInfoPojo;
import com.sibuqu.work.service.WorkService;
import com.sibuqu.work.vo.api.AppWorkInfoListVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 单元测试
 */

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = {ApiApplication.class, WorkTestApplication.class})
public class WorkTestApplication {
    @Autowired
    private WorkService workService;

    @Autowired
    private WorkInfoMapper workInfoMapper;
    @Autowired
    private WorkController workflowController;

    @Autowired
    private MongoTemplate mongoTemplate;
    // 今日作业
    @Test
    public void todayWork() {
        WorkCardInfoDTO workCardInfoDTO = new WorkCardInfoDTO();
        HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setCurrentCompanyId(-1);
        headerUserInfo.setId(1184320);
        workCardInfoDTO.setCourseId(509);
        workService.todayWork(workCardInfoDTO,headerUserInfo);
    }
    @Test
    public void syncDoWork() {
        String str = "{\"classesId\":0,\"companyId\":-1,\"courseId\":412,\"courseTimeTableId\":12670,\"listenStatus\":true,\"score\":0,\"tag\":\"listenedUp\",\"teamId\":0,\"ts\":1678054521,\"userId\":470021,\"workStatus\":false}";
        DoWorkSendKafkaBO doWorkMsgDTO = JSON.parseObject(str, DoWorkSendKafkaBO.class);
        workService.listenAddScore(doWorkMsgDTO);
    }
    //个人版班级情况统计
    @Test
    public void classStatisticsByPerson(){
        ClassStatisticsByPersonDTO classStatisticsDTO = new ClassStatisticsByPersonDTO();
        HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setCurrentCompanyId(-1);
        headerUserInfo.setId(1007624);
        classStatisticsDTO.setCourseId(399);
        classStatisticsDTO.setClassesNo("15956267");
        workService.classStatisticsByPerson(classStatisticsDTO,headerUserInfo);
    }
    //指定日期用户作业情况
    @Test
    public void classWorkingByDate(){
        WorkingByDateDTO workingByDateDTO = new WorkingByDateDTO();
        HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setCurrentCompanyId(36);
        headerUserInfo.setId(1007624);
        workingByDateDTO.setCourseId(464);
        System.out.println(workService.classWorkingByDate(workingByDateDTO, headerUserInfo));
    }

    @Test
    public void recommendWorkList(){
        AppWorkRecommendSearchDTO recommendSearchDTO = new AppWorkRecommendSearchDTO();
        HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setCurrentCompanyId(36);
        headerUserInfo.setId(1007624);
        recommendSearchDTO.setCourseId(399);
        System.out.println(workService.recommendWorkList(recommendSearchDTO, headerUserInfo));
    }

    @Test
    public void companyWorkList(){
        AppWorkHeartPerceptionDTO recommendSearchDTO = new AppWorkHeartPerceptionDTO();
        HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setCurrentCompanyId(36);/**/
        headerUserInfo.setId(1007624);

        recommendSearchDTO.setCourseId(510);
        System.out.println(workService.companyWorkList(recommendSearchDTO, headerUserInfo));
    }
    @Test
    public void testMongo(){

        WorkInfo workInfo = workInfoMapper.selectOne(new QueryWrapper<WorkInfo>().lambda().orderByDesc(WorkInfo::getId).last(" limit 1 "));
        long time = System.currentTimeMillis();
        for (int i = 0 ; i <= workInfo.getId() ; ){
            List<WorkInfoPojo> workInfoPojos = workInfoMapper.selectList(new QueryWrapper<WorkInfo>().lambda()
                    .le(WorkInfo::getId, i + 10000)
                    .gt(WorkInfo::getId, i)
            ).stream().map(work -> {
                WorkInfoPojo workInfoPojo = new WorkInfoPojo();
                BeanUtils.copyProperties(work, workInfoPojo);
                workInfoPojo.setWorkId(work.getId());
                return workInfoPojo;
            }).collect(Collectors.toList());
            mongoTemplate.insertAll(workInfoPojos);
            System.out.println(System.currentTimeMillis() - time);
            time = System.currentTimeMillis();
            i +=50000;
        }
    }
    @Test
    public void testCompanyPrefecture(){
        long t1 = System.currentTimeMillis();
        CompanyPrefectureDTO companyPrefectureDTO = new CompanyPrefectureDTO();
        companyPrefectureDTO.setPrefectureType(PrefectureTypeEnums.RECOMMEND_WORK.getCode());
        companyPrefectureDTO.setScreeningType(ScreeningTypeEnums.OTHER_COMPANY_RECOMMENDED.getCode());
        companyPrefectureDTO.setPageNum(1);
        companyPrefectureDTO.setPageSize(10);
        HeaderUserInfo headerUserInfo = new HeaderUserInfo();
        headerUserInfo.setCurrentCompanyId(-1);
        headerUserInfo.setId(470021);
        PageInfoBT<AppWorkInfoListVO> appWorkInfoListVOPageInfoBT = workService.companyPrefecture(companyPrefectureDTO, headerUserInfo);
        System.out.println(System.currentTimeMillis() -t1);
        System.out.println(appWorkInfoListVOPageInfoBT);
    }
}