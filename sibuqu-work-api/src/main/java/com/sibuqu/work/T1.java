package com.sibuqu.work;

import cn.hutool.core.io.FileUtil;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.BufferedWriter;
import java.io.File;
import java.io.IOException;

public class T1 {

    public static void main(String[] args) throws IOException {

        String path = "/Users/<USER>/Library/Containers/com.tencent.xinWeChat/Data/Library/Application Support/com.tencent.xinWeChat/2.0b4.0.9/6dbad5e987a1244d73e60c60f21c49a8/Message/MessageTemp/94963d1f690df2758a6dc71fbf2e3f65/Image/623721737882386_.pic_thumb.jpg";
//        path = "/Users/<USER>/temp/t3.png";
        createAsciiPic(path, "/Users/<USER>/temp/thumb.txt");
    }

    public static void createAsciiPic(final String path,String outPath) throws IOException {
        final String base = "DBSKdbsk,.";// 字符串由复杂到简单
        StringBuilder builder = new StringBuilder();
        int prev = 0;
        try {
            final BufferedImage image = ImageIO.read(new File(path));
            for (int y = 0; y < image.getHeight(); y += 2) {
                for (int x = 0; x < image.getWidth(); x++) {
                    final int pixel = image.getRGB(x, y);
                    final int r = (pixel & 0xff0000) >> 16, g = (pixel & 0xff00) >> 8, b = pixel & 0xff;
                    final float gray = 0.299f * r + 0.578f * g + 0.114f * b;
                    int index = Math.round(gray * (base.length() + 1) / 255);
                    if (index == 1 || index == 5) {
                        if (prev == 2 || prev == 6) {
                            index = 4;
                        }
                    }
                    prev = index;
                    System.out.print(index >= base.length() ? " " : String.valueOf(base.charAt(index)));
                    builder.append(index >= base.length() ? " " : String.valueOf(base.charAt(index)));
                }
                builder.append("\n");
                System.out.println();
            }
        } catch (final IOException e) {
            e.printStackTrace();
        }
        BufferedWriter bw = FileUtil.getWriter(outPath, "utf-8", false);
        bw.write(builder.toString());
        bw.close();
    }


}
