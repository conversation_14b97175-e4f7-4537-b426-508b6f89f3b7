package com.sibuqu.work.controller;

import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.work.dto.AddInnoWeeklyConfigDTO;
import com.sibuqu.work.dto.BatchQueryInnoCttStatusDTO;
import com.sibuqu.work.dto.EditInnoWeeklyInfoDTO;
import com.sibuqu.work.dto.QueryByCourseIdAndTimeDTO;
import com.sibuqu.work.dto.AdminQueryInnoWorkPageDTO;
import com.sibuqu.work.dto.InnoWorkRecommendDTO;
import com.sibuqu.work.dto.QueryInnoInfoByCourseIdAndCttIdDTO;
import com.sibuqu.work.dto.QueryInnoPosterPageDTO;
import com.sibuqu.work.dto.QueryInnoWeeklyConfigDTO;
import com.sibuqu.work.dto.QueryInnoWeeklyInfoDTO;
import com.sibuqu.work.dto.QueryInnoWeeklyInfoDetailDTO;
import com.sibuqu.work.dto.QueryInnoWeeklyInfoListDTO;
import com.sibuqu.work.dto.QueryInnoWeeklyInfoPageDTO;
import com.sibuqu.work.dto.QueryInnoWeeklyInfoSelectDTO;
import com.sibuqu.work.dto.QueryInnoWorkDTO;
import com.sibuqu.work.dto.WriteInnoWorkDTO;
import com.sibuqu.work.service.WorkInnoInfoService;
import com.sibuqu.work.service.WorkInnoWeeklyConfigService;
import com.sibuqu.work.service.WorkInnoWeeklyInfoService;
import com.sibuqu.work.vo.AdminInnoWorkPageVO;
import com.sibuqu.work.vo.InnoWeeklyConfigVO;
import com.sibuqu.work.vo.InnoWeeklyInfoDetailVO;
import com.sibuqu.work.vo.InnoWeeklyInfoPageVO;
import com.sibuqu.work.vo.InnoPosterPageVO;
import com.sibuqu.work.vo.InnoWeeklyInfoSelectVO;
import com.sibuqu.work.vo.InnoWeeklyInfoVO;
import com.sibuqu.work.vo.QueryInnoWeeklyInfoByIdListVO;
import com.sibuqu.work.vo.QueryInnoWeeklyInfoListVO;
import com.sibuqu.work.vo.WorkInnoInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import lombok.extern.slf4j.Slf4j;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@Slf4j
@Api(tags = "inno 作业表")
@RestController
@RequestMapping("/appwork/work_inno_info")
public class WorkInnoInfoController {

    @Autowired
    private WorkInnoInfoService workInnoInfoService;

    @Autowired
    private WorkInnoWeeklyConfigService workInnoWeeklyConfigService;

    @Autowired
    private WorkInnoWeeklyInfoService workInnoWeeklyInfoService;

    @ApiOperation(value = "inno 日作业写作业")
    @PostMapping("/writeInnoWork")
    public ResultInfo<Long> writeInnoWork(@RequestBody WriteInnoWorkDTO dto) {
        return ResultInfo.ok(workInnoInfoService.writeInnoWork(dto));
    }

    @ApiOperation(value = "inno 日作业查询作业")
    @PostMapping("/queryInnoWork")
    public ResultInfo<WorkInnoInfoVO> queryInnoWork(@RequestBody QueryInnoWorkDTO dto) {
        return ResultInfo.ok(workInnoInfoService.queryInnoWork(dto));
    }

    @ApiOperation(value = "批量查询inno课件日收获状态接口")
    @PostMapping("/batchQueryInnoCttStatus")
    public ResultInfo<Map<Integer, Boolean>> batchQueryInnoCttStatus(@RequestBody BatchQueryInnoCttStatusDTO dto) {
        return ResultInfo.ok(workInnoInfoService.batchQueryInnoCttStatus(dto));
    }

    @ApiOperation(value = "查询课程周作业列表接口")
    @PostMapping("/queryInnoWeeklyInfoList")
    public ResultInfo<List<QueryInnoWeeklyInfoListVO>> queryInnoWeeklyInfoList(
            @RequestBody QueryInnoWeeklyInfoListDTO dto) {
        return ResultInfo.ok(workInnoWeeklyInfoService.queryInnoWeeklyInfoList(dto));
    }

    @ApiOperation(value = "查询周作业单条信息")
    @PostMapping("/queryInnoWeeklyInfo")
    public ResultInfo<InnoWeeklyInfoVO> queryInnoWeeklyInfo(@Valid @RequestBody QueryInnoWeeklyInfoDTO dto) {
        return ResultInfo.ok(workInnoWeeklyInfoService.queryInnoWeeklyInfo(dto));
    }

    @ApiOperation(value = "管理后台-inno周作业配置列表")
    @PostMapping("/queryInnoWeeklyConfigList")
    public ResultInfo<List<InnoWeeklyConfigVO>> queryInnoWeeklyConfigList() {
        return ResultInfo.ok(workInnoWeeklyConfigService.queryInnoWeeklyConfigList());
    }

    @ApiOperation(value = "管理后台-新增inno周作业配置")
    @PostMapping("/addInnoWeeklyConfig")
    public ResultInfo<Long> addInnoWeeklyConfig(@Valid @RequestBody AddInnoWeeklyConfigDTO dto) {
        return ResultInfo.ok(workInnoWeeklyConfigService.addInnoWeeklyConfig(dto));
    }

    @ApiOperation(value = "管理后台-查询inno周作业配置详情")
    @PostMapping("/queryInnoWeeklyConfig")
    public ResultInfo<InnoWeeklyConfigVO> queryInnoWeeklyConfig(@Valid @RequestBody QueryInnoWeeklyConfigDTO dto) {
        return ResultInfo.ok(workInnoWeeklyConfigService.queryInnoWeeklyConfig(dto));
    }

    @ApiOperation(value = "管理后台-分页查询inno周作业信息")
    @PostMapping("/queryInnoWeeklyInfoPage")
    public ResultInfo<PageInfoBT<InnoWeeklyInfoPageVO>> queryInnoWeeklyInfoPage(
            @Valid @RequestBody QueryInnoWeeklyInfoPageDTO dto) {
        return ResultInfo.ok(workInnoWeeklyInfoService.queryInnoWeeklyInfoPage(dto));
    }

    @ApiOperation(value = "管理后台-查询inno周作业详情")
    @PostMapping("/queryInnoWeeklyInfoDetail")
    public ResultInfo<InnoWeeklyInfoDetailVO> queryInnoWeeklyInfoDetail(
            @Valid @RequestBody QueryInnoWeeklyInfoDetailDTO dto) {
        return ResultInfo.ok(workInnoWeeklyInfoService.queryInnoWeeklyInfoDetail(dto));
    }

    @ApiOperation(value = "管理后台-编辑inno周作业信息")
    @PostMapping("/editInnoWeeklyInfo")
    public ResultInfo<Boolean> editInnoWeeklyInfo(@Valid @RequestBody EditInnoWeeklyInfoDTO dto) {
        return ResultInfo.ok(workInnoWeeklyInfoService.editInnoWeeklyInfo(dto));
    }

    @ApiOperation(value = "管理后台-分页查询inno周作业下拉列表")
    @PostMapping("/queryInnoWeeklyInfoSelect")
    public ResultInfo<List<InnoWeeklyInfoSelectVO>> queryInnoWeeklyInfoSelect(
            @Valid @RequestBody QueryInnoWeeklyInfoSelectDTO dto) {
        return ResultInfo.ok(workInnoWeeklyInfoService.queryInnoWeeklyInfoSelect(dto));
    }

    @ApiOperation(value = "根据周作业 id 列表查询周作业信息")
    @PostMapping("/queryInnoWeeklyInfoByIdList")
    public ResultInfo<Map<Long, QueryInnoWeeklyInfoByIdListVO>> queryInnoWeeklyInfoByIdList(@RequestBody List<Long> idList) {
        return ResultInfo.ok(workInnoWeeklyInfoService.queryInnoWeeklyInfoByIdList(idList));
    }

    @ApiOperation(value = "根据课程id课件id查询有能量果的数量")
    @PostMapping("/queryInnoInfoByCourseIdAndCttId")
    public ResultInfo<Integer> queryInnoInfoByCourseIdAndCttId(@RequestBody QueryInnoInfoByCourseIdAndCttIdDTO dto) {
        return ResultInfo.ok(workInnoInfoService.queryInnoInfoByCourseIdAndCttId(dto));
    }

    @ApiOperation(value = "根据课程 id课件 id 列表查询能量果数量")
    @PostMapping("/queryByCourseIdAndCttIdList")
    public ResultInfo<Map<Integer, Long>> queryByCourseIdAndCttIdList(@RequestParam Integer courseId,
            @RequestParam List<Integer> cttIdList, @RequestParam List<Integer> userIdList) {
        return ResultInfo.ok(workInnoInfoService.queryByCourseIdAndCttIdList(courseId, cttIdList, userIdList));
    }

    @ApiOperation(value = "根据课程 id 查询周作业信息")
    @PostMapping("/queryWeeklyInfoByCourseId")
    public ResultInfo<List<InnoWeeklyInfoVO>> queryWeeklyInfoByCourseId(@RequestParam Integer courseId) {
        return ResultInfo.ok(workInnoWeeklyInfoService.queryWeeklyInfoByCourseId(courseId));
    }

    @ApiOperation(value = "根据课程 id 开始时间结束时间查询周作业")
    @PostMapping("/queryByCourseIdAndTime")
    public ResultInfo<List<InnoWeeklyInfoVO>> queryByCourseIdAndTime(@RequestBody QueryByCourseIdAndTimeDTO dto) {
        return ResultInfo.ok(workInnoWeeklyInfoService.queryByCourseIdAndTime(dto));
    }

    @ApiOperation(value = "内部接口-查询当前时间范围内的周作业")
    @PostMapping("/queryCurrentTimeWeeklyInfo")
    public ResultInfo<QueryInnoWeeklyInfoListVO> queryCurrentTimeWeeklyInfo(@RequestParam("userId") Integer userId, @RequestParam("courseId") Integer courseId) {
        return ResultInfo.ok(workInnoWeeklyInfoService.queryCurrentTimeWeeklyInfo(userId, courseId));
    }

    @ApiOperation(value = "分页查询inno海报")
    @PostMapping("/queryInnoPosterPage")
    public ResultInfo<PageInfoBT<InnoPosterPageVO>> queryInnoPosterPage(@Valid @RequestBody QueryInnoPosterPageDTO dto) {
        return ResultInfo.ok(workInnoInfoService.queryInnoPosterPage(dto));
    }

    // 管理后台

    @ApiOperation(value = "管理后台分页查询inno作业")
    @PostMapping("/admin/queryInnoWorkPage")
    public ResultInfo<PageInfoBT<AdminInnoWorkPageVO>> adminQueryInnoWorkPage(@Valid @RequestBody AdminQueryInnoWorkPageDTO dto) {
        return ResultInfo.ok(workInnoInfoService.adminQueryInnoWorkPage(dto));
    }

    @ApiOperation(value = "管理后台推荐/取消推荐inno作业")
    @PostMapping("/admin/recommendInnoWork")
    public ResultInfo<Boolean> adminRecommendInnoWork(@Valid @RequestBody InnoWorkRecommendDTO dto) {
        return ResultInfo.ok(workInnoInfoService.adminRecommendInnoWork(dto));
    }

}
