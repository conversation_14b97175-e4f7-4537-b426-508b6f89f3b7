package com.sibuqu.work.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.base.common.result.HeaderUserInfo;
import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.base.common.utils.ExcelUtils;
import com.sibuqu.work.annation.RequestHeaderUser;
import com.sibuqu.work.dto.*;
import com.sibuqu.work.service.WorkService;
import com.sibuqu.work.service.WorkStatisticsService;
import com.sibuqu.work.service.WorkTeacherReplyService;
import com.sibuqu.work.vo.admin.*;
import com.sibuqu.work.vo.api.WorkStatusInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.sibuqu.base.common.result.ResultInfo.ok;

/**
 * SAAS-作业相关统计"
 */
@Api(value = "SAAS-作业相关统计", tags = {"SAAS-作业相关统计"})
@RequestMapping("/appwork/manage/v1/work/")
@RestController
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class WorkManageController {
    private final WorkService workService;
    private final WorkTeacherReplyService workTeacherReplyService;

    /**
     * 作业列表
     *
     * @param userWorkListDTO
     * @return
     */
    @ApiOperation(value = "作业列表")
    @PostMapping("workList")
    public ResultInfo<PageInfoBT<WorkListVO>> workList(@RequestBody UserWorkListDTO userWorkListDTO) {
        return ok(workService.workList(userWorkListDTO));
    }

    /**
     * 作业详情
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "作业详情")
    @GetMapping("workInfo")
    public ResultInfo<WorkInfoVO> workInfo(@RequestParam("id") Integer id) {
        return ok(workService.workInfo(id));
    }

    @ApiOperation(value = "作业列表导出")
    @PostMapping("workListExport")
    public void workListExport(@RequestBody UserWorkListDTO userWorkListDTO, HttpServletResponse response) {
        List<WorkListVO> workInfoVoList = workService.workListExport(userWorkListDTO);
        List<UserWorkListExportVO> userWorkListExportVoList = BeanUtil.copyToList(workInfoVoList, UserWorkListExportVO.class);
        ExcelUtils.exportExcel(userWorkListExportVoList, "作业列表", "作业列表", UserWorkListExportVO.class, "作业列表.xls", response);
    }

    @ApiOperation(value = "推荐/取消推荐 作业")
    @GetMapping("recommendAndCancelWork")
    public ResultInfo recommendAndCancelWork(@RequestParam("id") Integer id, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
        workService.recommendAndCancelWork(id,headerUserInfo);
        return ok();
    }

    @ApiOperation(value = "修补作业分")
    @PostMapping("repairScore")
    public ResultInfo repairScore(@RequestBody RepairScoreDTO repairScoreDTO){

        workService.repairScore(repairScoreDTO);
        return ok();
    }
    @ApiOperation(value = "作业隐藏显示")
    @GetMapping("workHide")
    public ResultInfo workHide(@RequestParam("workId") Integer workId) {
        workService.workHide(workId);
        return ok();
    }

    @ApiOperation(value = "作业回复列表")
    @PostMapping("workReplyPage")
    public ResultInfo<PageInfoBT<WorkTeacherReplyVO>> workReplyPage(@RequestBody WorkReplyPageDTO dto) {
        return ResultInfo.ok(workTeacherReplyService.workReplyPage(dto));
    }

    @ApiOperation(value = "作业回复列表导出")
    @PostMapping("workReplyPageExport")
    public void workReplyPageExport(@RequestBody WorkReplyPageDTO dto, HttpServletResponse response) {
        List<WorkTeacherReplyVO> workInfoVoList = workTeacherReplyService.workReplyPageExport(dto);
        List<WorkTeacherReplyExportVO> userWorkListExportVoList = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(workInfoVoList)){
            //从vo转换为model
            userWorkListExportVoList = workInfoVoList.stream()
                    .map(WorkTeacherReplyExportVO::vo2ExportModel)
                    .collect(Collectors.toList());
        }
        ExcelUtils.exportExcel(userWorkListExportVoList, "作业回复列表", "作业回复列表", WorkTeacherReplyExportVO.class, "作业回复列表.xls", response);
    }

    @ApiOperation(value = "作业回复隐藏显示")
    @GetMapping("workReplyHideShow")
    public ResultInfo workReplyHideShow(@RequestParam("id") Integer id) {
        workTeacherReplyService.workReplyHideShow(id);
        return ok();
    }

    @ApiOperation(value = "后台添加作业回复")
    @PostMapping("addWorkReply")
    public ResultInfo addWorkReply(@RequestBody AddWorkTeacherReplyDTO dto) {
        return ResultInfo.ok(workTeacherReplyService.addWorkReply(dto));
    }
}
