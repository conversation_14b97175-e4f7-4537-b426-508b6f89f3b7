package com.sibuqu.work.controller;

import com.sibuqu.work.service.WorkOldUserInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(value = "新老学员", tags = {"新老学员"})
@RequestMapping("/appwork/workOldUser")
@RestController
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class WorkOldUserInfoController {

    private final WorkOldUserInfoService workOldUserInfoService;

    @ApiOperation(value = "校验学员是否是老学员")
    @GetMapping("/checkUserIsOld")
    public boolean checkUserIsOld(@RequestParam("userId") Integer userId) {
        return workOldUserInfoService.checkUserIsOld(userId);
    }
}
