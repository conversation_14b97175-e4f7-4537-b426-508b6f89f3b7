package com.sibuqu.work.controller;

import com.sibuqu.base.common.exception.BusinessException;
import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.base.common.result.BaseResultCode;
import com.sibuqu.base.common.result.HeaderUserInfo;
import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.work.annation.RequestHeaderUser;
import com.sibuqu.work.dto.DeleteModelDetailDTO;
import com.sibuqu.work.dto.WorkModelAddDTO;
import com.sibuqu.work.dto.WorkModelDetailAddDTO;
import com.sibuqu.work.dto.WorkModelDetailUpdateDTO;
import com.sibuqu.work.dto.WorkModelListDTO;
import com.sibuqu.work.dto.WorkModelUpdateDTO;
import com.sibuqu.work.service.WorkModelManageService;
import com.sibuqu.work.vo.AudioConfigVO;
import com.sibuqu.work.vo.admin.ModelDetailInfoVO;
import com.sibuqu.work.vo.admin.ModelDetailVO;
import com.sibuqu.work.vo.admin.WorkModelInfoVO;
import com.sibuqu.work.vo.admin.WorkModelListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

import static com.sibuqu.base.common.result.ResultInfo.ok;

@Api(value = "作业模版管理", tags = {"作业模版管理"})
@RestController
@RequestMapping("/appwork/manage/v1/work/model")
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class WorkModelController {
    private final WorkModelManageService workModelManageService;

    @ApiOperation(value = "作业模版列表")
    @PostMapping("list")
    public ResultInfo<PageInfoBT<WorkModelListVO>> list(@RequestBody WorkModelListDTO workModelListDTO) {
        return ok(workModelManageService.list(workModelListDTO));
    }

    @ApiOperation(value = "作业模版信息")
    @GetMapping("info")
    public ResultInfo<WorkModelInfoVO> info(@RequestParam("workModelId") Integer workModelId) {
        return ok(workModelManageService.info(workModelId));
    }

    @ApiOperation(value = "作业项列表")
    @GetMapping("detail")
    public ResultInfo<List<ModelDetailVO>> modelDetail(@RequestParam("workModelId") Integer workModelId) {
        return ok(workModelManageService.modelDetail(workModelId));
    }

    @ApiOperation(value = "创建作业模版")
    @PostMapping("addModel")
    public ResultInfo add(@RequestBody WorkModelAddDTO workModelAddDTO, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
        if (Objects.isNull(headerUserInfo)) {
            throw new BusinessException(BaseResultCode.E_11012);
        }
        workModelManageService.addModel(workModelAddDTO, headerUserInfo);
        return ok();
    }

    @ApiOperation(value = "编辑作业模版")
    @PostMapping("updateModel")
    public ResultInfo update(@RequestBody WorkModelUpdateDTO workModelUpdateDTO, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
        if (Objects.isNull(headerUserInfo)) {
            throw new BusinessException(BaseResultCode.E_11012);
        }
        workModelManageService.update(workModelUpdateDTO, headerUserInfo);
        return ok();
    }

    @ApiOperation(value = "创建作业项")
    @PostMapping("addModelDetail")
    public ResultInfo addModelDetail(@RequestBody WorkModelDetailAddDTO workModelDetailDTO, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
        if (Objects.isNull(headerUserInfo)) {
            throw new BusinessException(BaseResultCode.E_11012);
        }
        workModelManageService.addModelDetail(workModelDetailDTO, headerUserInfo);
        return ok();
    }

    @ApiOperation(value = "编辑作业项")
    @PostMapping("updateModelDetail")
    public ResultInfo updateModelDetail(@RequestBody WorkModelDetailUpdateDTO workModelDetailUpdateDTO, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
        if (Objects.isNull(headerUserInfo)) {
            throw new BusinessException(BaseResultCode.E_11012);
        }
        workModelManageService.updateModelDetail(workModelDetailUpdateDTO, headerUserInfo);
        return ok();
    }

    @ApiOperation(value = "作业项详情")
    @GetMapping("modelDetailInfo")
    public ResultInfo<ModelDetailInfoVO> modelDetailInfo(@RequestParam("modelDetailId") Integer modelDetailId, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
        if (Objects.isNull(headerUserInfo)) {
            throw new BusinessException(BaseResultCode.E_11012);
        }
        return ok(workModelManageService.modelDetailInfo(modelDetailId, headerUserInfo));
    }

    @ApiOperation(value = "删除作业项")
    @PostMapping("deleteModelDetail")
    public ResultInfo deleteModelDetail(@RequestBody DeleteModelDetailDTO deleteModelDetailDTO, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
        if (Objects.isNull(headerUserInfo)) {
            throw new BusinessException(BaseResultCode.E_11012);
        }
        workModelManageService.deleteModelDetail(deleteModelDetailDTO, headerUserInfo);
        return ok();
    }

    @ApiOperation(value = "语音项配置")
    @PostMapping("audioConfig")
    public ResultInfo<AudioConfigVO> audioConfig() {
        AudioConfigVO vo = workModelManageService.audioConfig();
        return ok(vo);
    }

}
