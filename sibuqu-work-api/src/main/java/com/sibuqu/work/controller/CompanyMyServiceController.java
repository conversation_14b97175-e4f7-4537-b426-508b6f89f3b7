package com.sibuqu.work.controller;

import com.sibuqu.base.common.result.HeaderUserInfo;
import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.work.annation.RequestHeaderUser;
import com.sibuqu.work.service.WorkTeacherInfoService;
import com.sibuqu.work.vo.api.CompanyMyServiceVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.List;

import static com.sibuqu.base.common.result.ResultInfo.ok;

@Api(tags = {"企业我的"})
@RestController
@RequestMapping("/appwork/")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CompanyMyServiceController {
    private final WorkTeacherInfoService workerInfoService;

    @ApiOperation(value = "我的服务")
    @GetMapping("companyMyServiceList")
    public Mono<ResultInfo<List<CompanyMyServiceVO>>> companyMyServiceList(@RequestHeaderUser HeaderUserInfo headerUserInfo){
        return Mono.just(headerUserInfo).map(workerInfoService::companyMyServiceList).map(ResultInfo::ok);
    }

}
