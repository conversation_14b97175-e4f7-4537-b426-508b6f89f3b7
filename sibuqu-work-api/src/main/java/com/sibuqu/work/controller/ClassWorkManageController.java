package com.sibuqu.work.controller;

import cn.hutool.core.bean.BeanUtil;
import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.base.common.utils.ExcelUtils;
import com.sibuqu.user.vo.course.CxktResDateListVO;
import com.sibuqu.work.dto.ClassWorkInfoDTO;
import com.sibuqu.work.dto.ClassWorkStatisticsDTO;
import com.sibuqu.work.dto.ClassWorkStatisticsInfoDTO;
import com.sibuqu.work.service.WorkStatisticsService;
import com.sibuqu.work.vo.admin.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

import static com.sibuqu.base.common.result.ResultInfo.ok;

@Api(value = "班级作业入口", tags = {"班级作业入口"})
@RequestMapping("/appwork/manage/v1/work/info/")
@RestController
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class ClassWorkManageController {

    private final WorkStatisticsService workStatisticsService;

    @ApiOperation(value = "班级数据列表")
    @PostMapping("selectClassWorkList")
    public ResultInfo<PageInfoBT<ClassWorkStatisticsListVO>> selectClassWorkList(@RequestBody ClassWorkStatisticsDTO classWorkStatisticsDTO) {
        return ok(workStatisticsService.selectClassWorkList(classWorkStatisticsDTO));
    }

    @ApiOperation(value = "班级数据导出")
    @PostMapping("classWorkListExport")
    public void workListExport(@RequestBody ClassWorkStatisticsDTO classWorkStatisticsDTO, HttpServletResponse response) {
        PageInfoBT<ClassWorkStatisticsListVO> classWorkStatisticsListVOPageInfoBT = workStatisticsService.selectClassWorkList(classWorkStatisticsDTO);
        List<ClassWorkStatisticsListVO> listVOList = classWorkStatisticsListVOPageInfoBT.getRecords();
        List<ClassWorkStatisticsExportVO> userWorkListExportVoList = BeanUtil.copyToList(listVOList, ClassWorkStatisticsExportVO.class);
        ExcelUtils.exportExcel(userWorkListExportVoList, "作业列表", "作业列表", ClassWorkStatisticsExportVO.class, "作业列表.xls", response);
    }


    @ApiOperation(value = "班级信息")
    @PostMapping("selectClassInfo")
    public ResultInfo<ClassWorkInfoVO> selectClassInfo(@RequestBody ClassWorkInfoDTO classWorkInfoDTO) {
        return ok(workStatisticsService.selectClassInfo(classWorkInfoDTO));
    }

    @ApiOperation(value = "查询文本表头信息")
    @GetMapping("selectTextInfo")
    public ResultInfo<List<WorkModelDetailVO>> selectTextInfo(@RequestParam Integer workModelId) {
        return ok(workStatisticsService.selectTextInfo(workModelId));
    }

    @ApiOperation(value = "班级作业详情")
    @PostMapping("selectClassWorkInfoList")
    public ResultInfo<PageInfoBT<ClassWorkStatisticsInfoVO>> selectClassWorkInfoList(@RequestBody ClassWorkStatisticsInfoDTO classWorkStatisticsInfoDTO) {
        return workStatisticsService.selectClassWorkInfoList(classWorkStatisticsInfoDTO);
    }

    @ApiOperation(value = "班级作业详情导出")
    @PostMapping("classWorkInfoListExport")
    public void classWorkInfoListExport(@RequestBody ClassWorkStatisticsInfoDTO classWorkStatisticsInfoDTO, HttpServletResponse response) {
        workStatisticsService.classWorkInfoListExport(classWorkStatisticsInfoDTO, response);
    }


    @ApiOperation(value = "根据课程ID获取课件信息")
    @GetMapping("queryCxktResDataByCourseId/{courseId}")
    public ResultInfo<List<CxktResDateListVO>> queryCxktResDataByCourseId(@PathVariable Integer courseId) {
        return ok(workStatisticsService.queryCxktResDataByCourseId(courseId));
    }

}

