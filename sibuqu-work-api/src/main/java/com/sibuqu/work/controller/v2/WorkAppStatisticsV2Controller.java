//package com.sibuqu.work.controller.v2;
//
//import com.sibuqu.base.common.exception.BusinessException;
//import com.sibuqu.base.common.page.PageInfoBT;
//import com.sibuqu.base.common.result.BaseResultCode;
//import com.sibuqu.base.common.result.HeaderUserInfo;
//import com.sibuqu.base.common.result.ResultInfo;
//import com.sibuqu.work.annation.RequestHeaderUser;
//import com.sibuqu.work.dto.*;
//import com.sibuqu.work.service.v2.WorkV2Service;
//import com.sibuqu.work.vo.api.*;
//import com.sibuqu.work.webclient.model.vo.*;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import lombok.RequiredArgsConstructor;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//import java.util.Objects;
//import java.util.concurrent.ExecutionException;
//
//import static com.sibuqu.base.common.result.ResultInfo.ok;
//
///**
// * APP-作业相关统计
// */
//@Api(value = "【四部曲APP】作业相关统计", tags = {"四部曲APP】作业相关统计"})
//@RequestMapping("/appwork/app/v2/work/statistics/")
//@RestController
//@RequiredArgsConstructor(onConstructor_ = @Autowired)
//public class WorkAppStatisticsV2Controller {
//    private final WorkV2Service workV2Service;
//
//    /**
//     * 班级作业情况统计
//     *
//     * @param classStatisticsDTO
//     * @param headerUserInfo
//     * @return
//     */
//    @ApiOperation(value = "企业版 - 班级作业情况统计")
//    @PostMapping("classStatistics")
//    public ResultInfo<ClassStatisticsVO> classStatistics(@RequestBody ClassStatisticsDTO classStatisticsDTO, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        if (Objects.isNull(headerUserInfo)){
//            throw new BusinessException(BaseResultCode.E_11012);
//        }
//        return ok(workV2Service.classStatistics(classStatisticsDTO, headerUserInfo));
//    }
//
//    @ApiOperation(value = "个人版 - 班级作业情况统计")
//    @PostMapping("classStatisticsByPerson")
//    public ResultInfo<ClassHworkStatisticsVO> classStatisticsByPerson(@RequestBody ClassStatisticsDTO classStatisticsDTO, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        if (Objects.isNull(headerUserInfo)){
//            throw new BusinessException(BaseResultCode.E_11012);
//        }
//        return ok(workV2Service.classStatisticsByPerson(classStatisticsDTO, headerUserInfo));
//    }
//
//    /**
//     * 获取指定日期班级成员作业情况
//     *
//     * @param workingByDateDTO
//     * @return
//     */
//    @ApiOperation(value = "企业版 - 获取指定日期班级成员作业情况")
//    @PostMapping("classWorkingByDate")
//    public ResultInfo<List<WorkingByDateVO>> classWorkingByDate(@RequestBody WorkingByDateDTO workingByDateDTO, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        if (Objects.isNull(headerUserInfo)){
//            throw new BusinessException(BaseResultCode.E_11012);
//        }
//        return ok(workV2Service.classWorkingByDate(workingByDateDTO, headerUserInfo));
//    }
//    /**
//     * 获取指定日期班级成员作业情况
//     *
//     * @param workingByDateDTO
//     * @return
//     */
//    @ApiOperation(value = "企业版 - 获取指定日期班级成员作业情况分页")
//    @PostMapping("classWorkingByDatePage")
//    public ResultInfo<PageInfoBT<WorkingByDateVO>> classWorkingByDatePage(@RequestBody WorkingByDateDTO workingByDateDTO, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        if (Objects.isNull(headerUserInfo)){
//            throw new BusinessException(BaseResultCode.E_11012);
//        }
//        return ok(workV2Service.classWorkingByDatePage(workingByDateDTO, headerUserInfo));
//    }
//    @ApiOperation(value = "个人版 - 今日和指定日期学习情况")
//    @PostMapping("todayWorking")
//    public ResultInfo<PageInfoBT<ToDayWorkVO>> todayWorking(@RequestBody WorkingByDateDTO workingByDateDTO, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        if (Objects.isNull(headerUserInfo)){
//            throw new BusinessException(BaseResultCode.E_11012);
//        }
//        return ok(workV2Service.todayWorkingV2(workingByDateDTO, headerUserInfo));
//    }
//    /**
//     * 企业版- 小组学习情况统计
//     *
//     * @param teamStatisticsDTO
//     * @return
//     */
//    @ApiOperation(value = "企业版 -小组学习情况统计")
//    @PostMapping("teamStatistics")
//    public ResultInfo<List<TeamStatisticsVO>> teamStatistics(@RequestBody TeamStatisticsDTO teamStatisticsDTO, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        if (Objects.isNull(headerUserInfo)){
//            throw new BusinessException(BaseResultCode.E_11012);
//        }
//        return ok(workV2Service.teamStatistics(teamStatisticsDTO, headerUserInfo));
//    }
//    @ApiOperation(value = "个人版 - 历史学习情况统计")
//    @PostMapping("historyWorking")
//    public ResultInfo<PageInfoBT<HistoryWorkingVO>> historyWorking(@RequestBody TeamStatisticsDTO teamStatisticsDTO, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        if (Objects.isNull(headerUserInfo)){
//            throw new BusinessException(BaseResultCode.E_11012);
//        }
//        return ok(workV2Service.historyWorking(teamStatisticsDTO, headerUserInfo));
//    }
//    /**
//     * 企业版 - 获取指定成员学习情况
//     *
//     * @param userStudyStatisticsDTO
//     * @return
//     */
//    @ApiOperation(value = "企业版 - 获取指定成员学习情况")
//    @PostMapping("userStudyStatistics")
//    public ResultInfo<UserStudyStatisticsVO> userStudyStatistics(@RequestBody UserStudyStatisticsDTO userStudyStatisticsDTO, @RequestHeaderUser HeaderUserInfo headerUserInfo) throws ExecutionException, InterruptedException {
//        if (Objects.isNull(headerUserInfo)){
//            throw new BusinessException(BaseResultCode.E_11012);
//        }
//        return ok(workV2Service.userStudyStatistics(userStudyStatisticsDTO, headerUserInfo));
//    }
//    @ApiOperation(value = "个人版 - 获取指定成员学习情况")
//    @PostMapping("memberWorking")
//    public ResultInfo<List<MyWorkDateVO>> memberWorking(@RequestBody MyWorkDTO myWorkDTO, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        if (Objects.isNull(headerUserInfo)){
//            throw new BusinessException(BaseResultCode.E_11012);
//        }
//        return ok(workV2Service.memberWorking(myWorkDTO, headerUserInfo));
//    }
//
//    @ApiOperation(value = "个人中心-学习情况")
//    @PostMapping("learningSituation")
//    public ResultInfo<LearningSituationVO> learningSituation(@RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        if (Objects.isNull(headerUserInfo)){
//            throw new BusinessException(BaseResultCode.E_11012);
//        }
//        return ok(workV2Service.learningSituation(headerUserInfo));
//    }
//
//    @ApiOperation(value = "个人版 -老师班级作业情况统计")
//    @PostMapping("getWorkGroupStatistics")
//    public ResultInfo<ClassHworkStatisticsVO> getWorkGroupStatistics(@RequestBody ClassStatisticsDTO classStatisticsDTO, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        if (Objects.isNull(headerUserInfo)){
//            throw new BusinessException(BaseResultCode.E_11012);
//        }
//        return ok(workV2Service.getWorkGroupStatistics(classStatisticsDTO, headerUserInfo));
//    }
//
//    @ApiOperation(value = "个人版 - 优秀班级/不理想班级 type:1优秀 2不理想", notes = "优秀班级/不理想班级 type:1优秀 2不理想")
//    @GetMapping("classRanking")
//    public ResultInfo<List<ClassRankVO>> classRanking(@RequestParam("courseId") Integer courseId, @RequestParam("type") Integer type, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        if (Objects.isNull(headerUserInfo)){
//            throw new BusinessException(BaseResultCode.E_11012);
//        }
//        return ok(workV2Service.classRankingV2(courseId,type, headerUserInfo));
//    }
//
//
//    @ApiOperation(value = "个人版 - 优秀学员排行榜", notes = "优秀学员排行榜")
//    @GetMapping("classMemberRanking")
//    public ResultInfo<List<ClassMemberRankVO>> classMemberRanking(@RequestParam("courseId") Integer courseId,@RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        if (Objects.isNull(headerUserInfo)){
//            throw new BusinessException(BaseResultCode.E_11012);
//        }
//        return ok(workV2Service.classMemberRankingV2(courseId,headerUserInfo));
//    }
//    @ApiOperation(value = "内部接口-企业版 - 作业情况统计")
//    @PostMapping("classStatisticsByTime")
//    public ResultInfo<ClassStatisticsVO> classStatisticsByTime(@RequestBody ClassStatisticsDTO classStatisticsDTO) {
//        return ok(workV2Service.classStatisticsByTime(classStatisticsDTO));
//    }
//}
