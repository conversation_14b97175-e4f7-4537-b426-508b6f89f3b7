package com.sibuqu.work.controller;

import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.work.dto.OpenCompanyWorkDTO;
import com.sibuqu.work.dto.sennuo.OpenClessesWorkDTO;
import com.sibuqu.work.service.WorkInfoService;
import com.sibuqu.work.vo.api.OpenCompanyWorkVO;
import com.sibuqu.work.vo.api.sennuo.OpenClassesWorkVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

@Slf4j
@RefreshScope
@RestController
@Api(value = "辅助信息",tags = {"辅助信息"})
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RequestMapping("/appwork/index")
public class IndexController {
    private final WorkInfoService workInfoService;

    @ApiOperation(value = "报警监控",notes = "报警监控")
    @PostMapping("/error")
    public void doError(){
        log.error("服务错误报警接口监控");
    }

    @ApiOperation(value = "对外数据接口",notes = "对外数据接口")
    @PostMapping("/open")
    public ResultInfo<List<OpenCompanyWorkVO>> openCompanyWorkList(@RequestBody OpenCompanyWorkDTO openCompanyWorkDTO){
        if (StringUtils.isEmpty(openCompanyWorkDTO.getSign()) || !"sennuo".equals(openCompanyWorkDTO.getSign())){
            return ResultInfo.error("验签错误");
        }
        if (StringUtils.isEmpty(openCompanyWorkDTO.getStartDate())){
           return ResultInfo.error("开始时间不能为空");
        }
        if (StringUtils.isEmpty(openCompanyWorkDTO.getEndDate())){
            return ResultInfo.error("结束时间不能为空");
        }
        return workInfoService.openCompanyWorkList(getDate(openCompanyWorkDTO.getStartDate()),getDate(openCompanyWorkDTO.getEndDate()));
    }

    @ApiOperation(value = "对外数据接口",notes = "对外数据接口")
    @PostMapping("/open/classes")
    public ResultInfo<PageInfoBT<OpenClassesWorkVO>> getOpenClassesWork(@RequestBody OpenClessesWorkDTO openClessesWorkDTO){
        if (StringUtils.isEmpty(openClessesWorkDTO.getSign()) || !"sennuo".equals(openClessesWorkDTO.getSign())){
            return ResultInfo.error("验签错误");
        }
        return ResultInfo.ok(workInfoService.getOpenClassesWork(openClessesWorkDTO));
    }

    private LocalDate getDate(String dateStr){
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return LocalDate.parse(dateStr, fmt);
    }
}
