package com.sibuqu.work.controller;

import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.work.dto.ClassWorkStatisticsDTO;
import com.sibuqu.work.dto.UserWorkStatisticsDTO;
import com.sibuqu.work.service.WorkStatisticsService;
import com.sibuqu.work.vo.admin.ClassWorkStatisticsListVO;
import com.sibuqu.work.vo.admin.ClassWorkStatisticsVO;
import com.sibuqu.work.vo.admin.UserWorkStatisticsListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.sibuqu.base.common.result.ResultInfo.ok;

/**
 * saas-作业相关统计"
 */
@Api(value = "saas-作业相关统计", tags = {"saas-作业相关统计"})
@RequestMapping("/appwork/manage/v1/work/statistics/")
@RestController
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class WorkManageStatisticsController {
    private final WorkStatisticsService workStatisticsService;

    /**
     * 班级作业统计
     *
     * @param ClassWorkStatisticsDto
     * @return
     */
    @ApiOperation(value = "班级作业统计")
    @PostMapping("classWorkStatistics")
    public ResultInfo<ClassWorkStatisticsVO> classWorkStatistics(@RequestBody ClassWorkStatisticsDTO ClassWorkStatisticsDto) {
        return ok(workStatisticsService.classWorkStatistics(ClassWorkStatisticsDto));
    }

    /**
     * 班级作业统计列表
     *
     * @param ClassWorkStatisticsDto
     * @return
     */
    @ApiOperation(value = "班级作业统计列表")
    @PostMapping("classWorkStatisticsList")
    public ResultInfo<PageInfoBT<ClassWorkStatisticsListVO>> classWorkStatisticsList(@RequestBody ClassWorkStatisticsDTO ClassWorkStatisticsDto) {
        return ok(workStatisticsService.classWorkStatisticsList(ClassWorkStatisticsDto));
    }

//    @ApiOperation(value = "小组作业统计")
//    @PostMapping("teamWorkStatistics")
//    public ResultInfo<ClassWorkStatisticsVO> teamWorkStatistics(@RequestBody TeamWorkStatisticsDTO teamWorkStatisticsDTO) {
//
//        return ok(workStatisticsService.teamWorkStatistics(teamWorkStatisticsDTO));
//    }
//
//    @ApiOperation(value = "小组作业统计列表")
//    @PostMapping("teamWorkStatisticsList")
//    public ResultInfo<PageInfoBT<TeamWorkStatisticsListVO>> teamWorkStatisticsList(@RequestBody TeamWorkStatisticsDTO teamWorkStatisticsDTO) {
//
//        return ok(workStatisticsService.teamWorkStatisticsList(teamWorkStatisticsDTO));
//    }

    @ApiOperation(value = "学员作业统计")
    @PostMapping("userWorkStatistics")
    public ResultInfo<ClassWorkStatisticsVO> userWorkStatistics(@RequestBody UserWorkStatisticsDTO userWorkStatisticsDTO) {
        return ok(workStatisticsService.userWorkStatistics(userWorkStatisticsDTO));
    }

    @ApiOperation(value = "学员作业统计列表")
    @PostMapping("userWorkStatisticsList")
    public ResultInfo<PageInfoBT<UserWorkStatisticsListVO>> userWorkStatisticsList(@RequestBody UserWorkStatisticsDTO userWorkStatisticsDTO) {
        return ok(workStatisticsService.userWorkStatisticsList(userWorkStatisticsDTO));
    }
}
