//package com.sibuqu.work.controller.v2;
//
//import com.alibaba.fastjson.JSON;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.sibuqu.base.common.exception.BusinessException;
//import com.sibuqu.base.common.page.PageInfoBT;
//import com.sibuqu.base.common.result.BaseResultCode;
//import com.sibuqu.base.common.result.HeaderUserInfo;
//import com.sibuqu.base.common.result.ResultInfo;
//import com.sibuqu.work.annation.RequestHeaderUser;
//import com.sibuqu.work.bo.ClassesMemberDynamicKafkaBO;
//import com.sibuqu.work.bo.DoWorkSendKafkaBO;
//import com.sibuqu.work.dto.*;
//import com.sibuqu.work.service.v2.WorkV2Service;
//import com.sibuqu.work.vo.AppWorkConfigVO;
//import com.sibuqu.work.vo.api.*;
//import com.sibuqu.work.webclient.model.vo.ClassGroupRoleVO;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import io.swagger.annotations.ApiOperationSupport;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.validation.BindingResult;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//import java.util.Map;
//import java.util.Objects;
//
//import static com.sibuqu.base.common.result.ResultInfo.ok;
//
///**
// * 四部曲App-写作业相关
// */
//@Slf4j
//@RestController
//@Api(value = "App写作业相关", tags = {"App写作业相关"})
//@RequestMapping("/appwork/app/v2/work/")
//@RequiredArgsConstructor(onConstructor_ = @Autowired)
//public class WorkV2Controller {
//    private final WorkV2Service workV2Service;
//    private final ObjectMapper objectMapper;
//
//    @ApiOperation(value = "检测work服务接口", notes = "检测work服务接口")
//    @GetMapping("/ok")
//    public String getOk() {
//        return "ok";
//    }
//
//    /**
//     * 获取作业卡
//     *
//     * @param workCardInfoDTO
//     * @return
//     */
//    @ApiOperationSupport(order = 1)
//    @ApiOperation(value = "获取作业卡")
//    @PostMapping("workCardInfo")
//    public ResultInfo<WorkCardInfoVO> workCardInfo(@RequestBody WorkCardInfoDTO workCardInfoDTO, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        if (Objects.isNull(headerUserInfo)) {
//            throw new BusinessException(BaseResultCode.E_11012);
//        }
//        return ok(workV2Service.workCardInfo(workCardInfoDTO, headerUserInfo));
//    }
//
//    /**
//     * 写作业
//     *
//     * @param doWorkDTO
//     * @return
//     */
//    @ApiOperationSupport(order = 2)
//    @ApiOperation(value = "写作业")
//    @PostMapping("doWork")
//    public ResultInfo doWork(@RequestBody DoWorkDTO doWorkDTO, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        workV2Service.doWork(doWorkDTO, headerUserInfo);
//        return ok();
//    }
//
//    /**
//     * 我的作业
//     *
//     * @param myWorkDTO
//     * @return
//     */
//    @ApiOperationSupport(order = 3)
//    @ApiOperation(value = "我的作业")
//    @PostMapping("myWork")
//    public ResultInfo<PageInfoBT<MyWorkVO>> myWork(@RequestBody MyWorkDTO myWorkDTO, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        if (Objects.isNull(headerUserInfo)) {
//            throw new BusinessException(BaseResultCode.E_11012);
//        }
//        return ok(workV2Service.myWork(myWorkDTO, headerUserInfo));
//    }
//
//
//    /**
//     * 我的分数
//     *
//     * @param myScoreDTO
//     * @return
//     */
//    @ApiOperationSupport(order = 4)
//    @ApiOperation(value = "我的分数")
//    @PostMapping("myScore")
//    public ResultInfo<MyScoreVO> myScore(@RequestBody MyScoreDTO myScoreDTO, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        if (Objects.isNull(headerUserInfo)) {
//            throw new BusinessException(BaseResultCode.E_11012);
//        }
//        return ok(workV2Service.myScore(myScoreDTO, headerUserInfo));
//    }
//
//    /**
//     * @param workStatusInfoDTO
//     * @return
//     */
//    @ApiOperationSupport(order = 5)
//    @ApiOperation(value = "作业状态信息")
//    @PostMapping("workStatusInfo")
//    public ResultInfo<WorkStatusInfoVO> workStatusInfo(@RequestBody WorkStatusInfoDTO workStatusInfoDTO) {
//        return ok(workV2Service.workStatusInfo(workStatusInfoDTO));
//    }
//
//
//    @ApiOperationSupport(order = 6)
//    @ApiOperation(value = "今日作业")
//    @PostMapping(value = "todayWork")
//    public ResultInfo<WorkCardInfoVO> todayWork(@RequestBody WorkCardInfoDTO workCardInfoDTO, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        if (Objects.isNull(headerUserInfo)) {
//            throw new BusinessException(BaseResultCode.E_11012);
//        }
//        return ok(workV2Service.todayWork(workCardInfoDTO, headerUserInfo));
//    }
//
//    @ApiOperationSupport(order = 7)
//    @ApiOperation(value = "往期作业")
//    @PostMapping(value = "previousWorkList")
//    public ResultInfo<PageInfoBT<WorkCardInfoVO>> previousWorkList(@RequestBody PreviousWorkDTO previousWorkDTO, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        return ok(workV2Service.previousWorkList(previousWorkDTO, headerUserInfo));
//    }
//
//    @ApiOperationSupport(order = 8)
//    @ApiOperation(value = "心得列表")
//    @PostMapping(value = "heartPerceptionList")
//    public ResultInfo<PageInfoBT<AppWorkInfoListVO>> heartPerceptionList(@RequestBody AppWorkHeartPerceptionDTO heartPerceptionDTO, BindingResult bindingResult,
//                                                                         @RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        if (Objects.isNull(headerUserInfo)) {
//            throw new BusinessException(BaseResultCode.E_11012);
//        }
//        return ResultInfo.ok(workV2Service.heartPerceptionListV2(heartPerceptionDTO, headerUserInfo));
//    }
//
//    @ApiOperationSupport(order = 9)
//    @ApiOperation(value = "老师回复心得列表")
//    @PostMapping(value = "teacherReplyHeartPerceptionList")
//    public ResultInfo<PageInfoBT<AppWorkInfoListVO>> teacherReplyHeartPerceptionList(@RequestBody AppWorkReplySearchDTO replySearchDTO, BindingResult bindingResult,
//                                                                                     @RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        if (Objects.isNull(headerUserInfo)) {
//            throw new BusinessException(BaseResultCode.E_11012);
//        }
//        return ResultInfo.ok(workV2Service.teacherReplyHeartPerceptionListV2(replySearchDTO, headerUserInfo));
//    }
//
//    @ApiOperationSupport(order = 10)
//    @ApiOperation(value = "老师/管理后台/企业管理员回复作业")
//    @PostMapping(value = "teacherReply")
//    public ResultInfo<List<AppWorkTeacherReplyVO>> teacherReply(@RequestBody AppWorkReplyDTO replyDTO, BindingResult bindingResult,
//                                                                @RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        if (Objects.isNull(headerUserInfo)) {
//            throw new BusinessException(BaseResultCode.E_11012);
//        }
//        return ResultInfo.ok(workV2Service.teacherReply(replyDTO, headerUserInfo));
//    }
//
//    @ApiOperationSupport(order = 11)
//    @ApiOperation(value = "老师删除作业回复")
//    @PostMapping(value = "teacherDeleteReply")
//    public ResultInfo teacherDeleteReply(@RequestBody AppWorkReplyDelDTO delDTO, BindingResult bindingResult,
//                                         @RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        if (Objects.isNull(headerUserInfo)) {
//            throw new BusinessException(BaseResultCode.E_11012);
//        }
//        workV2Service.teacherDeleteReply(delDTO, headerUserInfo);
//        return ResultInfo.ok();
//    }
//
//    @ApiOperationSupport(order = 13)
//    @ApiOperation(value = "附加作业信息")
//    @GetMapping(value = "additionalInfo/{courseId}")
//    public ResultInfo<AdditionalInfoVO> additionalInfo(@PathVariable("courseId") Integer courseId, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        if (Objects.isNull(headerUserInfo)) {
//            throw new BusinessException(BaseResultCode.E_11012);
//        }
//        return ok(workV2Service.additionalInfo(courseId));
//    }
//
//    @ApiOperationSupport(order = 14)
//    @ApiOperation(value = "完成附加作业作业信息")
//    @GetMapping(value = "addAdditionalScore/{courseId}")
//    public ResultInfo<Boolean> oldAddAdditionalScore(@PathVariable("courseId") Integer courseId, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        if (Objects.isNull(headerUserInfo)) {
//            throw new BusinessException(BaseResultCode.E_11012);
//        }
//        return ok(workV2Service.addAdditionalScore(courseId, null, headerUserInfo));
//    }
//
//    @ApiOperationSupport(order = 14)
//    @ApiOperation(value = "完成附加作业作业信息")
//    @GetMapping(value = "addAdditionalScore/{courseId}/{courseTimeTableId}")
//    public ResultInfo<Boolean> addAdditionalScore(@PathVariable("courseId") Integer courseId, @PathVariable("courseTimeTableId") Integer courseTimeTableId, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        if (Objects.isNull(headerUserInfo)) {
//            throw new BusinessException(BaseResultCode.E_11012);
//        }
//        return ok(workV2Service.addAdditionalScore(courseId, courseTimeTableId, headerUserInfo));
//    }
//
//
//    @ApiOperationSupport(order = 15)
//    @ApiOperation(value = "推荐作业列表")
//    @PostMapping(value = "recommendWorkList")
//    public ResultInfo<PageInfoBT<AppWorkInfoListVO>> recommendWorkList(@RequestBody AppWorkRecommendSearchDTO recommendSearchDTO, BindingResult bindingResult,
//                                                                       @RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        if (Objects.isNull(headerUserInfo)) {
//            throw new BusinessException(BaseResultCode.E_11012);
//        }
//        return ResultInfo.ok(workV2Service.recommendWorkList(recommendSearchDTO, headerUserInfo));
//    }
//
//    @ApiOperationSupport(order = 16)
//    @ApiOperation(value = "推荐作业")
//    @GetMapping(value = "recommendWork/{workId}")
//    public ResultInfo<ClassGroupRoleVO> recommendWork(@PathVariable("workId") Integer workId,
//                                                      @RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        if (Objects.isNull(headerUserInfo)) {
//            throw new BusinessException(BaseResultCode.E_11012);
//        }
//
//        return ok(workV2Service.recommendWork(workId, headerUserInfo));
//    }
//
//    @ApiOperation(value = "一键提醒")
//    @PostMapping(value = "workRemind")
//    public ResultInfo workRemind(@RequestBody WorkRemindDTO dto, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        //todo 需要优化
//        if (Objects.isNull(headerUserInfo)) {
//            throw new BusinessException(BaseResultCode.E_11012);
//        }
//        if (dto == null || dto.getClassesNo() == null || dto.getCourseId() == null) {
//            return ResultInfo.error("入参错误");
//        }
//        return workV2Service.workRemind(dto, headerUserInfo);
//    }
//
//
//    @ApiOperation(value = " 服务端接口 -获取推荐作业数")
//    @PostMapping(value = "getRecommendNum")
//    public GetRecommendVO getRecommendNum(@RequestBody GetRecommendNumDTO dto) {
//        return workV2Service.getRecommendNum(dto);
//    }
//
//    @ApiOperationSupport(order = 17)
//    @ApiOperation(value = "作业项分享加分")
//    @GetMapping(value = "shareWork/{courseId}/{courseTimeTableId}/{id}")
//    public ResultInfo<Boolean> shareWork(@PathVariable("courseId") Integer courseId, @PathVariable("courseTimeTableId") Integer courseTimeTableId, @PathVariable("id") Integer id, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        if (Objects.isNull(headerUserInfo)) {
//            throw new BusinessException(BaseResultCode.E_11012);
//        }
//        return ok(workV2Service.shareWork(courseId, courseTimeTableId, id, headerUserInfo));
//    }
//
//    @ApiOperation(value = " 服务端接口 - 学习情况")
//    @PostMapping(value = "getUserStudy")
//    public UserStudyVO getUserStudy(@RequestBody UserStudyDTO userStudyDTO) {
//        return workV2Service.getUserStudy(userStudyDTO);
//    }
//
//    @ApiOperation(value = " 服务端接口 - 听课加分")
//    @PostMapping(value = "listenAddScore")
//    public Boolean listenAddScore(@RequestBody DoWorkSendKafkaBO doWorkSendKafkaBo) {
//        return workV2Service.listenAddScore(doWorkSendKafkaBo);
//    }
//
//    @ApiOperation(value = " 服务端接口 - 进出班消息")
//    @PostMapping(value = "classesMemberDynamic")
//    public Boolean classesMemberDynamic(@RequestBody ClassesMemberDynamicKafkaBO classesMemberDynamicKafkaBO) {
//        return workV2Service.classesMemberDynamic(classesMemberDynamicKafkaBO);
//    }
//
//    @ApiOperation(value = " 服务端接口 - 生成排行榜")
//    @PostMapping(value = "generateRank")
//    public Boolean generateRank(@RequestBody DoWorkSendKafkaBO doWorkSendKafkaBo) {
//        return workV2Service.generateRank(doWorkSendKafkaBo);
//    }
//
//    @ApiOperation(value = " 服务端接口 - 分享加分")
//    @PostMapping(value = "syncShareWork")
//    public void syncShareWork(@RequestBody DoWorkMsgDTO doWorkMsgDTO){
//        log.info("syncShareWork收到消息:" + JSON.toJSONString(doWorkMsgDTO));
//        workV2Service.syncShareWork(doWorkMsgDTO);
//    }
//
//    @ApiOperation(value = " 服务端接口 - 延伸作业加分")
//    @PostMapping(value = "syncAddAdditionalScore")
//    public void syncAddAdditionalScore(@RequestBody DoWorkMsgDTO doWorkMsgDTO){
//        log.info("syncAddAdditionalScore收到消息:" + JSON.toJSONString(doWorkMsgDTO));
//        workV2Service.syncAddAdditionalScore(doWorkMsgDTO);
//    }
//
//    @ApiOperation(value = " 服务端接口 - 写作业")
//    @PostMapping(value = "syncDoWork")
//    public void syncDoWork(@RequestBody DoWorkMsgDTO doWorkMsgDTO){
//        log.info("syncDoWork收到消息:" + JSON.toJSONString(doWorkMsgDTO));
//        workV2Service.syncDoWork(doWorkMsgDTO);
//    }
//
//    @ApiOperationSupport(order = 18)
//    @ApiOperation(value = "批量获取作业状态信息")
//    @PostMapping("batchGetWorkStatusInfo")
//    public ResultInfo<Map<Integer, WorkStatusInfoVO>> batchGetWorkStatusInfo(@RequestBody WorkStatusInfoBatchDTO batchDTO) {
//        return ok(workV2Service.batchGetWorkStatusInfo(batchDTO));
//    }
//
//    @ApiOperationSupport(order = 19)
//    @ApiOperation(value = "补交作业列表")
//    @GetMapping("makeUpWorkList")
//    public ResultInfo<List<MakeUpWorkListVO>> makeUpWorkList(@RequestParam("courseId") Integer courseId, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        return ok(workV2Service.makeUpWorkList(courseId,headerUserInfo));
//    }
//
//    @ApiOperationSupport(order = 20)
//    @ApiOperation(value = "h5企业作业列表")
//    @PostMapping(value = "companyWorkList")
//    public ResultInfo<PageInfoBT<AppCompanyWorkInfoListVO>> companyWorkList(@RequestBody AppWorkHeartPerceptionDTO heartPerceptionDTO, BindingResult bindingResult,
//                                                                         @RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        if (Objects.isNull(headerUserInfo)) {
//            throw new BusinessException(BaseResultCode.E_11012);
//        }
//        return ResultInfo.ok(workV2Service.companyWorkList(heartPerceptionDTO, headerUserInfo));
//    }
//
//    @ApiOperationSupport(order = 21)
//    @ApiOperation(value = "企业专区")
//    @PostMapping(value = "companyPrefecture")
//    public ResultInfo<PageInfoBT<AppWorkInfoListVO>> companyPrefecture(@RequestBody CompanyPrefectureDTO companyPrefectureDTO,
//                                                                            @RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        if (Objects.isNull(headerUserInfo)) {
//            throw new BusinessException(BaseResultCode.E_11012);
//        }
//        return ResultInfo.ok(workV2Service.companyPrefecture(companyPrefectureDTO, headerUserInfo));
//    }
//
//    @ApiOperationSupport(order = 22)
//    @ApiOperation(value = "企业推荐作业列表")
//    @PostMapping(value = "companyRecommendWorkList")
//    public ResultInfo<PageInfoBT<AppWorkInfoListVO>> companyRecommendWorkList(@RequestBody AppWorkRecommendSearchDTO recommendSearchDTO, BindingResult bindingResult,
//                                                                       @RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        if (Objects.isNull(headerUserInfo)) {
//            throw new BusinessException(BaseResultCode.E_11012);
//        }
//        return ResultInfo.ok(workV2Service.companyRecommendWorkList(recommendSearchDTO, headerUserInfo));
//    }
//    @ApiOperationSupport(order = 23)
//    @ApiOperation(value = "app企业作业列表")
//    @PostMapping(value = "appCompanyWorkList")
//    public ResultInfo<PageInfoBT<AppWorkInfoListVO>> appCompanyWorkList(@RequestBody AppWorkHeartPerceptionDTO heartPerceptionDTO, BindingResult bindingResult,
//                                                                            @RequestHeaderUser HeaderUserInfo headerUserInfo) {
//        if (Objects.isNull(headerUserInfo)) {
//            throw new BusinessException(BaseResultCode.E_11012);
//        }
//        return ResultInfo.ok(workV2Service.appCompanyWorkList(heartPerceptionDTO, headerUserInfo));
//    }
//
//    @ApiOperation(value = "伙伴心得")
//    @PostMapping("/companyWorkPage")
//    public ResultInfo<PageInfoBT<LazyAppWorkInfoVO>> companyWorkPage(@RequestBody @Validated CompanyWorkPageDTO dto) {
//        PageInfoBT<LazyAppWorkInfoVO> pageInfoBT = workV2Service.companyWorkPage(dto);
//        return ResultInfo.ok(pageInfoBT);
//    }
//
//    @ApiOperation(value = "作业回应")
//    @PostMapping("/workReplyPage")
//    public ResultInfo<PageInfoBT<LazyAppWorkInfoVO>> workReplyPage(@RequestBody @Validated WorkReplyPageDTO dto) {
//        PageInfoBT<LazyAppWorkInfoVO> pageInfoBT = workV2Service.workReplyPage(dto);
//        return ResultInfo.ok(pageInfoBT);
//    }
//
//    @ApiOperation(value = "作业推荐心得")
//    @PostMapping("/workRecommendPage")
//    public ResultInfo<PageInfoBT<LazyAppWorkInfoVO>> workRecommendPage(@RequestBody @Validated WorkRecommendPageDTO dto) {
//        PageInfoBT<LazyAppWorkInfoVO> pageInfoBT = workV2Service.workRecommendPage(dto);
//        return ResultInfo.ok(pageInfoBT);
//    }
//
//    @ApiOperation(value = "班级作业数据")
//    @PostMapping("/classWorkData")
//    public ResultInfo<ClassWorkDataVO> classWorkData(@RequestBody @Validated ClassWorkDataDTO dto) {
//        ClassWorkDataVO vo = workV2Service.classWorkData(dto);
//        return ResultInfo.ok(vo);
//    }
//
//    @ApiOperation(value = "班级作业排行")
//    @PostMapping("/classWorkRank")
//    public ResultInfo<ClassWorkRankVO> classWorkRank(@RequestBody @Validated ClassWorkRankDTO dto) {
//        ClassWorkRankVO vo = workV2Service.classWorkRank(dto);
//        return ResultInfo.ok(vo);
//    }
//
//    @ApiOperation(value = "个人作业数据")
//    @PostMapping("/personalWorkData")
//    public ResultInfo<PersonalWorkDataVO> personalWorkData(@RequestBody @Validated PersonalWorkDataDTO dto) {
//        PersonalWorkDataVO vo = workV2Service.personalWorkData(dto);
//        return ResultInfo.ok(vo);
//    }
//
//    @ApiOperation(value = "个人作业排行")
//    @PostMapping("/personalWorkRank")
//    public ResultInfo<PersonalWorkRankVO> personalWorkRank(@RequestBody @Validated PersonalWorkRankDTO dto) {
//        PersonalWorkRankVO vo = workV2Service.personalWorkRank(dto);
//        return ResultInfo.ok(vo);
//    }
//
//    @ApiOperation(value = "企业排行")
//    @PostMapping("/companyRank")
//    public ResultInfo<CompanyRankVO> companyRank(@RequestBody @Validated CompanyRankDTO dto) {
//        CompanyRankVO vo = workV2Service.companyRank(dto);
//        return ResultInfo.ok(vo);
//    }
//
//    @ApiOperationSupport(order = 24)
//    @ApiOperation(value = "app作业配置")
//    @PostMapping(value = "appWorkConfig")
//    public ResultInfo<AppWorkConfigVO> appWorkConfig(@RequestBody AppWorkConfigDTO dto) {
//        return ResultInfo.ok(workV2Service.appWorkConfig(dto));
//    }
//
//    @ApiOperation(value = "读书加分")
//    @PostMapping("/readAddScore")
//    public ResultInfo<Integer> readAddScore(@RequestBody @Validated ReadAddScoreDTO dto) {
//        workV2Service.readAddScore(dto);
//        return ResultInfo.ok(1);
//    }
//
//    @ApiOperation(value = "作业详情承接页")
//    @PostMapping("/oneWork")
//    public ResultInfo<OneWorkVO> oneWork(@RequestBody @Validated OneWorkDTO dto) {
//        OneWorkVO vo = workV2Service.oneWork(dto);
//        return ResultInfo.ok(vo);
//    }
//
//    @ApiOperation(value = "根据作业id查询")
//    @PostMapping("/workById")
//    public ResultInfo<WorkByIdVO> workById(@RequestBody @Validated IdDTO dto) {
//        WorkByIdVO vo = workV2Service.workById(dto);
//        return ResultInfo.ok(vo);
//    }
//
//}
