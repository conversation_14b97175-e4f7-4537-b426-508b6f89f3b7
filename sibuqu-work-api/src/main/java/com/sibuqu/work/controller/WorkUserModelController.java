package com.sibuqu.work.controller;

import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.work.dto.AddWorkUserModelDTO;
import com.sibuqu.work.dto.QueryWorkUserModelDTO;
import com.sibuqu.work.service.WorkUserModelService;
import com.sibuqu.work.vo.QueryWorkUserModelListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "用户选择的作业模板")
@RestController
@RequestMapping("/appwork/app/v1/work/work-user-model")
public class WorkUserModelController {

    @Autowired
    private WorkUserModelService workUserModelService;

    @ApiOperation("保存设置")
    @PostMapping("/addWorkUserModel")
    public ResultInfo<String> addWorkUserModel(@RequestBody @Validated AddWorkUserModelDTO dto) {
        return ResultInfo.ok(workUserModelService.addWorkUserModel(dto));
    }

    @ApiOperation("查询设置")
    @PostMapping("/queryWorkUserModel")
    public ResultInfo<List<QueryWorkUserModelListVO>> queryWorkUserModel(@RequestBody @Validated QueryWorkUserModelDTO dto) {
        return ResultInfo.ok(workUserModelService.queryWorkUserModel(dto));
    }

}
