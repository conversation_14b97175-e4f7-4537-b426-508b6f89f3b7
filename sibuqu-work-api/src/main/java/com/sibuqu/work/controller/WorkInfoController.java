package com.sibuqu.work.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.base.common.result.HeaderUserInfo;
import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.base.common.utils.ExcelUtils;
import com.sibuqu.work.annation.RequestHeaderUser;
import com.sibuqu.work.dto.CheckOldStudentCountDTO;
import com.sibuqu.work.dto.CheckOldStudentDTO;
import com.sibuqu.work.dto.CurWeekWorkDTO;
import com.sibuqu.work.dto.HistoryWeekWorkDTO;
import com.sibuqu.work.dto.RecordThinkDTO;
import com.sibuqu.work.dto.WorkFlexibleItemSearchDTO;
import com.sibuqu.work.dto.WorkInfoListDTO;
import com.sibuqu.work.dto.WorkInfoRecommendTeacherDTO;
import com.sibuqu.work.entity.WorkInfo;
import com.sibuqu.work.service.WorkInfoService;
import com.sibuqu.work.service.WorkTeacherReplyService;
import com.sibuqu.work.vo.CurWeekWorkVO;
import com.sibuqu.work.vo.HistoryWeekWorkItemVO;
import com.sibuqu.work.vo.HistoryWeekWorkVO;
import com.sibuqu.work.vo.admin.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.sibuqu.base.common.result.ResultInfo.ok;

/**
 * 【后台管理系统-四部曲】作业相关"
 * <AUTHOR>
 */
@Slf4j
@RefreshScope
@Api(value = "后台管理-作业相关", tags = {"后台管理-作业相关"})
@RequestMapping("/appwork/manage/v1/workInfo/")
@RestController
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class WorkInfoController {
    private final WorkInfoService workInfoService;
    private final WorkTeacherReplyService workTeacherReplyService;
    /**
     * 作业列表
     *
     * @param dto 入参
     * @return
     */
    @ApiOperation(value = "【后台管理系统】作业列表")
    @PostMapping("workList")
    public ResultInfo<PageInfoBT<WorkInfoListVO>> workList(@RequestBody WorkInfoListDTO dto) {
        log.info("【后台管理系统】作业列表---WorkInfoController#workList---dto:{}------", JSON.toJSONString(dto));
        return ok(workInfoService.getWorkInfoList(dto,false));
    }
    /**
     * 作业详情
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "【后台管理系统】作业详情")
    @GetMapping("workInfo/detail")
    public ResultInfo<WorkInfoDetailVO> workInfoDetail(@RequestParam("id") Integer id) {
        log.info("【后台管理系统】作业详情---WorkInfoController#workInfoDetail---主键:{}------", id);
        WorkInfoDetailVO workInfoDetailVO = workInfoService.workInfoDetail(id);
        log.info("【后台管理系统】作业详情---WorkInfoController#workInfoDetail---主键:{}--返回的信息：{}----", id,JSON.toJSONString(workInfoDetailVO));
        return ok(workInfoDetailVO);
    }

    @ApiOperation(value = "【后台管理系统】作业列表导出")
    @PostMapping("workListExport")
    public void workListExport(@RequestBody WorkInfoListDTO dto, HttpServletResponse response) {
        log.info("【后台管理系统】作业列表导出---WorkInfoController#workListExport---dto:{}------", JSON.toJSONString(dto));
        PageInfoBT<WorkInfoListVO> workInfoList = workInfoService.getWorkInfoList(dto, true);

        List<WorkInfoListExportVO> workInfoListExportVOList =new ArrayList<>();
        if(CollectionUtil.isNotEmpty(workInfoList.getRecords())){
            //从vo转换为model
            workInfoListExportVOList= workInfoList.getRecords().stream()
                    .map(WorkInfoListExportVO::vo2ExportModel)
                    .collect(Collectors.toList());
        }
        ExcelUtils.exportExcel(workInfoListExportVOList, "作业列表", "作业列表", WorkInfoListExportVO.class, "作业列表.xls", response);
    }
    @ApiOperation(value = "【后台管理系统】推荐作业列表导出")
    @PostMapping("/recommend/workListExport")
    public void recommendWorkListExport(@RequestBody WorkInfoListDTO dto, HttpServletResponse response) {
        log.info("【后台管理系统】推荐作业列表导出---WorkInfoController#recommendWorkListExport---dto:{}------", JSON.toJSONString(dto));
        PageInfoBT<WorkInfoListVO> workInfoList = workInfoService.getWorkInfoList(dto, true);
        List<WorkInfoRecommendListExportVO> workInfoRecommendListExportVOList =new ArrayList<>();
        if(CollectionUtil.isNotEmpty(workInfoList.getRecords())){
            //从vo转换为model
            workInfoRecommendListExportVOList= workInfoList.getRecords().stream()
                    .map(WorkInfoRecommendListExportVO::vo2ExportModel)
                    .collect(Collectors.toList());
        }
        ExcelUtils.exportExcel(workInfoRecommendListExportVOList, "推荐作业列表", "推荐作业列表", WorkInfoRecommendListExportVO.class, "推荐作业列表.xls", response);
    }

    @ApiOperation(value = "【后台管理系统】推送给老师/取消推送作业")
    @PostMapping("recommendToTeacher")
    public ResultInfo<Boolean> recommendToTeacher(@RequestBody WorkInfoRecommendTeacherDTO dto, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
        log.info("【后台管理系统】推送给老师/取消推送作业---WorkInfoController#recommendToTeacher---dto:{}------", JSON.toJSONString(dto));
        return ok( workInfoService.recommendToTeacher(dto,headerUserInfo.getId()));
    }



    /**
     * 模板化老师回复删除
     * @param id
     * @return
     */
    @ApiOperation(value = "【后台管理系统】模板化老师回复删除")
    @GetMapping("workInfo/replay/delete")
    public ResultInfo<Boolean> replayDelete(@RequestParam("id") Integer id) {
        log.info("【后台管理系统】【后台管理系统】模板化老师回复删除---WorkInfoController#replayDelete---主键:{}------", id);
        Boolean result = workTeacherReplyService.replayDelete(id);
        log.info("【后台管理系统】【后台管理系统】模板化老师回复删除---WorkInfoController#replayDelete---主键:{}--返回的信息：{}----", id,result);
        return ok(result);
    }


    /**
     * 课程下的作业动态文本类型项 目前最多5列
     * 列
     *
     * @param dto 入参
     * @return
     */
    @ApiOperation(value = "【后台管理系统】课程下的作业动态文本类型显示项 目前最多5列")
    @PostMapping("workFlexibleColumnNameList")
    public ResultInfo<WorkFlexibleItemListVO> workFlexibleColumnNameList(@RequestBody WorkFlexibleItemSearchDTO dto) {
        log.info("【后台管理系统】课程下的作业动态文本类型显示项 目前最多5列---WorkInfoController#workFlexibleColumnNameList---dto:{}------", JSON.toJSONString(dto));
        return ok(workInfoService.workFlexibleColumnNameList(dto));
    }

    @ApiOperation(value = "内部服务-获取作业信息")
    @GetMapping("getWorkInfoById")
    public ResultInfo<WorkInfo> getWorkInfoById(@RequestParam("id") Integer id) {
        WorkInfo workInfo = workInfoService.getById(id);
        return ResultInfo.ok(workInfo);
    }

    @ApiOperation(value = "批量同步作业到专区 courseType1推荐作业2老师回复3企业回复")
    @GetMapping("batchAsyncWork")
    public ResultInfo batchAsyncWork(@RequestParam("courseId")Integer courseId,@RequestParam("contentType") Integer contentType){
        workInfoService.batchAsyncWork(courseId,contentType);
        return ok();
    }

    @ApiOperation("用户是否是老学员")
    @PostMapping("checkOldStudent")
    public ResultInfo<Boolean> checkOldStudent(@RequestBody CheckOldStudentDTO dto) {
        return ok(workInfoService.checkOldStudent(dto));
    }

    @ApiOperation("用户列表中有多少老学员")
    @PostMapping("checkOldStudentCount")
    public ResultInfo<List<Integer>> checkOldStudentCount(@RequestBody CheckOldStudentCountDTO dto) {
        return ok(workInfoService.checkOldStudentCount(dto));
    }

    @ApiOperation("作业数据按照 courseId 插入到 Mongo")
    @PostMapping("workMySQLtoMongo")
    public ResultInfo<Integer> workMySQLtoMongo(@RequestBody List<Integer> courseIdList) {
        return ok(workInfoService.workMySQLtoMongo(courseIdList));
    }

    @ApiOperation(value = "[内部调用]-全量明文手机号加密")
    @PostMapping("/replacePhone")
    public String replacePhone(){
        return workInfoService.replacePhone();
    }

}
