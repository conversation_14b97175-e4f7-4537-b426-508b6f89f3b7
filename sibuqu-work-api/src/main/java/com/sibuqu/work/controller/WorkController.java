package com.sibuqu.work.controller;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sibuqu.base.common.exception.BusinessException;
import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.base.common.result.BaseResultCode;
import com.sibuqu.base.common.result.HeaderUserInfo;
import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.starter.redis.aop.DistributedLock;
import com.sibuqu.work.annation.RequestHeaderUser;
import com.sibuqu.work.bo.ClassesMemberDynamicKafkaBO;
import com.sibuqu.work.bo.DoWorkSendKafkaBO;
import com.sibuqu.work.dto.*;
import com.sibuqu.work.service.WorkInfoService;
import com.sibuqu.work.service.WorkService;
import com.sibuqu.work.vo.AppWorkConfigVO;
import com.sibuqu.work.vo.CurWeekWorkVO;
import com.sibuqu.work.vo.HistoryWeekWorkItemVO;
import com.sibuqu.work.vo.api.AdditionalInfoVO;
import com.sibuqu.work.vo.api.AppCompanyWorkInfoListVO;
import com.sibuqu.work.vo.api.AppWorkInfoListVO;
import com.sibuqu.work.vo.api.AppWorkTeacherReplyVO;
import com.sibuqu.work.vo.api.ClassWorkDataVO;
import com.sibuqu.work.vo.api.ClassWorkRankVO;
import com.sibuqu.work.vo.api.CompanyRankVO;
import com.sibuqu.work.vo.api.GetRecommendVO;
import com.sibuqu.work.vo.api.LazyAppWorkInfoVO;
import com.sibuqu.work.vo.api.MakeUpWorkListVO;
import com.sibuqu.work.vo.api.MyScoreVO;
import com.sibuqu.work.vo.api.MyWorkVO;
import com.sibuqu.work.vo.api.OneWorkVO;
import com.sibuqu.work.vo.api.PersonalWorkDataVO;
import com.sibuqu.work.vo.api.PersonalWorkRankVO;
import com.sibuqu.work.vo.api.UserStudyVO;
import com.sibuqu.work.vo.api.WorkByIdVO;
import com.sibuqu.work.vo.api.WorkCardInfoVO;
import com.sibuqu.work.vo.api.WorkStatusInfoVO;
import com.sibuqu.work.webclient.model.vo.ClassGroupRoleVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.sibuqu.base.common.result.ResultInfo.ok;

/**
 * 四部曲App-写作业相关
 */
@Slf4j
@RestController
@Api(value = "App写作业相关", tags = {"App写作业相关"})
@RequestMapping("/appwork/app/v1/work/")
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class WorkController {
    private final WorkService workService;
    private final ObjectMapper objectMapper;
    private final WorkInfoService workInfoService;

    @ApiOperation(value = "检测work服务接口", notes = "检测work服务接口")
    @GetMapping("/ok")
    public String getOk() {
        return "ok";
    }

    /**
     * 获取作业卡
     *
     * @param workCardInfoDTO
     * @return
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "获取作业卡")
    @PostMapping("workCardInfo")
    public ResultInfo<WorkCardInfoVO> workCardInfo(@RequestBody WorkCardInfoDTO workCardInfoDTO, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
        if (Objects.isNull(headerUserInfo)) {
            throw new BusinessException(BaseResultCode.E_11012);
        }
        return ok(workService.workCardInfo(workCardInfoDTO, headerUserInfo));
    }

    /**
     * 写作业
     *
     * @param doWorkDTO
     * @return
     */
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "写作业")
    @PostMapping("doWork")
    public ResultInfo doWork(@RequestBody DoWorkDTO doWorkDTO, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
        workService.doWork(doWorkDTO, headerUserInfo);
        return ok();
    }

    /**
     * 我的作业
     *
     * @param myWorkDTO
     * @return
     */
    @ApiOperationSupport(order = 3)
    @ApiOperation(value = "我的作业")
    @PostMapping("myWork")
    public ResultInfo<PageInfoBT<MyWorkVO>> myWork(@RequestBody MyWorkDTO myWorkDTO, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
        if (Objects.isNull(headerUserInfo)) {
            throw new BusinessException(BaseResultCode.E_11012);
        }
        return ok(workService.myWork(myWorkDTO, headerUserInfo));
    }


    /**
     * 我的分数
     *
     * @param myScoreDTO
     * @return
     */
    @ApiOperationSupport(order = 4)
    @ApiOperation(value = "我的分数")
    @PostMapping("myScore")
    public ResultInfo<MyScoreVO> myScore(@RequestBody MyScoreDTO myScoreDTO, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
        if (Objects.isNull(headerUserInfo)) {
            throw new BusinessException(BaseResultCode.E_11012);
        }
        return ok(workService.myScore(myScoreDTO, headerUserInfo));
    }

    /**
     * @param workStatusInfoDTO
     * @return
     */
    @ApiOperationSupport(order = 5)
    @ApiOperation(value = "作业状态信息")
    @PostMapping("workStatusInfo")
    public ResultInfo<WorkStatusInfoVO> workStatusInfo(@RequestBody WorkStatusInfoDTO workStatusInfoDTO) {
        return ok(workService.workStatusInfo(workStatusInfoDTO));
    }


    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "今日作业")
    @PostMapping(value = "todayWork")
    public ResultInfo<WorkCardInfoVO> todayWork(@RequestBody WorkCardInfoDTO workCardInfoDTO, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
        if (Objects.isNull(headerUserInfo)) {
            throw new BusinessException(BaseResultCode.E_11012);
        }
        return ok(workService.todayWork(workCardInfoDTO, headerUserInfo));
    }

    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "往期作业")
    @PostMapping(value = "previousWorkList")
    public ResultInfo<PageInfoBT<WorkCardInfoVO>> previousWorkList(@RequestBody PreviousWorkDTO previousWorkDTO, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
        return ok(workService.previousWorkList(previousWorkDTO, headerUserInfo));
    }

    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "心得列表")
    @PostMapping(value = "heartPerceptionList")
    public ResultInfo<PageInfoBT<AppWorkInfoListVO>> heartPerceptionList(@RequestBody AppWorkHeartPerceptionDTO heartPerceptionDTO, BindingResult bindingResult,
                                                                         @RequestHeaderUser HeaderUserInfo headerUserInfo) {
        if (Objects.isNull(headerUserInfo)) {
            throw new BusinessException(BaseResultCode.E_11012);
        }
        return ResultInfo.ok(workService.heartPerceptionList(heartPerceptionDTO, headerUserInfo));
    }

    @ApiOperationSupport(order = 9)
    @ApiOperation(value = "老师回复心得列表")
    @PostMapping(value = "teacherReplyHeartPerceptionList")
    public ResultInfo<PageInfoBT<AppWorkInfoListVO>> teacherReplyHeartPerceptionList(@RequestBody AppWorkReplySearchDTO replySearchDTO, BindingResult bindingResult,
                                                                                     @RequestHeaderUser HeaderUserInfo headerUserInfo) {
        if (Objects.isNull(headerUserInfo)) {
            throw new BusinessException(BaseResultCode.E_11012);
        }
        return ResultInfo.ok(workService.teacherReplyHeartPerceptionList(replySearchDTO, headerUserInfo));
    }

    @ApiOperationSupport(order = 10)
    @ApiOperation(value = "老师/管理后台/企业管理员回复作业")
    @PostMapping(value = "teacherReply")
    public ResultInfo<List<AppWorkTeacherReplyVO>> teacherReply(@RequestBody AppWorkReplyDTO replyDTO, BindingResult bindingResult,
                                                                @RequestHeaderUser HeaderUserInfo headerUserInfo) {
        if (Objects.isNull(headerUserInfo)) {
            throw new BusinessException(BaseResultCode.E_11012);
        }
        return ResultInfo.ok(workService.teacherReply(replyDTO, headerUserInfo));
    }

    @ApiOperationSupport(order = 11)
    @ApiOperation(value = "老师删除作业回复")
    @PostMapping(value = "teacherDeleteReply")
    public ResultInfo teacherDeleteReply(@RequestBody AppWorkReplyDelDTO delDTO, BindingResult bindingResult,
                                         @RequestHeaderUser HeaderUserInfo headerUserInfo) {
        if (Objects.isNull(headerUserInfo)) {
            throw new BusinessException(BaseResultCode.E_11012);
        }
        workService.teacherDeleteReply(delDTO, headerUserInfo);
        return ResultInfo.ok();
    }

    @ApiOperationSupport(order = 13)
    @ApiOperation(value = "附加作业信息")
    @GetMapping(value = "additionalInfo/{courseId}")
    public ResultInfo<AdditionalInfoVO> additionalInfo(@PathVariable("courseId") Integer courseId, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
        if (Objects.isNull(headerUserInfo)) {
            throw new BusinessException(BaseResultCode.E_11012);
        }
        return ok(workService.additionalInfo(courseId));
    }

    @ApiOperationSupport(order = 14)
    @ApiOperation(value = "完成附加作业作业信息")
    @GetMapping(value = "addAdditionalScore/{courseId}")
    public ResultInfo<Boolean> oldAddAdditionalScore(@PathVariable("courseId") Integer courseId, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
        if (Objects.isNull(headerUserInfo)) {
            throw new BusinessException(BaseResultCode.E_11012);
        }
        return ok(workService.addAdditionalScore(courseId, null, headerUserInfo));
    }

    @ApiOperationSupport(order = 14)
    @ApiOperation(value = "完成附加作业作业信息")
    @GetMapping(value = "addAdditionalScore/{courseId}/{courseTimeTableId}")
    public ResultInfo<Boolean> addAdditionalScore(@PathVariable("courseId") Integer courseId, @PathVariable("courseTimeTableId") Integer courseTimeTableId, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
        if (Objects.isNull(headerUserInfo)) {
            throw new BusinessException(BaseResultCode.E_11012);
        }
        return ok(workService.addAdditionalScore(courseId, courseTimeTableId, headerUserInfo));
    }


    @ApiOperationSupport(order = 15)
    @ApiOperation(value = "推荐作业列表")
    @PostMapping(value = "recommendWorkList")
    public ResultInfo<PageInfoBT<AppWorkInfoListVO>> recommendWorkList(@RequestBody AppWorkRecommendSearchDTO recommendSearchDTO, BindingResult bindingResult,
                                                                       @RequestHeaderUser HeaderUserInfo headerUserInfo) {
        if (Objects.isNull(headerUserInfo)) {
            throw new BusinessException(BaseResultCode.E_11012);
        }
        return ResultInfo.ok(workService.recommendWorkList(recommendSearchDTO, headerUserInfo));
    }

    @ApiOperationSupport(order = 16)
    @ApiOperation(value = "推荐作业")
    @GetMapping(value = "recommendWork/{workId}")
    public ResultInfo<ClassGroupRoleVO> recommendWork(@PathVariable("workId") Integer workId,
                                                      @RequestHeaderUser HeaderUserInfo headerUserInfo) {
        if (Objects.isNull(headerUserInfo)) {
            throw new BusinessException(BaseResultCode.E_11012);
        }

        return ok(workService.recommendWork(workId, headerUserInfo));
    }

    @ApiOperation(value = "一键提醒")
    @PostMapping(value = "workRemind")
    public ResultInfo workRemind(@RequestBody WorkRemindDTO dto, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
        return workService.workRemind(dto, headerUserInfo);
    }


    @ApiOperation(value = " 服务端接口 -获取推荐作业数")
    @PostMapping(value = "getRecommendNum")
    public GetRecommendVO getRecommendNum(@RequestBody GetRecommendNumDTO dto) {
        return workService.getRecommendNum(dto);
    }

    @ApiOperationSupport(order = 17)
    @ApiOperation(value = "作业项分享加分")
    @GetMapping(value = "shareWork/{courseId}/{courseTimeTableId}/{id}")
    public ResultInfo<Boolean> shareWork(@PathVariable("courseId") Integer courseId, @PathVariable("courseTimeTableId") Integer courseTimeTableId, @PathVariable("id") Integer id, @RequestHeaderUser HeaderUserInfo headerUserInfo) {
        if (Objects.isNull(headerUserInfo)) {
            throw new BusinessException(BaseResultCode.E_11012);
        }
        return ok(workService.shareWork(courseId, courseTimeTableId, id, headerUserInfo));
    }

    @ApiOperation(value = " 服务端接口 - 学习情况")
    @PostMapping(value = "getUserStudy")
    public UserStudyVO getUserStudy(@RequestBody UserStudyDTO userStudyDTO) {
        return workService.getUserStudy(userStudyDTO);
    }

    @ApiOperation(value = " 服务端接口 - 听课加分")
    @PostMapping(value = "listenAddScore")
    public Boolean listenAddScore(@RequestBody DoWorkSendKafkaBO doWorkSendKafkaBo) {
        return workService.listenAddScore(doWorkSendKafkaBo);
    }

    @ApiOperation(value = " 服务端接口 - 进出班消息")
    @PostMapping(value = "classesMemberDynamic")
    public Boolean classesMemberDynamic(@RequestBody ClassesMemberDynamicKafkaBO classesMemberDynamicKafkaBO) {
        return workService.classesMemberDynamic(classesMemberDynamicKafkaBO);
    }
    
    @ApiOperation(value = " 服务端接口 - 生成排行榜")
    @PostMapping(value = "generateRank")
    public Boolean generateRank(@RequestBody DoWorkSendKafkaBO doWorkSendKafkaBo) {
        return workService.generateRank(doWorkSendKafkaBo);
    }

    @ApiOperation(value = " 服务端接口 - 分享加分")
    @PostMapping(value = "syncShareWork")
    public void syncShareWork(@RequestBody DoWorkMsgDTO doWorkMsgDTO){
        log.info("syncShareWork收到消息:" + JSON.toJSONString(doWorkMsgDTO));
        workService.syncShareWork(doWorkMsgDTO);
    }

    @ApiOperation(value = " 服务端接口 - 延伸作业加分")
    @PostMapping(value = "syncAddAdditionalScore")
    public void syncAddAdditionalScore(@RequestBody DoWorkMsgDTO doWorkMsgDTO){
        log.info("syncAddAdditionalScore收到消息:" + JSON.toJSONString(doWorkMsgDTO));
        workService.syncAddAdditionalScore(doWorkMsgDTO);
    }

    @ApiOperation(value = " 服务端接口 - 写作业")
    @PostMapping(value = "syncDoWork")
    public void syncDoWork(@RequestBody DoWorkMsgDTO doWorkMsgDTO){
        log.info("syncDoWork收到消息:" + JSON.toJSONString(doWorkMsgDTO));
        workService.syncDoWork(doWorkMsgDTO);
    }

    @ApiOperationSupport(order = 18)
    @ApiOperation(value = "批量获取作业状态信息")
    @PostMapping("batchGetWorkStatusInfo")
    public ResultInfo<Map<Integer, WorkStatusInfoVO>> batchGetWorkStatusInfo(@RequestBody WorkStatusInfoBatchDTO batchDTO) {
        return ok(workService.batchGetWorkStatusInfo(batchDTO));
    }

    @ApiOperationSupport(order = 19)
    @ApiOperation(value = "补交作业列表")
    @GetMapping("makeUpWorkList")
    public ResultInfo<List<MakeUpWorkListVO>> makeUpWorkList(@RequestParam("courseId") Integer courseId, @RequestHeaderUser HeaderUserInfo headerUserInfo, HttpServletRequest request) {
        return ok(workService.makeUpWorkList(courseId,headerUserInfo, request));
    }

    @ApiOperationSupport(order = 20)
    @ApiOperation(value = "h5企业作业列表")
    @PostMapping(value = "companyWorkList")
    public ResultInfo<PageInfoBT<AppCompanyWorkInfoListVO>> companyWorkList(@RequestBody AppWorkHeartPerceptionDTO heartPerceptionDTO, BindingResult bindingResult,
                                                                         @RequestHeaderUser HeaderUserInfo headerUserInfo) {
        if (Objects.isNull(headerUserInfo)) {
            throw new BusinessException(BaseResultCode.E_11012);
        }
        return ResultInfo.ok(workService.companyWorkList(heartPerceptionDTO, headerUserInfo));
    }

    @ApiOperationSupport(order = 21)
    @ApiOperation(value = "企业专区")
    @PostMapping(value = "companyPrefecture")
    public ResultInfo<PageInfoBT<AppWorkInfoListVO>> companyPrefecture(@RequestBody CompanyPrefectureDTO companyPrefectureDTO,
                                                                            @RequestHeaderUser HeaderUserInfo headerUserInfo) {
        if (Objects.isNull(headerUserInfo)) {
            throw new BusinessException(BaseResultCode.E_11012);
        }
        return ResultInfo.ok(workService.companyPrefecture(companyPrefectureDTO, headerUserInfo));
    }

    @ApiOperationSupport(order = 22)
    @ApiOperation(value = "企业推荐作业列表")
    @PostMapping(value = "companyRecommendWorkList")
    public ResultInfo<PageInfoBT<AppWorkInfoListVO>> companyRecommendWorkList(@RequestBody AppWorkRecommendSearchDTO recommendSearchDTO, BindingResult bindingResult,
                                                                       @RequestHeaderUser HeaderUserInfo headerUserInfo) {
        if (Objects.isNull(headerUserInfo)) {
            throw new BusinessException(BaseResultCode.E_11012);
        }
        return ResultInfo.ok(workService.companyRecommendWorkList(recommendSearchDTO, headerUserInfo));
    }
    @ApiOperationSupport(order = 23)
    @ApiOperation(value = "app企业作业列表")
    @PostMapping(value = "appCompanyWorkList")
    public ResultInfo<PageInfoBT<AppWorkInfoListVO>> appCompanyWorkList(@RequestBody AppWorkHeartPerceptionDTO heartPerceptionDTO, BindingResult bindingResult,
                                                                            @RequestHeaderUser HeaderUserInfo headerUserInfo) {
        if (Objects.isNull(headerUserInfo)) {
            throw new BusinessException(BaseResultCode.E_11012);
        }
        return ResultInfo.ok(workService.appCompanyWorkList(heartPerceptionDTO, headerUserInfo));
    }

    @ApiOperation(value = "伙伴心得")
    @PostMapping("/companyWorkPage")
    public ResultInfo<PageInfoBT<LazyAppWorkInfoVO>> companyWorkPage(@RequestBody @Validated CompanyWorkPageDTO dto) {
        PageInfoBT<LazyAppWorkInfoVO> pageInfoBT = workService.companyWorkPage(dto);
        return ResultInfo.ok(pageInfoBT);
    }

    @ApiOperation(value = "作业回应")
    @PostMapping("/workReplyPage")
    public ResultInfo<PageInfoBT<LazyAppWorkInfoVO>> workReplyPage(@RequestBody @Validated WorkReplyPageDTO dto) {
        PageInfoBT<LazyAppWorkInfoVO> pageInfoBT = workService.workReplyPage(dto);
        return ResultInfo.ok(pageInfoBT);
    }

    @ApiOperation(value = "作业推荐心得")
    @PostMapping("/workRecommendPage")
    public ResultInfo<PageInfoBT<LazyAppWorkInfoVO>> workRecommendPage(@RequestBody @Validated WorkRecommendPageDTO dto) {
        PageInfoBT<LazyAppWorkInfoVO> pageInfoBT = workService.workRecommendPage(dto);
        return ResultInfo.ok(pageInfoBT);
    }

    @ApiOperation(value = "班级作业数据")
    @PostMapping("/classWorkData")
    public ResultInfo<ClassWorkDataVO> classWorkData(@RequestBody @Validated ClassWorkDataDTO dto) {
        ClassWorkDataVO vo = workService.classWorkData(dto);
        return ResultInfo.ok(vo);
    }

    @ApiOperation(value = "班级作业排行")
    @PostMapping("/classWorkRank")
    public ResultInfo<ClassWorkRankVO> classWorkRank(@RequestBody @Validated ClassWorkRankDTO dto) {
        ClassWorkRankVO vo = workService.classWorkRank(dto);
        return ResultInfo.ok(vo);
    }

    @ApiOperation(value = "个人作业数据")
    @PostMapping("/personalWorkData")
    public ResultInfo<PersonalWorkDataVO> personalWorkData(@RequestBody @Validated PersonalWorkDataDTO dto) {
        PersonalWorkDataVO vo = workService.personalWorkData(dto);
        return ResultInfo.ok(vo);
    }

    @ApiOperation(value = "个人作业排行")
    @PostMapping("/personalWorkRank")
    public ResultInfo<PersonalWorkRankVO> personalWorkRank(@RequestBody @Validated PersonalWorkRankDTO dto) {
        PersonalWorkRankVO vo = workService.personalWorkRank(dto);
        return ResultInfo.ok(vo);
    }

    @ApiOperation(value = "企业排行")
    @PostMapping("/companyRank")
    public ResultInfo<CompanyRankVO> companyRank(@RequestBody @Validated CompanyRankDTO dto) {
        CompanyRankVO vo = workService.companyRank(dto);
        return ResultInfo.ok(vo);
    }

    @ApiOperationSupport(order = 24)
    @ApiOperation(value = "app作业配置")
    @PostMapping(value = "appWorkConfig")
    public ResultInfo<AppWorkConfigVO> appWorkConfig(@RequestBody AppWorkConfigDTO dto) {
        return ResultInfo.ok(workService.appWorkConfig(dto));
    }

    @ApiOperation(value = "读书加分")
    @PostMapping("/readAddScore")
    public ResultInfo<Integer> readAddScore(@RequestBody @Validated ReadAddScoreDTO dto) {
        workService.readAddScore(dto);
        return ResultInfo.ok(1);
    }

    @ApiOperation(value = "作业详情承接页")
    @PostMapping("/oneWork")
    public ResultInfo<OneWorkVO> oneWork(@RequestBody @Validated OneWorkDTO dto) {
        OneWorkVO vo = workService.oneWork(dto);
        return ResultInfo.ok(vo);
    }

    @ApiOperation(value = "根据作业id查询")
    @PostMapping("/workById")
    public ResultInfo<WorkByIdVO> workById(@RequestBody @Validated IdDTO dto) {
        WorkByIdVO vo = workService.workById(dto);
        return ResultInfo.ok(vo);
    }

    @ApiOperation("小微大道-本周行动查询")
    @PostMapping("/curWeekWork")
    public ResultInfo<CurWeekWorkVO> curWeekWork(@RequestBody @Validated CurWeekWorkDTO dto) {
        return ok(workInfoService.curWeekWork(dto));
    }

    @ApiOperation("小微大道-记录今日思考")
    @PostMapping("/recordThink")
    @DistributedLock
    public ResultInfo<Integer> recordThink(@RequestBody @Validated RecordThinkDTO dto) {
        return ok(workInfoService.recordThink(dto));
    }

    @ApiOperation("小微大道-编辑今日思考")
    @PostMapping("/updateThink")
    @DistributedLock
    public ResultInfo<Integer> updateThink(@RequestBody @Validated RecordThinkDTO dto) {
        return ok(workInfoService.updateThink(dto));
    }

    @ApiOperation("小微大道-历史行动查询")
    @PostMapping("/historyWeekWork")
    public ResultInfo<PageInfoBT<HistoryWeekWorkItemVO>> historyWeekWork(@RequestBody @Validated HistoryWeekWorkDTO dto) {
        return ok(workInfoService.historyWeekWork(dto));
    }

    @ApiOperation(value = " flushToMongo")
    @PostMapping(value = "flushToMongo")
    public ResultInfo<Integer> flushToMongo(@RequestParam("workId") Integer workId) {
        workService.flushToMongo(workId);
        return ResultInfo.ok(1);
    }

}
