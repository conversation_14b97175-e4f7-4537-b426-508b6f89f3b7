package com.sibuqu.work.Interceptor;

import cn.hutool.core.map.MapUtil;
import com.sibuqu.base.common.result.BaseResultCode;
import com.sibuqu.base.common.result.ResultInfo;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.validation.BeanPropertyBindingResult;

import javax.servlet.http.HttpServletRequest;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * <AUTHOR> @date 2021-04-19 14:42
 * @description 切面验证接口Req参数 返回ResultInfo
 */
@Aspect
@Component
@Order(2)
public class AutoValidateParamAop {

    /**
     * 功能描述: 定义切入点
     */
    @Pointcut("execution(* com.sibuqu.work.controller..*(..))")
    public void cutPath() {
    }

    /**
     * 功能描述: 定义切入处理
     *
     * @Param: [joinPoint]
     * @Return: java.lang.Object
     * @Author: 
     * @Date: 2021/4/19 14:53
     */
    @Around("cutPath()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Object result = null;
        // 验证结果
        Set<Object> allParams = new LinkedHashSet<>();
        BeanPropertyBindingResult bindingResult = printParams(joinPoint, allParams);
        if (Objects.nonNull(bindingResult) && bindingResult.hasErrors()) {
            result = ResultInfo.error(BaseResultCode.ERROR.getCode(), bindingResult.getFieldErrors().stream()
                    .map(DefaultMessageSourceResolvable::getDefaultMessage)
                    .distinct()
                    .collect((Collectors.joining(","))));
        } else {
            //执行被拦截方法
            result = joinPoint.proceed();
        }
        return result;
    }

    /**
     * 功能描述: 组装请求参数
     *
     * @Param: [pjp, allParams]
     * @Return: org.springframework.validation.BeanPropertyBindingResult
     * @Author: 
     * @Date: 2021/4/19 14:55
     */
    private BeanPropertyBindingResult printParams(ProceedingJoinPoint pjp, Set<Object> allParams) {
        Object[] args = pjp.getArgs();
        BeanPropertyBindingResult bindingResult = null;
        for (Object arg : args) {
            if (arg instanceof BeanPropertyBindingResult) {
                bindingResult = (BeanPropertyBindingResult) arg;
            } else if (arg instanceof HttpServletRequest) {
                HttpServletRequest request = (HttpServletRequest) arg;
                Map<String, String[]> paramMap = request.getParameterMap();
                if (MapUtil.isNotEmpty(paramMap)) {
                    allParams.add(paramMap);
                }
            } else {
                allParams.add(arg);
            }
        }
        return bindingResult;
    }
}
