package com.sibuqu.work;

import org.mockito.Mockito;

import java.io.IOException;

import static org.mockito.Mockito.when;

public class WorkMockitoTestApplication {
    public static void main(String... args) throws IOException {
        WorkMockitoTestApplication origin = new WorkMockitoTestApplication();
        System.out.println(origin.hello());
        System.out.println("------ 分割线 -------");
        WorkMockitoTestApplication mock = Mockito.mock(WorkMockitoTestApplication.class);
        when(mock.hello()).thenReturn(1, 2);
        System.out.println(mock.hello());
        System.out.println(mock.hello());
        System.out.println(mock.hello());
    }

    public int hello(){
        System.out.println("hello world");
        return 0;
    }


}
