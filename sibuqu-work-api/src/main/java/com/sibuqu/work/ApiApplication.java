package com.sibuqu.work;

import org.mybatis.spring.annotation.MapperScan;
import org.mybatis.spring.annotation.MapperScans;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;

@SpringBootApplication(scanBasePackages = {"com.sibuqu.work","com.sibuqu.starter.mongohelper",
        "com.sibuqu.starter.redis","com.sibuqu.mysqlmultiple"},exclude = {DataSourceAutoConfiguration.class})
@EnableFeignClients
@MapperScans(value = {@MapperScan("com.sibuqu.work.mapper")})
public class ApiApplication {
    public static void main(String[] args) {
        SpringApplication.run(ApiApplication.class, args);
    }
}
