package com.sibuqu.work.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@ApiModel(description = "inno 周作业表")
@Data
@TableName(value = "work_inno_weekly_info")
public class WorkInnoWeeklyInfo implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    @TableField(value = "weekly_config_id")
    @ApiModelProperty(value = "周作业配置id")
    private Long weeklyConfigId;

    @TableField(value = "course_id")
    @ApiModelProperty(value = "课程ID")
    private Integer courseId;

    @TableField(value = "name")
    @ApiModelProperty(value = "名称")
    private String name;

    @TableField(value = "title")
    @ApiModelProperty(value = "标题")
    private String title;

    @TableField(value = "description")
    @ApiModelProperty(value = "描述")
    private String description;

    @TableField(value = "user_points")
    @ApiModelProperty(value = "用户积分")
    private Long userPoints;

    @TableField(value = "courseware_group_id")
    @ApiModelProperty(value = "课件分组ID")
    private Integer coursewareGroupId;

    @TableField(value = "weekly_begin_date")
    @ApiModelProperty(value = "周起始日期")
    private LocalDate weeklyBeginDate;

    @TableField(value = "weekly_end_date")
    @ApiModelProperty(value = "周截止日期")
    private LocalDate weeklyEndDate;

    @TableField(value = "work_begin_datetime")
    @ApiModelProperty(value = "周作业开始时间")
    private LocalDateTime workBeginDatetime;

    @TableField(value = "work_end_datetime")
    @ApiModelProperty(value = "周作业截止时间")
    private LocalDateTime workEndDatetime;

    @TableField(value = "create_user_id")
    @ApiModelProperty(value = "创建人ID")
    private Integer createUserId;

    @TableField(value = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @TableField(value = "update_user_id")
    @ApiModelProperty(value = "修改人ID")
    private Integer updateUserId;

    @TableField(value = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    @TableField(value = "update_time")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @TableField(value = "delete_flag")
    @ApiModelProperty(value = "删除状态 1-是 0-否")
    private Integer deleteFlag;

    private static final long serialVersionUID = 1L;
}
