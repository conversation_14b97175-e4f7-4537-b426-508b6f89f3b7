package com.sibuqu.work.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@ApiModel(description = "inno 作业表")
@Data
@TableName(value = "work_inno_info")
public class WorkInnoInfo implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    @TableField(value = "course_id")
    @ApiModelProperty(value = "课程id")
    private Integer courseId;

    @TableField(value = "course_timetable_id")
    @ApiModelProperty(value = "课件id")
    private Integer courseTimetableId;

    @TableField(value = "user_id")
    @ApiModelProperty(value = "用户id")
    private Integer userId;

    @TableField(value = "user_points")
    @ApiModelProperty(value = "用户积分")
    private Long userPoints;

    @TableField(value = "content")
    @ApiModelProperty(value = "内容")
    private String content;

    @TableField(value = "poster")
    @ApiModelProperty(value = "海报")
    private String poster;

    @TableField(value = "recommend_flag")
    @ApiModelProperty(value = "推荐标识 0-不推荐 1-推荐 2-历史推荐")
    private Integer recommendFlag;

    @TableField(value = "recommend_time")
    @ApiModelProperty(value = "推荐时间")
    private LocalDateTime recommendTime;

    @TableField(value = "create_user_id")
    @ApiModelProperty(value = "创建人ID")
    private Integer createUserId;

    @TableField(value = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @TableField(value = "update_user_id")
    @ApiModelProperty(value = "修改人ID")
    private Integer updateUserId;

    @TableField(value = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    @TableField(value = "update_time")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @TableField(value = "delete_flag")
    @ApiModelProperty(value = "删除状态 1-是 0-否")
    private Integer deleteFlag;

    private static final long serialVersionUID = 1L;
}
