package com.sibuqu.work.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 周目标
 */
@ApiModel(description = "周目标")
@Data
@TableName(value = "week_goal")
public class WeekGoal implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Integer id;

    @TableField(value = "course_id")
    @ApiModelProperty(value = "课程id")
    private Integer courseId;

    @TableField(value = "week_begin_date")
    @ApiModelProperty(value = "周开始时间")
    private LocalDate weekBeginDate;

    @TableField(value = "week_end_date")
    @ApiModelProperty(value = "周结束时间")
    private LocalDate weekEndDate;

    @TableField(value = "goal")
    @ApiModelProperty(value = "目标")
    private String goal;

    @TableField(value = "up_status")
    @ApiModelProperty(value = "上下架状态 1-上架 0-下架")
    private Integer upStatus;

    @TableField(value = "create_user_id")
    @ApiModelProperty(value = "创建人 id")
    private Integer createUserId;

    @TableField(value = "create_user_name")
    @ApiModelProperty(value = "创建人姓名")
    private String createUserName;

    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @TableField(value = "update_user_id")
    @ApiModelProperty(value = "更新人 id")
    private Integer updateUserId;

    @TableField(value = "update_user_name")
    @ApiModelProperty(value = "更新人姓名")
    private String updateUserName;

    @TableField(value = "update_time")
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @TableField(value = "delete_flag")
    @ApiModelProperty(value = "删除状态 1-是 0-否")
    private Integer deleteFlag;

    private static final long serialVersionUID = 1L;
}