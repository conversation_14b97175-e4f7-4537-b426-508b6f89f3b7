package com.sibuqu.work.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * user_study_info
 * <AUTHOR>
@ApiModel(value="UserStudyInfo")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("user_study_info")
public class UserStudyInfo implements Serializable {
    /**
     * 主键id
     */
    @ApiModelProperty(value="主键id")
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 用户id
     */
    @ApiModelProperty(value="用户id")
    private Integer userId;

    /**
     * 企业id
     */
    @ApiModelProperty(value="企业id")
    private Integer companyId;

    /**
     * 课程模版id
     */
    @ApiModelProperty(value="课程模版id")
    private Integer courseModelId;

    /**
     * 课程id
     */
    @ApiModelProperty(value="课程id")
    private Integer courseId;

    /**
     * 分数
     */
    @ApiModelProperty(value="分数")
    private Integer score;

    /**
     * 励志
     */
    @ApiModelProperty(value="励志")
    private String determin;

    /**
     * 敢愿
     */
    @ApiModelProperty(value="敢愿")
    private String hope;

    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createTime;
    /**
     * 总学分
     */
    @TableField(exist = false)
    private Integer learnScore;
    private static final long serialVersionUID = 1L;
}