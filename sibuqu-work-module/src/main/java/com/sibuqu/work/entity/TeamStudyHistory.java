package com.sibuqu.work.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * team_study_history
 * <AUTHOR>
@ApiModel(value="TeamStudyHistory班级历史作业情况")
@Data
public class TeamStudyHistory implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty(value="主键")
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 学习人数
     */
    @ApiModelProperty(value="学习人数")
    private Integer listenNum;

    /**
     * 做作业人数
     */
    @ApiModelProperty(value="做作业人数")
    private Integer doWorkNum;

    /**
     * 得分
     */
    @ApiModelProperty(value="得分")
    private Integer score;

    /**
     * 班级号
     */
    @ApiModelProperty(value="班级号")
    private Integer classesId;

    /**
     * 课程模版id
     */
    @ApiModelProperty(value="课程模版id")
    private LocalDateTime courseModelId;

    /**
     * 课程id
     */
    @ApiModelProperty(value="课程id")
    private Integer courseId;

    /**
     * 课程表id
     */
    @ApiModelProperty(value="课程表id")
    private Integer courseTimetableId;

    /**
     * 资源id
     */
    @ApiModelProperty(value="资源id")
    private Integer resourceId;

    /**
     * 课程开始时间
     */
    @ApiModelProperty(value="课程开始时间")
    private LocalDateTime courseBeginTime;

    private static final long serialVersionUID = 1L;
}