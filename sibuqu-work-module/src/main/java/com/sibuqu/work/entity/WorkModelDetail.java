package com.sibuqu.work.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * 作业模版作业明细项信息表
 */
@ApiModel(description = "作业模版作业明细项信息表")
@Data
@TableName(value = "work_model_detail")
public class WorkModelDetail implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "id")
    private Integer id;

    @TableField(value = "work_model_id")
    @ApiModelProperty(value = "作业模版id")
    private Integer workModelId;

    @TableField(value = "title")
    @ApiModelProperty(value = "作业项名称")
    private String title;

    @TableField(value = "`type`")
    @ApiModelProperty(value = "作业项类型 1-听课类型 2-打卡 3-文本 4-数字 5-评价 6-无打卡只显示提示 7-附加作业 8-选择题 9-打卡不加分 10-笃行 11-分享加分 12-多选题 13-NPS 14-语音加文本 15-动态多选题 16-动态单选题 17-读书")
    private Integer type;

    @TableField(value = "prompt")
    @ApiModelProperty(value = "作业提示")
    private String prompt;

    @TableField(value = "description")
    @ApiModelProperty(value = "描述")
    private String description;

    @TableField(value = "description_one")
    @ApiModelProperty(value = "描述一")
    private String descriptionOne;

    @TableField(value = "description_two")
    @ApiModelProperty(value = "描述二")
    private String descriptionTwo;

    @TableField(value = "max_length")
    @ApiModelProperty(value = "最大值")
    private Integer maxLength;

    @TableField(value = "min_length")
    @ApiModelProperty(value = "最小值")
    private Integer minLength;

    @TableField(value = "manual_status")
    @ApiModelProperty(value = "是否可编辑1可手动编辑2不可手动编辑")
    private Integer manualStatus;

    @TableField(value = "show_status")
    @ApiModelProperty(value = "展示状态0不展示1展示")
    private Integer showStatus;

    @TableField(value = "show_title")
    @ApiModelProperty(value = "展示标题")
    private String showTitle;

    @TableField(value = "option_json")
    @ApiModelProperty(value = "选项json")
    private String optionJson;

    @TableField(value = "show_style")
    @ApiModelProperty(value = "1-复选框 2-小红花")
    private Integer showStyle;

    @TableField(value = "ebook_json")
    @ApiModelProperty(value = "书籍json")
    private String ebookJson;

    @TableField(value = "score")
    @ApiModelProperty(value = "分值")
    private Integer score;

    @TableField(value = "sorted")
    @ApiModelProperty(value = "排序")
    private Integer sorted;

    @TableField(value = "default_value")
    @ApiModelProperty(value = "默认值")
    private Integer defaultValue;

    @TableField(value = "create_user_id")
    @ApiModelProperty(value = "创建人id")
    private Integer createUserId;

    @TableField(value = "update_user_id")
    @ApiModelProperty(value = "操作人id")
    private Integer updateUserId;

    @TableField(value = "deleted")
    @ApiModelProperty(value = "是否删除 1:未删除 0:删除")
    private Integer deleted;

    @TableField(value = "words")
    @ApiModelProperty(value = "词")
    private String words;

    @TableField(value = "answer")
    @ApiModelProperty(value = "选择题答案")
    private String answer;

    @TableField(value = "share_image_url")
    @ApiModelProperty(value = "分享图链接")
    private String shareImageUrl;

    @TableField(value = "share_status")
    @ApiModelProperty(value = "分享状态：1支持分享0不支持")
    private Integer shareStatus;

    private static final long serialVersionUID = 1L;
}