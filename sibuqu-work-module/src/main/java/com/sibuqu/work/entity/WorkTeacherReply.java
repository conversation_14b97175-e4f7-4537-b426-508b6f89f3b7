package com.sibuqu.work.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 老师回复表
 * </p>
 *
 * <AUTHOR>
 * @since 2022年5月18日
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("work_teacher_reply")
@ApiModel(value = "WorkTeacherReply", description = "老师回复表")
public class WorkTeacherReply implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty("课程ID")
    @TableField("course_id")
    private Integer courseId;

    @ApiModelProperty("作业ID")
    @TableField("work_id")
    private Integer workId;

    @ApiModelProperty("老师ID")
    @TableField("teacher_user_id")
    private Integer teacherUserId;

    @ApiModelProperty("回复内容")
    @TableField("teacher_content")
    private String teacherContent;

    @ApiModelProperty("老师回复内容类型（0文字  1语音）")
    @TableField("teacher_reply_type")
    private Integer teacherReplyType;

    @ApiModelProperty("数据来源(0.app  1.pc)")
    @TableField("source_type")
    private Integer sourceType;

    @ApiModelProperty("回复时间")
    @TableField("teacher_reply_time")
    private LocalDateTime teacherReplyTime;

    @ApiModelProperty("音频文件时长")
    @TableField("audio_time_length")
    private Integer audioTimeLength;

    @ApiModelProperty("音频文件大小")
    @TableField("audio_size")
    private Integer audioSize;

    @ApiModelProperty("删除状态（1是 0否）")
    @TableField("delete_flag")
    private Integer deleteFlag;

    @ApiModelProperty("回复用户类型 1 老师 2企业管理员")
    private Integer replyUserType;

    @ApiModelProperty("用户名")
    private String userName;
    @ApiModelProperty("推荐状态：是否推荐1推荐0不推荐")
    private Integer recommendStatus;
}
