package com.sibuqu.work.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 用户选择的作业模板
 */
@ApiModel(description = "用户选择的作业模板")
@Data
@TableName(value = "work_user_model")
public class WorkUserModel implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    @TableField(value = "user_id")
    @ApiModelProperty(value = "用户id")
    private Integer userId;

    @TableField(value = "course_id")
    @ApiModelProperty(value = "课程id")
    private Integer courseId;

    @TableField(value = "`level`")
    @ApiModelProperty(value = "作业模板等级 a b c")
    private String level;

    @TableField(value = "work_model_id")
    @ApiModelProperty(value = "学员作业模板ID")
    private Integer workModelId;

    @TableField(value = "work_model_name")
    @ApiModelProperty(value = "学员作业模板名称")
    private String workModelName;

    @TableField(value = "create_user_id")
    @ApiModelProperty(value = "创建人id")
    private Integer createUserId;

    @TableField(value = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @TableField(value = "update_user_id")
    @ApiModelProperty(value = "修改人id")
    private Integer updateUserId;

    @TableField(value = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    @TableField(value = "update_time")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @TableField(value = "delete_flag")
    @ApiModelProperty(value = "删除状态 1-是 0-否")
    private Integer deleteFlag;

    private static final long serialVersionUID = 1L;
}