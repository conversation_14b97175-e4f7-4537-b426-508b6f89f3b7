package com.sibuqu.work.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

@ApiModel(description = "inno 周作业配置表")
@Data
@TableName(value = "work_inno_weekly_config")
public class WorkInnoWeeklyConfig implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    @TableField(value = "course_id")
    @ApiModelProperty(value = "课程ID")
    private Integer courseId;

    @TableField(value = "name")
    @ApiModelProperty(value = "名称")
    private String name;

    @TableField(value = "user_points")
    @ApiModelProperty(value = "用户积分")
    private Long userPoints;

    @TableField(value = "course_begin_date")
    @ApiModelProperty(value = "课程起始日期")
    private LocalDate courseBeginDate;

    @TableField(value = "course_end_date")
    @ApiModelProperty(value = "课程截止日期")
    private LocalDate courseEndDate;

    @TableField(value = "work_begin_weekday")
    @ApiModelProperty(value = "周作业开始时间-星期几（1-7）")
    private Integer workBeginWeekday;

    @TableField(value = "work_begin_time")
    @ApiModelProperty(value = "周作业开始时间-几点几分")
    private LocalTime workBeginTime;

    @TableField(value = "work_end_weekday")
    @ApiModelProperty(value = "周作业截止时间-星期几（1-7）")
    private Integer workEndWeekday;

    @TableField(value = "work_end_time")
    @ApiModelProperty(value = "周作业截止时间-几点几分")
    private LocalTime workEndTime;

    @TableField(value = "create_user_id")
    @ApiModelProperty(value = "创建人ID")
    private Integer createUserId;

    @TableField(value = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @TableField(value = "create_time")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @TableField(value = "update_user_id")
    @ApiModelProperty(value = "修改人ID")
    private Integer updateUserId;

    @TableField(value = "update_user_name")
    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    @TableField(value = "update_time")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @TableField(value = "delete_flag")
    @ApiModelProperty(value = "删除状态 1-是 0-否")
    private Integer deleteFlag;

    private static final long serialVersionUID = 1L;
}
