package com.sibuqu.work.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * team_study_statistics
 * <AUTHOR>
@ApiModel(value="TeamStudyStatistics每日作业提交统计")
@Data
public class TeamStudyStatistics implements Serializable {
    /**
     * id
     */
    @ApiModelProperty(value="id")
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 课程模版id
     */
    @ApiModelProperty(value="课程模版id")
    private Integer courseModelId;

    /**
     * 课程表id
     */



    /**
     * 资源id
     */
    @ApiModelProperty(value="资源id")
    private Integer resourceId;

    /**
     * 班级号
     */
    @ApiModelProperty(value="班级号")
    private Integer classesId;

    /**
     * 课程id
     */
    @ApiModelProperty(value="课程id")
    private Integer courseId;

    /**
     * 当次提交作业率
     */
    @ApiModelProperty(value="当次提交作业率")
    private String currentSubRate;

    /**
     * 历史提交作业率
     */
    @ApiModelProperty(value="历史提交作业率")
    private String historySubRate;

    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createTime;

    /**
     * 班级总人数
     */
    @ApiModelProperty(value="班级总人数")
    private Integer classMemberTotal;

    /**
     * 提交作业人数
     */
    @ApiModelProperty(value="提交作业人数")
    private Integer subTotal;

    /**
     * 未提交作业人名
     */
    @ApiModelProperty(value="未提交作业人名")
    private String unsubUserName;

    /**
     * 当次听课率
     */
    @ApiModelProperty(value="当次听课率")
    private String currentListenRate;

    /**
     * 历史听课率
     */
    @ApiModelProperty(value="历史听课率")
    private String historyListenRate;

    /**
     * 听课人数
     */
    @ApiModelProperty(value="听课人数")
    private Integer listenTotal;

    /**
     * 未听课人数
     */
    @ApiModelProperty(value="未听课人数")
    private Integer unlistenTotal;

    /**
     * 课程开始时间
     */
    @ApiModelProperty(value="课程开始时间")
    private LocalDateTime courseBeginTime;

    private static final long serialVersionUID = 1L;
}