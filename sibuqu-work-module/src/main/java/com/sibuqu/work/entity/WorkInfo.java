package com.sibuqu.work.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * work_info
 *
 * <AUTHOR>
@ApiModel(value = "WorkInfo")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WorkInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private Integer userId;

    /**
     * 课程表id
     */
    @ApiModelProperty(value = "课程表id")
    private Integer courseTimetableId;

    /**
     * 作业模版id
     */
    @ApiModelProperty(value = "作业模版id")
    private Integer workModelId;

    /**
     * 课程母版id
     */
    @ApiModelProperty(value = "课程母版id")
    private Integer courseModelId;

    /**
     * 课程id
     */
    @ApiModelProperty(value = "课程id")
    private Integer courseId;

    /**
     * 课程开始时间
     */
    @ApiModelProperty(value = "课程开始时间")
    private LocalDateTime courseBeginTime;

    /**
     * 课程名称
     */
    @ApiModelProperty(value = "课程名称")
    private String courseName;

    /**
     * 企业id
     */
    @ApiModelProperty(value = "企业id")
    private Integer companyId;

    /**
     * 企业编号
     */
    @ApiModelProperty(value = "企业编号")
    private String companyCode;

    /**
     * 企业名称
     */
    @ApiModelProperty(value = "企业名称")
    private String companyName;

    /**
     * 班级id
     */
    @ApiModelProperty(value = "班级id")
    private Integer classesId;

    /**
     * 班级号
     */
    @ApiModelProperty(value = "班级号")
    private Integer classesNo;

    /**
     * 班级名称
     */
    @ApiModelProperty(value = "班级名称")
    private String classesName;

    /**
     * 班主任手机号
     */
    @ApiModelProperty(value = "班主任手机号")
    private String classesTeacherPhone;

    /**
     * 小组id
     */
    @ApiModelProperty(value = "小组id")
    private Integer teamId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    private LocalDateTime updateTime;

    /**
     * 作业总分
     */
    @ApiModelProperty(value = "作业总分")
    private Integer score;

    /**
     * 用户手机号
     */
    @ApiModelProperty(value = "用户手机号")
    private String userPhone;

    /**
     * 用户姓名
     */
    @ApiModelProperty(value = "用户姓名")
    private String userName;

    /**
     * 作业状态1已写 0未写
     */
    @ApiModelProperty(value = "作业状态1已写 0未写")
    private Integer workStatus;


    @ApiModelProperty(value = "听课状态 1已听 0未听")
    private Integer listenStatus;
    /**
     * 小组名称
     */
    @ApiModelProperty(value = "小组名称")
    private String teamName;

    @ApiModelProperty(value = "内容")
    private String content;
    /**
     * 作业时间
     */
    @ApiModelProperty(value = "作业时间")
    private LocalDateTime workTime;

    @ApiModelProperty(value = "是否推荐0未推荐1已推荐")
    private Integer recommendStatus;

    @ApiModelProperty(value = "推荐时间")
    private LocalDateTime recommendTime;

    @ApiModelProperty(value = "是否推荐给老师0未推荐1已推荐")
    private Integer recommendTeacherStatus;

    @ApiModelProperty(value = "推荐给老师的时间")
    private LocalDateTime recommendTeacherTime;

    @ApiModelProperty(value = "老师回复状态")
    private Integer teacherReplyStatus;

    @ApiModelProperty(value = "心得列表是否要展示1展示0不展示")
    private Integer contentShowStatus;

    @ApiModelProperty(value = "0手动听课打卡1自动听课打卡")
    private Integer automatic;

    @ApiModelProperty(value = "附加作业信息")
    private String additional;

    @ApiModelProperty(value = "显示状态0隐藏 1显示")
    private Integer showStatus;

    @ApiModelProperty("作业模版标识（0-统一配置作业模版 1-区分老学员配置作业模版 2-分等级作业模板）")
    private Integer workModelFlag;

    @ApiModelProperty("用户标识0新学员1老学员")
    private Integer userFlag;

    @ApiModelProperty("企业回复标识")
    private Integer companyReplyStatus;

    @ApiModelProperty("企业回复时间")
    private LocalDateTime companyReplyTime;

    @ApiModelProperty(value = "推荐来源1管理后台2企业管理员3班主任")
    private Integer recommendSource;

    @ApiModelProperty(value = "老师回复时间")
    private LocalDateTime teacherReplyTime;

    @ApiModelProperty("课件名称")
    private String resourceTitle;

    @ApiModelProperty(value = "班主任名称", dataType = "String")
    private String classesTeacherName;

    @ApiModelProperty(value = "作业模板等级 a b c")
    private String level;

    @ApiModelProperty(value = "分享状态 0-未分享 1-已分享")
    private Integer shareStatus;

}