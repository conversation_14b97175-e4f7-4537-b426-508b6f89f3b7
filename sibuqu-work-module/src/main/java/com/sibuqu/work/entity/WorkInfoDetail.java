package com.sibuqu.work.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * work_info_detail
 * <AUTHOR>
@ApiModel(value="WorkInfoDetail")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WorkInfoDetail implements Serializable {
    /**
     * 主键id
     */
    @ApiModelProperty(value="主键id")
    @TableId(type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "作业项类型 1-听课类型 2-打卡 3-文本 4-数字 5-评价 6-无打卡只显示提示 7-附加作业 8-选择题 9-打卡不加分 10-笃行 11-分享加分 12-多选题 13-NPS 14-语音加文本 15-动态多选题 16-动态单选题 17-读书")
    private Integer type;

    /**
     * 描述
     */
    @ApiModelProperty(value="描述")
    private String description;

    /**
     * 作业模版明细id
     */
    @ApiModelProperty(value="作业模版明细id")
    private Integer workModelDetailId;

    /**
     * 作业内容
     */
    @ApiModelProperty(value="作业内容")
    private String content;

    /**
     * 作业id
     */
    @ApiModelProperty(value="作业id")
    private Integer workId;

    /**
     * 作业项得分
     */
    @ApiModelProperty(value="作业项得分")
    private Integer score;

    @ApiModelProperty(value="排序")
    private Integer sorted;
    private static final long serialVersionUID = 1L;
}