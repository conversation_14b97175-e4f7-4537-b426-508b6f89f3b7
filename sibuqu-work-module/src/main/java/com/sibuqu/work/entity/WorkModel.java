package com.sibuqu.work.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * work_model
 * <AUTHOR>
@ApiModel(value="WorkModel")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WorkModel implements Serializable {
    /**
     * 主键ID
     */
    @ApiModelProperty(value="主键ID")
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 作业模版编号
     */
    @ApiModelProperty(value="作业模版编号")
    private String modelCode;
    /**
     * 模版名称
     */
    @ApiModelProperty(value="模版名称")
    private String modelTitle;

    /**
     * 创建人id
     */
    @ApiModelProperty(value="创建人id")
    private Integer createUserId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createTime;

    /**
     * 操作人id
     */
    @ApiModelProperty(value="操作人id")
    private Integer updateUserId;

    /**
     * 操作时间
     */
    @ApiModelProperty(value="操作时间")
    private LocalDateTime updateTime;

    private static final long serialVersionUID = 1L;
}