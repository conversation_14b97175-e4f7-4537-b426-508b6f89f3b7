package com.sibuqu.work.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DoWorkSendKafkaBO {
    @ApiModelProperty("用户id")
    private Integer userId;
    @ApiModelProperty("企业id")
    private Integer companyId;
    @ApiModelProperty("班级id")
    private Integer classesId;
    @ApiModelProperty("课程id")
    private Integer courseId;
    @ApiModelProperty("小组id")
    private Integer teamId;
    @ApiModelProperty("听课状态")
    private Boolean listenStatus;
    @ApiModelProperty("作业状态")
    private Boolean workStatus;
    @ApiModelProperty("课程表id")
    private Integer courseTimeTableId;
    private Long ts;
    /**
     * 消息tag
     * 写作业（doWork）
     * 听课（listen）
     * 听课统计（listenStatistics）
     */
    @ApiModelProperty("消息tag")
    private String tag;
    @ApiModelProperty("分数")
    private Integer score;

}
