package com.sibuqu.work.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 班级动态 kafka消息体BO
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ClassesMemberDynamicKafkaBO {

    /**
     * 消息tag
     * 学员进班（enterClasses）
     * 学员退班（exitClasses）
     * 学员进组（enterTeam）
     * 学员退组（exitTeam）
     */
    private String tag;

    @ApiModelProperty("企业id")
    private Integer companyId;

    @ApiModelProperty("课程id")
    private Integer courseId;

    @ApiModelProperty("用户id")
    private Integer userId;

    @ApiModelProperty("原班级id")
    private Integer oldClassesId;

    @ApiModelProperty("新班级id")
    private Integer newClassesId;

    @ApiModelProperty("原小组id")
    private Integer oldTeamId;

    @ApiModelProperty("新小组id")
    private Integer newTeamId;

    private Long ts;

}
