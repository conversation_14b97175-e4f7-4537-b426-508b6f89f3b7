package com.sibuqu.work.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CourseTimetableHistoryBO {
    /**
     * 课件精华
     */
    private String cream;
    /**
     * 课程表id
     */
    private Integer id;
    @ApiModelProperty("企业ID")
    @TableField("company_id")
    private Integer companyId;
    @ApiModelProperty("课程母版ID")
    @TableField("course_model_id")
    private Integer courseModelId;
    @ApiModelProperty("课程ID")
    @TableField("course_id")
    private Integer courseId;
    @ApiModelProperty("课程资源ID")
    @TableField("resource_id")
    private Integer resourceId;
    @ApiModelProperty("课程资源名称")
    @TableField("resource_title")
    private String resourceTitle;
    @ApiModelProperty("课程资源提示")
    @TableField("resource_tips")
    private String resourceTips;
    @ApiModelProperty("课程开始时间")
    @TableField("course_begin_time")
    private String courseBeginTime;
    @ApiModelProperty("课程结束时间")
    @TableField("course_end_time")
    private String courseEndTime;
    @ApiModelProperty("作业开始时间 ")
    @TableField("work_begin_time")
    private String workBeginTime;
    @ApiModelProperty("作业结束时间")
    @TableField("work_end_time")
    private String workEndTime;
    @ApiModelProperty("正课标识（1正课 2非正课）")
    @TableField("normal_course_flag")
    private Integer normalCourseFlag;
    @ApiModelProperty("创建人ID")
    @TableField("create_user_id")
    private Integer createUserId;
    @ApiModelProperty("创建人名称")
    @TableField("create_user_name")
    private String createUserName;
    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private String createTime;
    @ApiModelProperty("修改人ID")
    @TableField("update_user_id")
    private Integer updateUserId;
    @ApiModelProperty("修改人名称")
    @TableField("update_user_name")
    private String updateUserName;
    @ApiModelProperty("修改时间")
    @TableField("update_time")
    private String updateTime;
    @ApiModelProperty("排序")
    @TableField("sort_num")
    private Integer sortNum;
    @ApiModelProperty("是否支持试看（1是 0否）")
    @TableField("try_see_flag")
    private Integer trySeeFlag;
    @ApiModelProperty("状态（1显示 2隐藏）")
    @TableField("data_flag")
    private Integer dataFlag;
    @ApiModelProperty("删除状态（1是 0否）")
    @TableField("delete_flag")
    @TableLogic
    private Integer deleteFlag;
}
