package com.sibuqu.work.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class WorkLevelBO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("表单数据id(每新填写一次就是一个id)")
    private String id;

    @ApiModelProperty("用户id")
    private Integer userId;

    @ApiModelProperty("模板id")
    private String templateId;

    // 下面俩暂时用不着
    @ApiModelProperty("firstShareId")
    private String firstShareId;

    @ApiModelProperty("lastShareId")
    private String lastShareId;

}
