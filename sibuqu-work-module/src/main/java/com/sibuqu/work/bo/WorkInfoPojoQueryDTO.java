package com.sibuqu.work.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

@Data
@Accessors(chain = true)
public class WorkInfoPojoQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("第几页")
    private int pageNum = 1;

    @ApiModelProperty("分页大小")
    private int pageSize = 10;

    @ApiModelProperty(value = "企业id")
    private Integer companyId;

    @ApiModelProperty(value = "课程id")
    private Integer courseId;

    @ApiModelProperty(value = "课件id")
    private Integer courseTimetableId;

    @ApiModelProperty(value = "课件id列表")
    private List<Integer> courseTimetableIdList;

    @ApiModelProperty(value = "作业状态1已写 0未写")
    private Integer workStatus;

    @ApiModelProperty(value = "用户id")
    private Integer userId;

    @ApiModelProperty(value = "用户id列表")
    private List<Integer> userIdList;

    @ApiModelProperty(value = "班级号")
    private Integer classesNo;

    @ApiModelProperty(value = "是否推荐0未推荐1已推荐")
    private Integer recommendStatus;

    @ApiModelProperty(value="用户手机号")
    private String userPhone;

    @ApiModelProperty(value = "显示状态0隐藏 1显示")
    private Integer showStatus;

    @ApiModelProperty(value = "心得列表是否要展示1展示0不展示")
    private Integer contentShowStatus;

    @ApiModelProperty("企业回复标识")
    private Integer companyReplyStatus;

    @ApiModelProperty("班级ID")
    private Integer classesId;

    @ApiModelProperty(value = "小组id")
    private Integer teamId;

}
