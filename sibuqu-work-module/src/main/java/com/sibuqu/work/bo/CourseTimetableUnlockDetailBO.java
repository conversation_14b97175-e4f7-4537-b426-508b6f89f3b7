package com.sibuqu.work.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2021/12/16 18:42
 * @Description 课程表资源解锁详情VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CourseTimetableUnlockDetailBO {

    @ApiModelProperty("课程ID")
    private Integer courseId;

    @ApiModelProperty("课程标题")
    private String courseTitle;

    @ApiModelProperty("课程表ID")
    private Integer id;

    @ApiModelProperty("资源ID")
    private Integer resourceId;

    @ApiModelProperty("资源标题")
    private String resourceTitle;

    @ApiModelProperty("课件精华")
    private String cream;

    @ApiModelProperty("作业模板ID")
    private Integer workModelId;

    @ApiModelProperty("作业开始时间 ")
    private String workBeginTime;

    @ApiModelProperty("作业结束时间")
    private String workEndTime;

}
