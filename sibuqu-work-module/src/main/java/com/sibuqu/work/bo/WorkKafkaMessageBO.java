package com.sibuqu.work.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkKafkaMessageBO {
    /**
     * 消息tag
     * 写作业（doWork）
     * 听课（listen）
     * 听课统计（listenStatistics）
     */
    private String tag;
    /**
     * 消息体
     */
    private DoWorkSendKafkaBO doWorkSendKafkaBO;
}
