package com.sibuqu.work.bo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "内容BO")
@Data
public class ContentBO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("内容类型 1-文本 2-图片 3-视频 4-语音")
    private Integer type;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("封面图")
    private String cover;

    @ApiModelProperty("时长")
    private Integer time;

    @ApiModelProperty("文件大小")
    private Integer size;

}