package com.sibuqu.work.dto;

import com.sibuqu.base.common.page.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class TeamWorkStatisticsDTO extends PageBase {
    @ApiModelProperty(value="课程id")
    private String courseId;
    @ApiModelProperty(value="企业编号")
    private String companyCode;
    @ApiModelProperty(value="企业名称")
    private String companyName;
    @ApiModelProperty(value="编辑编号")
    private String classesNo;
    @ApiModelProperty(value="班主任手机")
    private String classTeacherPhone;
    @ApiModelProperty(value="开始时间")
    private LocalDateTime startTime;
    @ApiModelProperty(value="截止时间")
    private LocalDateTime endTime;
}
