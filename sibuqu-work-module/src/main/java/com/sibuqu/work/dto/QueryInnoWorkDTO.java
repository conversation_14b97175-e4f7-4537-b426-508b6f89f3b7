package com.sibuqu.work.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class QueryInnoWorkDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "课程id", required = true)
    @NotNull(message = "课程id不能为空")
    private Integer courseId;

    @ApiModelProperty(value = "课件id", required = true)
    @NotNull(message = "课件id不能为空")
    private Integer courseTimetableId;
}