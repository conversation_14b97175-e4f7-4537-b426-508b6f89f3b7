package com.sibuqu.work.dto;

import com.sibuqu.base.common.page.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class UserWorkStatisticsDTO extends PageBase {
    @ApiModelProperty(value="课程id")
    private String courseId;
    @ApiModelProperty(value="企业id")
    private String companyId;
    @ApiModelProperty(value="企业编号")
    private String companyCode;
    @ApiModelProperty(value="企业名称")
    private String companyName;
    @ApiModelProperty(value="小组名称")
    private String teamName;
    @ApiModelProperty(value="用户手机号")
    private String userPhone;
    @ApiModelProperty(value="班级编号")
    private String classesNo;
    @ApiModelProperty(value="班主任手机")
    private String classesTeacherPhone;
    @ApiModelProperty(value="开始日期")
    private LocalDate startDate;
    @ApiModelProperty(value="截止日期")
    private LocalDate endDate;
}
