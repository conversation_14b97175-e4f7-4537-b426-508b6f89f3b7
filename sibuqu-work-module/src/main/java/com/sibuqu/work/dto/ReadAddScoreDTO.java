package com.sibuqu.work.dto;

import com.sibuqu.work.vo.admin.AppChapterVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ReadAddScoreDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    @ApiModelProperty("电子书ID")
    private Long ebookId;

    @ApiModelProperty(value="电子书名字")
    private String ebookName;

    @ApiModelProperty(value = "章节信息")
    private List<AppChapterVO> chapterList;

    @ApiModelProperty("用户id")
    private Integer userId;

    @ApiModelProperty("读书记录id")
    private Long readRecordId;

    @ApiModelProperty(value="课程表id")
    private Integer courseTimetableId;

}
