package com.sibuqu.work.dto;

import com.sibuqu.base.common.page.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2022年5月17日16:08:39
 * @Version 1.0
 * @Description 老师回复心得列表查询参数DTO  特殊说明：因为APP端想改动最小化  暂时所有字段用的老代码字段 做映射
 **/
@Data
public class AppWorkReplySearchDTO extends PageBase {

//    @ApiModelProperty(value="班级ID")
//    @NotNull(message="班级ID不能为空")
//    private Integer groupId;

    @ApiModelProperty(value="课程ID")
    @NotNull(message="课程ID不能为空")
    private Integer courseId;

}
