package com.sibuqu.work.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@ApiModel(description = "编辑inno周作业DTO")
@Data
public class EditInnoWeeklyInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "周作业ID", required = true)
    @NotNull(message = "周作业ID不能为空")
    private Long id;

    @ApiModelProperty(value = "名称", required = true)
    @NotBlank(message = "名称不能为空")
    private String name;

    @ApiModelProperty(value = "标题", required = true)
    @NotBlank(message = "标题不能为空")
    private String title;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "周起始日期", required = true)
    @NotNull(message = "周起始日期不能为空")
    private LocalDate weeklyBeginDate;

    @ApiModelProperty(value = "周截止日期", required = true)
    @NotNull(message = "周截止日期不能为空")
    private LocalDate weeklyEndDate;

    @ApiModelProperty(value = "周作业开始时间", required = true)
    @NotNull(message = "周作业开始时间不能为空")
    private LocalDateTime workBeginDatetime;

    @ApiModelProperty(value = "周作业截止时间", required = true)
    @NotNull(message = "周作业截止时间不能为空")
    private LocalDateTime workEndDatetime;

    @ApiModelProperty(value = "课件分组ID")
    private Integer coursewareGroupId;
}