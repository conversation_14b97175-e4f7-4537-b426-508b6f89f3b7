package com.sibuqu.work.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

/**
 * 后台管理系统 作业推送给老师
 * <AUTHOR>
 */
@Data
public class WorkInfoRecommendTeacherDTO  {
    @NotNull(message="作业的id")
    @ApiModelProperty(value="作业的id")
    private Integer id;

    @ApiModelProperty(value="是否推送给老师 0取消推荐 1推荐")
    @Range(min = 0,max = 1,message = "只能是 0取消推荐 1推荐")
    private Integer recommendTeacherStatus;


}
