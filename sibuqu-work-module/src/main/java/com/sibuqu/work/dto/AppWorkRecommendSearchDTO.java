package com.sibuqu.work.dto;

import com.sibuqu.base.common.page.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2022年5月17日16:08:39
 * @Version 1.0
 * @Description 推荐作业列表搜索DTO  特殊说明：因为APP端想改动最小化  暂时所有字段用的老代码字段 做映射
 **/
@Data
public class AppWorkRecommendSearchDTO extends PageBase {

    @ApiModelProperty(value="课程ID")
    private Integer courseId;

    @ApiModelProperty(value = "老师回复状态（-1全部 1已回复 0未回复）",dataType = "Integer")
    private Integer replyStatus;

}
