package com.sibuqu.work.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalTime;

@ApiModel(description = "新增inno周作业配置DTO")
@Data
public class AddInnoWeeklyConfigDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "名称", required = true)
    @NotBlank(message = "名称不能为空")
    private String name;

    @ApiModelProperty(value = "课程ID", required = true)
    @NotNull(message = "课程不能为空")
    private Integer courseId;

    @ApiModelProperty(value = "能量果", required = true)
    @NotNull(message = "能量果不能为空")
    private Long userPoints;

    @ApiModelProperty(value = "课程起始日期", required = true)
    @NotNull(message = "课程起始日期不能为空")
    private LocalDate courseBeginDate;

    @ApiModelProperty(value = "课程截止日期", required = true)
    @NotNull(message = "课程截止日期不能为空")
    private LocalDate courseEndDate;

    @ApiModelProperty(value = "周作业开始时间-星期几（1-7）", required = true)
    @NotNull(message = "周作业开始星期不能为空")
    private Integer workBeginWeekday;

    @ApiModelProperty(value = "周作业开始时间-几点几分", required = true)
    @NotNull(message = "周作业开始时间不能为空")
    private LocalTime workBeginTime;

    @ApiModelProperty(value = "周作业截止时间-星期几（1-7）", required = true)
    @NotNull(message = "周作业截止星期不能为空")
    private Integer workEndWeekday;

    @ApiModelProperty(value = "周作业截止时间-几点几分", required = true)
    @NotNull(message = "周作业截止时间不能为空")
    private LocalTime workEndTime;
}