package com.sibuqu.work.dto;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@ApiModel(description = "管理后台查询inno周作业信息分页DTO")
@Data
public class QueryInnoWeeklyInfoPageDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "页码", required = true)
    @NotNull(message = "页码不能为空")
    private Integer pageNum;

    @ApiModelProperty(value = "每页条数", required = true)
    @NotNull(message = "每页条数不能为空")
    private Integer pageSize;

    @ApiModelProperty(value = "周作业配置id")
    @NotNull(message = "周作业配置id不能为空")
    private Long weeklyConfigId;

    @ApiModelProperty(value = "状态：1-已开始，0-未开始")
    private Integer status;

}