package com.sibuqu.work.dto;

import com.sibuqu.work.vo.admin.AppOptionVO;
import com.sibuqu.work.vo.admin.WorkModelAudioVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class DoWorkDetailDTO {
    /**
     * id
     */
    @ApiModelProperty(value="id")
    private Integer id;

    @ApiModelProperty(value="作业项类型 1-听课类型 2-打卡 3-文本 4-数字 5-评价 6-无打卡只显示提示 7-附加作业 8-选择题 9-打卡不加分 10-笃行 11-分享加分 12-多选题 13-NPS 14-语音加文本 15-动态多选题 16-动态单选题 17-读书")
    private Integer type;
    @ApiModelProperty(value="作业内容")
    private String content;

    @ApiModelProperty("读书记录id")
    private Long readRecordId;

    @ApiModelProperty(value="作业语音内容")
    private List<WorkModelAudioVO> audioList;

    @ApiModelProperty(value = "选项列表")
    private List<AppOptionVO> appOptionList;

}
