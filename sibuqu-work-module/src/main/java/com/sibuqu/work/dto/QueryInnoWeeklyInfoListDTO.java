package com.sibuqu.work.dto;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;

import lombok.Data;

import java.io.Serializable;

@Data
public class QueryInnoWeeklyInfoListDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "课程id", required = true)
    @NotNull(message = "课程id不能为空")
    private Integer courseId;

    @ApiModelProperty(value = "课件分组ID")
    private Integer coursewareGroupId;

}