package com.sibuqu.work.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class WriteInnoWorkDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "课程id", required = true)
    @NotNull(message = "课程id不能为空")
    private Integer courseId;

    @ApiModelProperty(value = "课件id", required = true)
    @NotNull(message = "课件id不能为空")
    private Integer courseTimetableId;

    @ApiModelProperty("type 1-积分 2-内容 3-海报 4-内容加海报")
    private Integer type = 0;

    @ApiModelProperty(value = "用户积分")
    private Long userPoints;

    @ApiModelProperty(value = "作业内容")
    private String content;

    @ApiModelProperty(value = "海报")
    private String poster;

}