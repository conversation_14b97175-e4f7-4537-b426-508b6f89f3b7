package com.sibuqu.work.dto;

import com.sibuqu.base.common.page.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

@Data
public class WorkingByDateDTO  extends PageBase {
    @ApiModelProperty(value = "课程id",required = true)
    private Integer courseId;
    @ApiModelProperty(value = "班级id")
    private Integer classesId;
    @ApiModelProperty(value = "废弃-作业所属日期")
    private LocalDate belongDate;
    @ApiModelProperty(value = "小组id")
    private Integer teamId;
    @ApiModelProperty("课件id")
    private Integer courseTimeTableId;
    @ApiModelProperty("多方案课件->用户所选方案")
    private String planSchemeOption;

}
