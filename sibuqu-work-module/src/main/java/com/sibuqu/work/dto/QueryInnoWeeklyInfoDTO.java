package com.sibuqu.work.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@ApiModel(description = "查询inno周作业单条信息DTO")
@Data
public class QueryInnoWeeklyInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "周作业ID", required = true)
    @NotNull(message = "周作业ID不能为空")
    private Long id;
}