package com.sibuqu.work.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2022年5月17日16:08:39
 * @Version 1.0
 * @Description 老师删除回复作业参数DTO  特殊说明：因为APP端想改动最小化  暂时所有字段用的老代码字段 做映射
 **/
@Data
public class AppWorkReplyDelDTO {

    @ApiModelProperty("作业ID")
    @NotNull(message="作业ID不能为空")
    private Integer workId;

    @ApiModelProperty("回复ID")
    @NotNull(message="回复ID不能为空")
    private Integer id;

}
