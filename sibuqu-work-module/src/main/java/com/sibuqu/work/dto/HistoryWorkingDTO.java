package com.sibuqu.work.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class HistoryWorkingDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("第几页")
    private int pageNum = 1;

    @ApiModelProperty("分页大小")
    private int pageSize = 10;

    @ApiModelProperty(value = "课程id", required = true)
    private Integer courseId;

    @ApiModelProperty(value = "前端不传-班级id")
    private Integer classesId;

    @ApiModelProperty(value = "小组id")
    private Integer teamId;

}
