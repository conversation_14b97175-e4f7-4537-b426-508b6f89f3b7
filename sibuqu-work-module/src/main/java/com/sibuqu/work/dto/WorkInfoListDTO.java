package com.sibuqu.work.dto;

import com.sibuqu.base.common.page.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 后台管理系统 作业列表
 * <AUTHOR>
 */
@Data
public class WorkInfoListDTO extends PageBase {
    @NotNull(message="课程id")
    @ApiModelProperty(value="课程id",required = true)
    private String courseId;
    @ApiModelProperty(value="是否推荐0未推荐1已推荐")
    private Integer recommendStatus;
    /**
     * 是否有班级群
     */
    @ApiModelProperty(value ="是否有班级 true: 是 false:否")
    private Boolean haveClass;

    @ApiModelProperty(value ="学员姓名")
    private String userName;

    @ApiModelProperty(value ="学员手机号")
    private String userPhone;

    @ApiModelProperty(value ="班级号")
    private String classesNo;

    @ApiModelProperty(value ="班主任姓名")//需要查
    private String classesTeacherName;

    @ApiModelProperty(value ="班主任手机号")//需要查
    private String classesTeacherPhone;

    @ApiModelProperty("作业归属日期开始时间")
    private String courseBeginTimeStart;

    @ApiModelProperty("作业归属日期结束时间")
    private String courseBeginTimeEnd;

    /**
     *
     */
    @NotNull(message = "企业id不能为空")
    @ApiModelProperty(value ="企业id 个人版本传0")
    private String companyId;



    @ApiModelProperty(value="是否推送给老师 0未推荐1已推荐")
    private Integer recommendTeacherStatus;

    @ApiModelProperty(value="老师回复状态 0 未回复 1:已回复")
    private Integer teacherReplyStatus;

    @ApiModelProperty("作业推荐日期开始时间")
    private String recommendTimeStart;
    @ApiModelProperty("作业推荐日期结束时间")
    private String recommendTimeEnd;

    @ApiModelProperty("作业模版标识（0-统一作业模版 1-区分新老作业模版 2-等级作业模板）")
    private Integer workModelFlag;

    @ApiModelProperty("作业模版id")
    private Integer workModelId;

    @ApiModelProperty(value="课件名称")
    private String courseWareName;

    @ApiModelProperty("课件id")
    private Integer courseTimetableId;
}
