package com.sibuqu.work.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@ApiModel(description = "inno作业推荐/取消推荐DTO")
@Data
public class InnoWorkRecommendDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "作业id", required = true)
    @NotNull(message = "作业id不能为空")
    private Long id;

    @ApiModelProperty(value = "推荐标识 0-取消推荐 1-推荐", required = true)
    @NotNull(message = "推荐标识不能为空")
    @Range(min = 0, max = 1, message = "推荐标识只能是 0-取消推荐 1-推荐")
    private Integer recommendFlag;
}
