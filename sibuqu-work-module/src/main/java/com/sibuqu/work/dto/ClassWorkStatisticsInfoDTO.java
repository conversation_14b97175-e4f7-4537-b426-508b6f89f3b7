package com.sibuqu.work.dto;

import com.sibuqu.base.common.page.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date ：Created in 2022/5/20 16:11
 * @description：班级作业详情
 * @modified By：
 * @version: $
 */
@Data
public class ClassWorkStatisticsInfoDTO extends PageBase {

    @ApiModelProperty(value="班级号")
    private String classesNo;

    @ApiModelProperty(value="所属日期")
    private LocalDate belongDate;

    @ApiModelProperty("学员姓名")
    private String userName;

    @ApiModelProperty("学员手机号")
    private String userPhone;

    @ApiModelProperty(value="课程ID")
    private Integer courseId;

    @ApiModelProperty("作业模版标识（0-统一作业模版 1-区分新老作业模版 2-等级作业模板）")
    private Integer workModelFlag;

    @ApiModelProperty("作业模版id")
    private Integer workModelId;
}
