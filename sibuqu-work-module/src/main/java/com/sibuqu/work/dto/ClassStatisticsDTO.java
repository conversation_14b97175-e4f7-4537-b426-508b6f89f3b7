package com.sibuqu.work.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ClassStatisticsDTO{
    @ApiModelProperty(value = "课程id",required = true)
    private Integer courseId;
    @ApiModelProperty("班级id")
    private Integer classesId;
    @ApiModelProperty(value = "班级号")
    private Integer classesNo;
    @ApiModelProperty(value = "企业id")
    private Integer companyId;

    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "截止时间")
    private LocalDateTime endTime;

    @ApiModelProperty("用户计划方案选项")
    private String planSchemeOption;

}
