package com.sibuqu.work.dto;

import com.sibuqu.base.common.page.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class WorkReplyPageDTO extends PageBase implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "企业ID")
    private Integer companyId;

    @ApiModelProperty(value = "课程ID")
    private Integer courseId;

    @ApiModelProperty(value="课件名称")
    private String courseWareName;

    @ApiModelProperty("课件id")
    private Integer courseTimetableId;

    @ApiModelProperty(value="老师回复人")
    private String teacherReplyName;

    @ApiModelProperty("作业归属日期开始时间")
    private String startDate;

    @ApiModelProperty("作业归属日期结束时间")
    private String endDate;

    @ApiModelProperty(value="是否Ai回复-0否，1是")
    private Integer aiRecommendStatus;

    @ApiModelProperty(value="是否推荐0未推荐1已推荐")
    private Integer recommendStatus;

    @ApiModelProperty(value ="学员姓名")
    private String userName;

    @ApiModelProperty(value ="学员手机号")
    private String userPhone;

    @ApiModelProperty("作业回复日期开始时间")
    private String startReplyDate;

    @ApiModelProperty("作业回复日期结束时间")
    private String endReplyDate;

    @ApiModelProperty(value="是否AI回复 0:否 1:是")
    private Integer aiReplyStatus;

    @ApiModelProperty(value="是否显示 0显示 1隐藏")
    private Integer showStatus;
}
