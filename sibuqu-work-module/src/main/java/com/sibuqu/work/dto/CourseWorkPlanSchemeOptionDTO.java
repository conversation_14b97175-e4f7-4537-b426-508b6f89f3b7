package com.sibuqu.work.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class CourseWorkPlanSchemeOptionDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "课程id", required = true)
    private Integer courseId;

    @ApiModelProperty("课件id")
    private Integer courseTimetableId;

    @ApiModelProperty(value = "小组id")
    private Integer teamId;

    @ApiModelProperty("计划方案选项")
    private String planSchemeOption;

}