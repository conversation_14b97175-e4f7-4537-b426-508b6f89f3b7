package com.sibuqu.work.dto;

import com.sibuqu.base.common.page.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Data
public class UserWorkListDTO extends PageBase {
    @ApiModelProperty(value="课程id")
    private String courseId;

    @ApiModelProperty(value="企业id")
    private String companyId;

    @ApiModelProperty(value="企业名称")
    private String companyName;

    @ApiModelProperty(value="小组id")
    private Integer teamId;

    @ApiModelProperty(value="用户手机号")
    private String userPhone;

    @ApiModelProperty(value="用户姓名")
    private String userName;

    @ApiModelProperty(value="班级编号")
    private Integer classesNo;

    @ApiModelProperty(value="班主任手机")
    private String classesTeacherPhone;

    @ApiModelProperty(value="班主任姓名")
    private String classesTeacherName;

    @ApiModelProperty(value="开始日期")
    private LocalDate startDate;

    @ApiModelProperty(value="截止日期")
    private LocalDate endDate;

    @ApiModelProperty(value="是否推荐0未推荐1已推荐")
    private Integer recommendStatus;

    @ApiModelProperty(value="课件名称")
    private String resourceTitle;
}
