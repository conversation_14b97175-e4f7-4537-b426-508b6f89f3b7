package com.sibuqu.work.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/10/25 9:48
 * @Description 作业心得评论搜索DTO
 */
@Data
public class AppWorkCommentSearchDTO implements Serializable {

    @ApiModelProperty(value = "作业Ids",notes = "作业Ids",required = true)
    @NotNull(message = "作业Ids不能为空")
    private List<Integer> workIds;

    @ApiModelProperty(value = "用户ID",notes = "用户ID",required = true)
    @NotNull(message = "用户ID不能为空")
    private Integer userId;

}
