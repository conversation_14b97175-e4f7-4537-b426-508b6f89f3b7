package com.sibuqu.work.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class RecordThinkDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("课程id")
    @NotNull(message = "课程id不能为空")
    private Integer courseId;

    @ApiModelProperty(value = "课程表id")
    private Integer courseTimetableId;

    @ApiModelProperty(value = "内容")
    private String content;

}