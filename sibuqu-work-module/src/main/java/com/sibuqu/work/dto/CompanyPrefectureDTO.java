package com.sibuqu.work.dto;

import com.sibuqu.base.common.page.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class CompanyPrefectureDTO extends PageBase {
    @ApiModelProperty(value = "企业专区分类 1回复心得，2推荐作业")
    @NotNull(message = "企业专区分类不能为空")
    private Integer prefectureType;
    @ApiModelProperty(value = "筛选类型 1.仅看老师回复 2仅看企业回复 3仅看其他企业 4仅看我的企业")
    private Integer screeningType;
}
