package com.sibuqu.work.dto;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class QueryByCourseIdAndTimeDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotNull(message = "课程 id 不能为空")
    @ApiModelProperty(value = "课程 id", required = true)
    private Integer courseId;

    @NotNull(message = "开始时间不能为空")
    @ApiModelProperty(value = "开始时间", required = true)
    private LocalDateTime startTime;

    @NotNull(message = "结束时间不能为空")
    @ApiModelProperty(value = "结束时间", required = true)
    private LocalDateTime endTime;
}