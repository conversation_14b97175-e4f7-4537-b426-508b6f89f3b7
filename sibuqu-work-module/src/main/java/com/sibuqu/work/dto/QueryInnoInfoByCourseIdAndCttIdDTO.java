package com.sibuqu.work.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class QueryInnoInfoByCourseIdAndCttIdDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "课程id")
    private Integer courseId;

    @ApiModelProperty(value = "课件id")
    private Integer courseTimetableId;

    @ApiModelProperty(value = "用户 id 列表")
    private List<Integer> userIdList;

}