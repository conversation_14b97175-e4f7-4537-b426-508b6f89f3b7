package com.sibuqu.work.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class AddWorkUserModelDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "课程id")
    @NotNull(message = "课程 id 不能为空")
    private Integer courseId;

    @ApiModelProperty(value = "作业模板等级 a b c")
    @NotNull(message = "作业模板等级不能为空")
    private String level;

    @ApiModelProperty(value = "学员作业模板ID")
    @NotNull(message = "作业模板ID不能为空")
    private Integer workModelId;

    @ApiModelProperty(value = "学员作业模板名称")
    @NotBlank(message = "作业模板名称不能为空")
    private String workModelName;

}
