package com.sibuqu.work.dto;

import com.sibuqu.base.common.page.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Date 2022年5月17日16:08:39
 * @Version 1.0
 * @Description 心得列表查询参数DTO  特殊说明：因为APP端想改动最小化  暂时所有字段用的老代码字段 做映射
 **/
@Data
public class AppWorkHeartPerceptionDTO extends PageBase {

    @ApiModelProperty(value="班级ID")
    private Integer groupId;

    @ApiModelProperty(value="课程ID")
    private Integer courseId;

    @ApiModelProperty(value="课件id")
    private Integer courseTimetableId;

    @ApiModelProperty(value="作业ID（用于进入页面定位到第一条）")
    private Integer workId;

    @ApiModelProperty(value="小组id")
    private Integer teamId;


    @ApiModelProperty(value="废弃-班级ID")
    private Integer classesId;

    @ApiModelProperty(value = "班级号")
    private String classesNo;

    @ApiModelProperty(value = "作业排序顺序 1-热点数据；2-最新数据")
    private Integer sortType;
}
