package com.sibuqu.work.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class CheckOldStudentCountDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("课程id")
    @NotNull(message = "课程id不能为空")
    private Integer courseId;

    @ApiModelProperty("用户id列表")
    private List<Integer> userIdList;

}
