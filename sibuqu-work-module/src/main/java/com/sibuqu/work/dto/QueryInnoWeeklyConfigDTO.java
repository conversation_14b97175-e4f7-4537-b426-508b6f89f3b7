package com.sibuqu.work.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@ApiModel(description = "查询inno周作业配置DTO")
@Data
public class QueryInnoWeeklyConfigDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "配置ID", required = true)
    @NotNull(message = "配置ID不能为空")
    private Long id;
}