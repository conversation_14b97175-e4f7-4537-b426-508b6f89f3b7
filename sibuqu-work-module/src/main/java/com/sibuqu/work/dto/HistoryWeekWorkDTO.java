package com.sibuqu.work.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class HistoryWeekWorkDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("课程id")
    @NotNull(message = "课程id不能为空")
    private Integer courseId;

    @ApiModelProperty("当前页")
    private Integer pageNum;

    @ApiModelProperty("每页显示条数")
    private Integer pageSize;

}