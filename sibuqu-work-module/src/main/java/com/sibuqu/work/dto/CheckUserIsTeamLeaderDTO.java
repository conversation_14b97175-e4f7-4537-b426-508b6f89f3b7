package com.sibuqu.work.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@ApiModel(description = "查询用户是否是团队负责人身份DTO")
@Data
@Validated
public class CheckUserIsTeamLeaderDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "课程id", required = true)
    @NotNull(message = "课程id不能为空")
    private Integer courseId;

    @ApiModelProperty(value = "用户id列表", required = true)
    @NotEmpty(message = "用户id列表不能为空")
    private List<Integer> userIds;
}
