package com.sibuqu.work.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ClassWorkRankDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "企业ID")
    private Integer companyId;

    @ApiModelProperty(value = "课程ID")
    private Integer courseId;

    @ApiModelProperty("第几页")
    private int pageNum = 1;

    @ApiModelProperty("分页大小")
    private int pageSize = 10;

    @ApiModelProperty("排序 1-正序 2-倒序")
    private Integer sort;

}
