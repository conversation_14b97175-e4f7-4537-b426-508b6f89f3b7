package com.sibuqu.work.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class DoWorkMsgDTO {
    @ApiModelProperty("课程id")
    private Integer courseId;
    @ApiModelProperty(value = "课程表id")
    private Integer courseTimeTableId;
    @ApiModelProperty(value = "企业id")
    private Integer companyId;
    private Integer userId;
    private String userName;
    private String avatar;
    private String phone;
    private String tag;
    private Integer id;
    /**
     * 作业卡信息
     */
    private List<DoWorkDetailDTO> workDetails;
}
