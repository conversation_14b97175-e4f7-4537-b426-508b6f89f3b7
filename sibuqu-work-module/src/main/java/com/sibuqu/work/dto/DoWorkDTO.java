package com.sibuqu.work.dto;

import com.sibuqu.work.vo.admin.WorkModelDetailVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class DoWorkDTO {
    @ApiModelProperty("课程id")
    private Integer courseId;
    @ApiModelProperty("作业模版id")
    private Integer workModelId;
    @ApiModelProperty(value = "课程表id")
    private Integer courseTimeTableId;
    @ApiModelProperty(value = "企业id")
    private Integer companyId;
    /**
     * 作业卡信息
     */
    private List<DoWorkDetailDTO> workDetails;
}
