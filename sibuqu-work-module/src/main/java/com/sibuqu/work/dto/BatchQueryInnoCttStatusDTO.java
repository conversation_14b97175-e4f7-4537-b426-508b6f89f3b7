package com.sibuqu.work.dto;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BatchQueryInnoCttStatusDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "用户id")
    private Integer userId;

    @ApiModelProperty(value = "课件id列表")
    private List<Integer> courseTimetableIds;

}