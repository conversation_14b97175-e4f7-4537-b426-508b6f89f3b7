package com.sibuqu.work.dto;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(description = "查询inno周作业下拉列表DTO")
@Data
public class QueryInnoWeeklyInfoSelectDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "课程 id")
    @NotNull(message = "课程 id 不能为空")
    private Integer courseId;
}