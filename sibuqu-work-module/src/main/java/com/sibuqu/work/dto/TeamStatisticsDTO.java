package com.sibuqu.work.dto;

import com.sibuqu.base.common.page.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TeamStatisticsDTO extends PageBase {
    @ApiModelProperty(value = "课程id",required = true)
    private Integer courseId;
    @ApiModelProperty(value = "班级id")
    private Integer classesId;
    @ApiModelProperty(value = "小组id")
    private Integer teamId;
    private Integer classesNo;
}
