package com.sibuqu.work.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2022年5月17日16:08:39
 * @Version 1.0
 * @Description 老师回复作业参数DTO  特殊说明：因为APP端想改动最小化  暂时所有字段用的老代码字段 做映射
 **/
@Data
public class AppWorkReplyDTO {

    @ApiModelProperty("作业ID")
    @NotNull(message="作业ID不能为空")
    private Integer id;
    @ApiModelProperty("老师回复内容")
    @NotNull(message="老师回复内容不能为空")
    private String teacherReply;

    @ApiModelProperty(value = "老师回复内容类型（0文字  1语音）", dataType = "Integer")
    private Integer teacherReplyType;

    @ApiModelProperty("语音时长")
    private Integer audioTimeLength;

    @ApiModelProperty("语音文件大小")
    private Integer audioSize;

}
