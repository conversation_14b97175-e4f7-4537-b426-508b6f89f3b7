package com.sibuqu.work.dto;

import com.sibuqu.base.common.page.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class ClassWorkStatisticsDTO extends PageBase {
    @ApiModelProperty(value="课程id")
    private String courseId;
    @ApiModelProperty(value="企业id")
    private String companyId;
    @ApiModelProperty(value="企业编号")
    private String companyCode;
    @ApiModelProperty(value="企业名称")
    private String companyName;
    @ApiModelProperty(value="班级号")
    private String classesNo;
    @ApiModelProperty(value="班主任手机")
    private String classesTeacherPhone;
    @ApiModelProperty(value="开始时间")
    private LocalDate startDate;
    @ApiModelProperty(value="截止时间")
    private LocalDate endDate;

    @ApiModelProperty(value="所属日期")
    private LocalDate belongDate;

    @ApiModelProperty(value="课件名称")
    private String courseWareName;

    @ApiModelProperty(value="课件ID")
    private Integer courseTimetableId;


}
