package com.sibuqu.work.enums;

/**
 * 
 *
 * <AUTHOR> @date 
 */
public enum WorkDetailTypeEnum {
    LISTEN(1, "听课"),
    CLOCK(2, "打卡"),
    TEXT(3, "文本"),
    NUMBER(4, "数字"),
    EVALUATE(5,"评价"),
    NOUN_SCORE(6,"无评分"),
    ADDITIONAL(7,"附加作业"),
    CHOICE(8,"选择题"),
    CLOCK_NOUN_SCORE(9,"打卡不加分"),
    DUSING(10,"笃行"),
    SHARE(11,"分享打卡"),
    MULTIPLE_CHOICE(12,"多选"),
    NPS(13,"NPS"),
    AUDIO_TEXT(14,"语音和文本"),
    DYNAMIC_MULTIPLE_CHOICE(15,"动态多选题"),
    DYNAMIC_SINGLE_CHOICE(16,"动态单选题"),
    READ(17,"读书");
    ;

    private Integer code;
    private String desc;

    WorkDetailTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;

    }

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
    public static String getDesc(Integer code) {
        for (WorkDetailTypeEnum workDetailTypeEnum : WorkDetailTypeEnum.values()) {
            if (workDetailTypeEnum.getCode().equals(code)) {
                return workDetailTypeEnum.desc;
            }
        }
        return null;
    }
}
