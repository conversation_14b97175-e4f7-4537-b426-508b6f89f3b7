package com.sibuqu.work.enums;

import lombok.Getter;

public class WorkInfoEnum {

    @Getter
    public enum ShowStatus {
        HIDE(0, "隐藏"),
        SHOW(1, "显示");

        ShowStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private final Integer code;
        private final String desc;
    }

    @Getter
    public enum ContentShowStatus {
        HIDE(0, "不展示"),
        SHOW(1, "展示");

        ContentShowStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private final Integer code;
        private final String desc;
    }

    @Getter
    public enum CompanyReplyStatus {
        REPLY(1, "回复"),
        NO_REPLY(0, "没有回复");

        CompanyReplyStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private final Integer code;
        private final String desc;
    }

    @Getter
    public enum RecommendStatus {
        NO_RECOMMEND(0, "未推荐"),
        RECOMMEND(1, "已推荐");

        RecommendStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private final Integer code;
        private final String desc;
    }

    @Getter
    public enum WorkStatus {
        OK(1, "完成"),
        NO(0, "未完成");

        WorkStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private final Integer code;
        private final String desc;
    }

    //     @ApiModelProperty(value = "听课状态 1已听 0未听")
    //     private Integer listenStatus;
    @Getter
    public enum ListenStatus {
        LISTEN(1, "已听"),
        NO_LISTEN(0, "未听");

        ListenStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        private final Integer code;
        private final String desc;
    }
}
