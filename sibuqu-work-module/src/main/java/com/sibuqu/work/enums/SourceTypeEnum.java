package com.sibuqu.work.enums;

/**
 * <AUTHOR>
 * @Version 1.0.0
 * @Description 数据来源枚举
 * @CreateTime 2022年5月18日21:09:22
 */
public enum SourceTypeEnum {
    APP(0, "app"),
    PC(1, "pc");

    private Integer code;
    private String desc;

    SourceTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;

    }

    public Integer getCode() {
        return this.code;
    }

}
