package com.sibuqu.work.enums;

import lombok.Getter;

import java.util.EnumSet;

/**
 * <AUTHOR> 推荐状态 0 未推荐 1推荐
 * @date 2022/2/22-15:57 下午
 */


@Getter
public enum RecommendStatusEnums {

    NO(0, "未推荐"),
    YES(1, "推荐"),
    ;

    RecommendStatusEnums(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    private Integer code;
    private String name;

    public Integer getCode() {
        return code;
    }

    public static RecommendStatusEnums getByCode(Integer code) {
        for (RecommendStatusEnums model : EnumSet.allOf(RecommendStatusEnums.class)) {
            if (model.code.equals(code)) {
                return model;
            }
        }
        return null;
    }

    public static RecommendStatusEnums getByName(String name){
        for (RecommendStatusEnums model : EnumSet.allOf(RecommendStatusEnums.class)) {
            if (model.name.equals(name)) {
                return model;
            }
        }
        return null;
    }
}
