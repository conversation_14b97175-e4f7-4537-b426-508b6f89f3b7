package com.sibuqu.work.enums;


public enum ScreeningTypeEnums {
    TEACHER_REPLY(1, "仅看老师回复"),
    COMPANY_REPLY(2, "仅看企业回复"),
    OTHER_COMPANY_RECOMMENDED(3,"其他企业推荐"),
    MY_COMPANY_RECOMMENDED(4, "我的企业推荐"),

    ;

    private Integer code;
    private String desc;

    ScreeningTypeEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;

    }

    public Integer getCode() {
        return this.code;
    }

}
