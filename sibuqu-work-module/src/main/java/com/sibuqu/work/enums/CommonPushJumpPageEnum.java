package com.sibuqu.work.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2021/10/25 17:56
 * @Description 推送消息跳转页面类型枚举
 */
@Getter
@AllArgsConstructor
public enum CommonPushJumpPageEnum {

    NEW_WORK_LIST(1,"新版心得列表"),
    OLD_WORK_LIST(2,"老版心得列表"),
    COURSE_COMMENT_LIST(3,"课件评论回复列表"),
    COURSE_COMMENT_LIKE_LIST(4,"课件评论点赞列表"),
    ORDER_DETAIL(11,"订单详情页"),
    INVOICE_DETAIL(12,"发票详情页"),
    REFUND_DETAIL(13,"退款详情页"),
    MEETING_REMIND(21,"会议提醒"),
    MEETING_APPLY(22,"学员申请参会消息"),
    MEETING_LEAVE(23,"学员会议请假消息"),
    MEETING_APPLY_PASS(24,"学员参会申请通过消息"),
    AREA_COMMENT(25,"专区学员对内容的评论"),
    AREA_AT_COMMENT(26,"学员对@内容的评论"),
    AREA_AT_ME(27,"学员被@的消息"),
    AREA_LIKE(28,"专区学员对内容的点赞"),
    AREA_QUOTE(29,"专区对内容的引用"),
    ENTERPRISE_CLASS_HOME(30,"企业版班级主页"),
    TEN_JOIN_HEART(31,"十家连心页");

    private Integer code;
    private String value;
    public static CommonPushJumpPageEnum getByCode(Integer code){
        for (CommonPushJumpPageEnum value : CommonPushJumpPageEnum.values()) {
            if(value.code.equals(code)){
                return value;
            }
        }
        return null;
    }
}
