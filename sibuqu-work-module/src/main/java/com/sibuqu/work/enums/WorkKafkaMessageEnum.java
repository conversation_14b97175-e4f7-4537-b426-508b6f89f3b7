package com.sibuqu.work.enums;

/**
 * 
 *
 * <AUTHOR> @date 
 */
public enum WorkKafkaMessageEnum {
    DO_WORK("doWork", "写作业"),
    AUTO_LISTENED("autoListened", "听课统计"),
    SHAR("SHAR", "分享加分"),
    ADDITIONAL("additional", "附加题加分"),
    LISTEN_UP("listenedUp", "听课");

    private String code;
    private String desc;

    WorkKafkaMessageEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;

    }

    public String getCode() {
        return this.code;
    }

}
