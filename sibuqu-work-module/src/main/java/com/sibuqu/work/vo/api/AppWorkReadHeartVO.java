package com.sibuqu.work.vo.api;

import com.sibuqu.work.vo.admin.AppChapterVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AppWorkReadHeartVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("读书记录id")
    private Long readRecordId;

    @ApiModelProperty("电子书ID")
    private Long ebookId;

    @ApiModelProperty("封面图")
    private String ebookCover;

    @ApiModelProperty("电子书书籍分享图")
    private String ebookShareCover;

    @ApiModelProperty("电子书名")
    private String ebookName;

    @ApiModelProperty("章节信息")
    private List<AppChapterVO> chapterList;

    @ApiModelProperty("共读时长 秒")
    private Integer readDuration;

    @ApiModelProperty("共读任务ID")
    private Long readTaskId;

}
