package com.sibuqu.work.vo.api;

import com.sibuqu.work.vo.admin.WorkModelAudioVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022年5月17日16:08:39
 * @Version 1.0
 * @Description 心得内容明细VO
 **/
@Data
public class AppWorkDetailListVO {

    @ApiModelProperty(value="作业项类型")
    private Integer type;

    @ApiModelProperty("展示标题")
    private String showTitle;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("语音内容")
    private List<WorkModelAudioVO> audioList;

    @ApiModelProperty("读书信息")
    private AppWorkReadHeartVO readVO;

}
