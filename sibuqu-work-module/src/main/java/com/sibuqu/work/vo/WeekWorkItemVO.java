package com.sibuqu.work.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class WeekWorkItemVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("归属日期")
    private LocalDate belongDate;

    @ApiModelProperty(value = "课程表id")
    private Integer courseTimetableId;

    @ApiModelProperty(value = "作业状态1已写 0未写")
    private Integer workStatus = 0;

    @ApiModelProperty(value="作业提示")
    private String prompt;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "回复人")
    private String replyName;

    @ApiModelProperty(value = "回复内容")
    private String replyContent;

    @ApiModelProperty("作业开始时间 ")
    private LocalDateTime workBeginTime;

}