package com.sibuqu.work.vo.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2022年5月17日16:08:39
 * @Version 1.0
 * @Description 心得评论列表VO  特殊说明：因为APP端想改动最小化  暂时所有字段用的老代码字段 做映射
 **/
@Data
public class AppWorkCommentListVO {

    @ApiModelProperty(value = "评论id",dataType = "Integer")
    private Integer id;

    @ApiModelProperty(value = "当前所在小组id",dataType = "Integer")
    private Integer groupId;

    @ApiModelProperty(value = "心得id",dataType = "Integer")
    private Integer postId;

    @ApiModelProperty(value = "用户id",dataType = "Integer")
    private Integer userId;

    @ApiModelProperty(value = "用户名称",dataType = "String")
    private String userName;

    @ApiModelProperty(value = "被回复评论id",dataType = "Integer")
    private Integer replyPostId;

    @ApiModelProperty(value = "被回复用户id",dataType = "Integer")
    private Integer replyUserId;

    @ApiModelProperty(value = "被回复用户名称",dataType = "String")
    private String replyUserName;

    @ApiModelProperty(value = "回复内容",dataType = "String")
    private String postContent;

    @ApiModelProperty(value = "创建的时间",dataType = "LocalDateTime")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改的时间",dataType = "LocalDateTime")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "评论类别（0评论 1回复）",dataType = "String")
    private Integer commentType;

    @ApiModelProperty(value = "企业 id")
    private Integer companyId;

}
