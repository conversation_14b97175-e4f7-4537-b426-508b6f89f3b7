package com.sibuqu.work.vo.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class WorkReplyVO {

    @ApiModelProperty(value = "回复的主键ID")
    private Integer id;

    @ApiModelProperty(value = "回复的内容")
    private String content;

    @ApiModelProperty(value = "回复类型（0文字 1语音）")
    private Integer replyType;

    @ApiModelProperty(value = "消息来源(0app 1pc)")
    private Integer sourceType;

    @ApiModelProperty(value = "回复用户ID")
    private Integer userId;

    @ApiModelProperty(value = "回复用户姓名")
    private String userName;

    @ApiModelProperty("语音时长")
    private Integer audioTimeLength;

    @ApiModelProperty("语音文件大小")
    private Integer audioSize;

    @ApiModelProperty("回复用户类型 1-老师 2-企业管理员")
    private Integer replyUserType;

}
