package com.sibuqu.work.vo.admin;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.sibuqu.work.enums.RecommendStatusEnums;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * 后台推荐作业列表
 * <AUTHOR>
 */
@Data
public class WorkInfoRecommendListExportVO {

    @Excel(name ="学员Id", orderNum = "1")
    private Integer userId;
    @Excel(name = "学员姓名" , orderNum = "2")
    private String userName;
    @Excel(name = "学员手机号" , orderNum = "3")
    private String userPhone;

    @Excel(name ="作业项1" , orderNum = "5")
    private String workContentOne;
    @Excel(name ="作业项2" , orderNum = "6")
    private String workContentTwo;
    @Excel(name ="作业项3", orderNum = "7")
    private String workContentThree;
    @Excel(name ="作业项项4", orderNum = "8")
    private String workContentFour;
    @Excel(name ="作业项5", orderNum = "9")
    private String workContentFive;

    @Excel(name = "班级号" , orderNum = "10")
    private String classesNo;

    @Excel(name = "班级名称" , orderNum = "12")
    private String classesName;

    @Excel(name="推荐时间" , orderNum = "13",width = 30)
    private String recommendTimeStr;


    @Excel(name = "班主任" , orderNum = "14")//需要查
    private String classesTeacherName;


    @Excel(name = "班主任手机号" , orderNum = "16")//需要查
    private String classesTeacherPhone;

    @Excel(name="作业归属日期", orderNum = "18",width = 30)
    private String courseBeginTime;

    /**
     * 创建时间
     */
    @Excel(name="作业提交时间" , orderNum = "20",width = 30)
    private String createTimeStr;

    /** 是否推荐给老师0未推荐1已推荐  */
    @Excel(name="推送状态" , orderNum = "23")
    private String recommendTeacherStatusStr;

    /**  */
    @Excel(name="推送时间" , orderNum = "35",width = 30)
    private String recommendTeacherTimeStr;


    /** 老师回复状态 0 未回复 1:已回复 */
    @Excel(name="回复状态" , orderNum = "38")
    private String teacherReplyStatusStr;

    /**
     * 老师回复时间
     */
    @Excel(name = "回复时间" , orderNum = "40",width = 30)//去最后面一次回复的时间
    private String teacherReplyDateStr;
    /**
     * vo 转为 excel的  Model
     * @param vo
     * @return
     */
    public static WorkInfoRecommendListExportVO vo2ExportModel(WorkInfoListVO vo) {
        if (null == vo) {
            return null;
        }
        WorkInfoRecommendListExportVO result = new WorkInfoRecommendListExportVO();
        BeanUtils.copyProperties(vo, result);

        result.setCreateTimeStr(DateUtil.format(vo.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN));
        if(vo.getRecommendTeacherStatus()!=null){
            if(RecommendStatusEnums.getByCode(vo.getRecommendTeacherStatus())!=null){
                result.setRecommendTeacherStatusStr(RecommendStatusEnums.getByCode(vo.getRecommendTeacherStatus()).getName());
            }

        }

        if(vo.getRecommendTeacherTime()!=null){
            result.setRecommendTeacherTimeStr(DateUtil.format(vo.getRecommendTeacherTime(), DatePattern.NORM_DATETIME_PATTERN));
        }

        if(vo.getRecommendTime()!=null){
            result.setRecommendTimeStr(DateUtil.format(vo.getRecommendTime(), DatePattern.NORM_DATETIME_PATTERN));
        }

        if(vo.getTeacherReplyDate()!=null){
            result.setTeacherReplyDateStr(DateUtil.format(vo.getTeacherReplyDate(), DatePattern.NORM_DATETIME_PATTERN));
        }

        if(new Integer(1).equals(vo.getTeacherReplyStatus())){
            result.setTeacherReplyStatusStr("已回复");
        }else{
            result.setTeacherReplyStatusStr("未回复");
        }

        return result;
    }
}
