package com.sibuqu.work.vo.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ClassWorkRankItemVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("序号")
    private Integer rankNum;

    @ApiModelProperty("班级名称")
    private String className;

    @ApiModelProperty("班主任")
    private String classTeacherName;

    @ApiModelProperty("当期作业率")
    private Double currentWorkRate;

    @ApiModelProperty("当期听课率")
    private Double currentListenRate;

    @ApiModelProperty("当期作业率")
    private String currentWorkRateStr;

    @ApiModelProperty("当期听课率")
    private String currentListenRateStr;

}
