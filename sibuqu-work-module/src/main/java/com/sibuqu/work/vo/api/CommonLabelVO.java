package com.sibuqu.work.vo.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CommonLabelVO {
    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("标签色值")
    private String labelColor;

    @ApiModelProperty("跳转类型 1-不跳转 2-跳转链接 3-跳转商品 4-跳转企业")
    private Integer jumpType;
    @ApiModelProperty("跳转位置 链接为h5地址 商品为商品id 企业为企业id")
    private String jumpLocation;

    @ApiModelProperty("课程id")
    private Integer courseId;

    @ApiModelProperty("企业图片/课程图标")
    private String logoPic;

    @ApiModelProperty("描述")
    private String desc;
}
