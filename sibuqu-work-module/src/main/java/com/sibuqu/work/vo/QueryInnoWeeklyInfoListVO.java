package com.sibuqu.work.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class QueryInnoWeeklyInfoListVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "课程ID")
    private Integer courseId;

    @ApiModelProperty(value = "周作业内容ID")
    private Long contentId;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "周起始日期")
    private LocalDate weeklyBeginDate;

    @ApiModelProperty(value = "周截止日期")
    private LocalDate weeklyEndDate;

    @ApiModelProperty(value = "周作业开始时间")
    private LocalDateTime workBeginDatetime;

    @ApiModelProperty(value = "周作业截止时间")
    private LocalDateTime workEndDatetime;

    @ApiModelProperty(value = "是否完成")
    private Boolean completeFlag;

    @ApiModelProperty(value = "是否是当前周")
    private Boolean currentWeekFlag;

    @ApiModelProperty(value = "课件分组ID")
    private Integer coursewareGroupId;

}