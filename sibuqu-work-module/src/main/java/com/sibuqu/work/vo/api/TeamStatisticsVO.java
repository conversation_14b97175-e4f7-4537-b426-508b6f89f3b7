package com.sibuqu.work.vo.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TeamStatisticsVO {
    @ApiModelProperty(value = "课程id")
    private Integer courseId;
    @ApiModelProperty("班级id")
    private Integer classesId;
    @ApiModelProperty("所属日期")
    private LocalDate belongDate;
    @ApiModelProperty("上课人数")
    private Integer listenNum;
    @ApiModelProperty("作业人数")
    private Integer workNum;
    @ApiModelProperty("积分")
    private Integer score;
    private Integer courseTimeTableId;

}
