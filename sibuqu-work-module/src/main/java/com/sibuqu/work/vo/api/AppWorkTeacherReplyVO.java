package com.sibuqu.work.vo.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2022年5月17日16:08:39
 * @Version 1.0
 * @Description 心得老师回复列表VO  特殊说明：因为APP端想改动最小化  暂时所有字段用的老代码字段 做映射
 **/
@Data
public class AppWorkTeacherReplyVO {

    @ApiModelProperty(value = "老师回复的主键ID",dataType = "Integer")
    private Integer id;

    @ApiModelProperty(value = "老师回复的内容",dataType = "String")
    private String teacherReply;

    @ApiModelProperty(value = "回复类型（0文字 1语音）",dataType = "Integer")
    private Integer teacherReplyType;

    @ApiModelProperty(value = "心得作业ID",dataType = "Integer")
    private Integer postId;

    @ApiModelProperty(value = "回复时间",dataType = "Date")
    private LocalDateTime teacherReplyTime;

    @ApiModelProperty(value = "创建时间",dataType = "Date")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "消息来源(0app 1pc)",dataType = "Integer")
    private Integer sourceType;

    @ApiModelProperty(value = "老师ID",dataType = "Integer")
    private Integer teacherUserId;

    @ApiModelProperty(value = "老师名字",dataType = "String")
    private String teacherName;

    @ApiModelProperty("语音时长")
    private Integer audioTimeLength;

    @ApiModelProperty("语音文件大小")
    private Integer audioSize;

    @ApiModelProperty("回复用户类型 1 老师 2企业管理员")
    private Integer replyUserType;

    @ApiModelProperty("是否是我我回复的")
    private Boolean isMyReply;


}
