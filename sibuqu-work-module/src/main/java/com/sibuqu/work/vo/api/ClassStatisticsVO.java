package com.sibuqu.work.vo.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ClassStatisticsVO {
    @ApiModelProperty(value = "课件名称")
    private String courseWareName;
    @ApiModelProperty(value = "开始提交作业时间")
    private LocalDateTime workStartTime;
    @ApiModelProperty(value = "截止提交作业时间")
    private LocalDateTime workEndTime;
    @ApiModelProperty(value = "当期作业率")
    private String workRate;
    @ApiModelProperty(value = "历史作业率")
    private String historyWorkRate;
    @ApiModelProperty(value = "当期听课率")
    private String listenRate;
    @ApiModelProperty(value = "历史听课率")
    private String historyListenRate;
    @ApiModelProperty(value = "所属日期")
    private LocalDate belongDate;
    private String unsubUserNames;
    @ApiModelProperty("未提交作业人名")
    private List<String> unsubUserName;
    private Integer listenTotal;
    private Integer subTotal;
    /**
     * 班级总人数
     */
    @ApiModelProperty(value="班级总人数")
    private Integer classMemberTotal;

    private Integer classNoSubTotal;

    private Integer classNoListenTotal;
    /**
     * 今日听课率
     */
    @ApiModelProperty(value="今日听课率")
    private String currentListenRate;
    /**
     * 未听课人数
     */
    @ApiModelProperty(value="未听课人数")
    private Integer unlistenTotal;
}
