package com.sibuqu.work.vo.admin;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date ：Created in 2022/5/20 16:07
 * @description：班级作业详情
 * @modified By：
 * @version: $
 */
@Data
public class ClassWorkStatisticsInfoVO {

    @ApiModelProperty(name = "主键ID")
    private Integer id;

    @ApiModelProperty(value="作业归属日期")
    private LocalDate belongDate;

    @ApiModelProperty(value="作业时间")
    private LocalDateTime workTime;

    @ApiModelProperty("学员姓名")
    private String userName;

    @ApiModelProperty("学员手机号")
    private String userPhone;

    @ApiModelProperty(name = "当日合计得分")
    private String dayTotalScore;

    @ApiModelProperty(name = "作业累计得总分")
    private Integer totalScore;

    @ApiModelProperty(name = "作业模板ID")
    private Integer workModelId;

    @ApiModelProperty(name = "作业项内容")
    private String content;

    @ApiModelProperty(name = "返回数据头信息")
    private List<WorkExtraVO> tableHeader;


    @ApiModelProperty(name = "返回数据信息")
    private Map<String, Object> tableData;

}
