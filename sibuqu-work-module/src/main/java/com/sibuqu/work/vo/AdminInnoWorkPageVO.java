package com.sibuqu.work.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@ApiModel(description = "管理后台inno作业分页VO")
@Data
public class AdminInnoWorkPageVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "课程id")
    private Integer courseId;

    @ApiModelProperty(value = "课件id")
    private Integer courseTimetableId;

    @ApiModelProperty(value = "课件名称")
    private String courseTimetableName;

    @ApiModelProperty(value = "用户id")
    private Integer userId;

    @ApiModelProperty(value = "用户姓名")
    private String userName;

    @ApiModelProperty(value = "用户手机号")
    private String userPhone;

    @ApiModelProperty(value = "用户积分")
    private Long userPoints;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "海报")
    private String poster;

    @ApiModelProperty(value = "学员身份 1-学员 2-组长 3-团队负责人")
    private  Integer userQueryRole;

    @ApiModelProperty(value = "推荐标识 0-不推荐 1-推荐 2-历史推荐")
    private Integer recommendFlag;

    @ApiModelProperty(value = "推荐时间")
    private LocalDateTime recommendTime;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人名称")
    private String createUserName;
}
