package com.sibuqu.work.vo.admin;

import com.sibuqu.work.vo.api.AppWorkTeacherReplyVO;
import com.sibuqu.work.vo.api.MyWorkDetailVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WorkInfoVO {

    @ApiModelProperty("学员手机号")
    private String userPhone;

    @ApiModelProperty("学员姓名")
    private String userName;

    @ApiModelProperty("班级号")
    private Integer classesNo;

    @ApiModelProperty("班主任手机号")
    private String classesTeacherPhone;
    @ApiModelProperty("班主任姓名")

    private String classesTeacherName;
    @ApiModelProperty("课程名称")
    private String courseName;

    @ApiModelProperty("所属日期")
    private LocalDate belongDate;

    @ApiModelProperty("作业得分")
    private Integer score;

    @ApiModelProperty("作业提交时间")
    private LocalDateTime workTime;

    @ApiModelProperty("课件名称")
    private String resourceTitle;

    @ApiModelProperty("作业明细")
    private List<MyWorkDetailVO> workDetails;

    @ApiModelProperty(value = "推荐来源1管理后台2企业管理员3班主任")
    private Integer recommendSource;

    @ApiModelProperty(value = "是否推荐给老师0未推荐1已推荐")
    private Integer recommendTeacherStatus;

    @ApiModelProperty(value="老师回复状态 0 未回复 1:已回复")
    private Integer teacherReplyStatus;

    @ApiModelProperty(value = "推荐时间")
    private LocalDateTime recommendTime;

    @ApiModelProperty(value="推荐给老师的时间")
    private LocalDateTime recommendTeacherTime;

    @ApiModelProperty(value = "老师回复时间")
    private LocalDateTime teacherReplyTime;

    @ApiModelProperty(value = "老师回复的内容")
    private List<AppWorkTeacherReplyVO> teacherReplyList;

    @ApiModelProperty(value = "企业回复的内容")
    private List<AppWorkTeacherReplyVO> companyReplyList;
}
