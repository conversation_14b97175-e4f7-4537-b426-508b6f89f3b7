package com.sibuqu.work.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@ApiModel(description = "管理后台inno周作业信息分页VO")
@Data
public class InnoWeeklyInfoPageVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "课程ID")
    private Integer courseId;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "周起始日期")
    private LocalDate weeklyBeginDate;

    @ApiModelProperty(value = "周截止日期")
    private LocalDate weeklyEndDate;

    @ApiModelProperty(value = "周作业开始时间")
    private LocalDateTime workBeginDatetime;

    @ApiModelProperty(value = "周作业截止时间")
    private LocalDateTime workEndDatetime;

    @ApiModelProperty(value = "能量果")
    private Long userPoints;

    @ApiModelProperty(value = "课件分组ID")
    private Integer coursewareGroupId;

    @ApiModelProperty(value = "课件分组名称")
    private String coursewareGroupName;

}