package com.sibuqu.work.vo.admin;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2022年5月19日16:08:39
 * @Version 1.0
 * @Description 心得老师回复列表VO  特殊说明：因为APP端想改动最小化  暂时所有字段用的老代码字段 做映射
 **/
@Data
public class WorkTeacherReplyVO {

    @ApiModelProperty(value = "老师回复的主键ID",dataType = "Integer")
    private Integer id;

    @ApiModelProperty(value = "老师回复的内容",dataType = "String")
    private String teacherReply;

    @ApiModelProperty(value = "回复类型（0文字 1语音）",dataType = "Integer")
    private Integer teacherReplyType;

    @ApiModelProperty(value = "心得作业ID",dataType = "Integer")
    private Integer postId;

    @ApiModelProperty(value = "回复时间",dataType = "Date")
    private LocalDateTime teacherReplyTime;

    @ApiModelProperty(value = "创建时间",dataType = "Date")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "消息来源(0app 1pc)",dataType = "Integer")
    private Integer sourceType;

    @ApiModelProperty(value = "老师名字",dataType = "String")
    private String teacherName;

    @ApiModelProperty("语音时长")
    private Integer audioTimeLength;

    @ApiModelProperty("语音文件大小")
    private Integer audioSize;

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty("用户手机号")
    private String userPhone;

    @ApiModelProperty("作业归属日期")
    private LocalDateTime courseBeginTime;

    @ApiModelProperty(value="课件名称")
    private String courseWareName;

    @ApiModelProperty(value ="内容项1")
    private String workContentOne;
    @ApiModelProperty(value ="内容项2")
    private String workContentTwo;
    @ApiModelProperty(value ="作业项1")
    private String workTitleOne;
    @ApiModelProperty(value ="作业项2")
    private String workTitleTwo;

    @ApiModelProperty(value="是否推荐0未推荐1已推荐")
    private Integer recommendStatus;

    @ApiModelProperty(value="作业1显示 0显示 1隐藏'")
    private Integer showStatus;

    @ApiModelProperty(value="是否AI回复 0:否 1:是")
    private Integer aiReplyStatus;

    @ApiModelProperty(value="课程id")
    private Integer courseId;

    @ApiModelProperty(value="作业全部内容")
    private String content;

    @ApiModelProperty(value="老师id")
    private Integer teacherUserId;
}
