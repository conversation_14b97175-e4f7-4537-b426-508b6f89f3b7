package com.sibuqu.work.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@ApiModel(description = "课件全部信息VO")
@Data
public class CourseTimetableAllVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("课程表ID")
    private Integer id;

    @ApiModelProperty("企业ID")
    private Integer companyId;

    @ApiModelProperty("课程母版ID")
    private Integer courseModelId;

    @ApiModelProperty("课程ID")
    private Integer courseId;

    @ApiModelProperty("课程资源ID")
    private Integer resourceId;

    @ApiModelProperty("课程资源名称")
    private String resourceTitle;

    @ApiModelProperty("课程资源提示")
    private String resourceTips;

    @ApiModelProperty("课程开始时间")
    private LocalDateTime courseBeginTime;

    @ApiModelProperty("课程结束时间")
    private LocalDateTime courseEndTime;

    @ApiModelProperty("作业开始时间")
    private LocalDateTime workBeginTime;

    @ApiModelProperty("作业结束时间")
    private LocalDateTime workEndTime;

    @ApiModelProperty("正课标识（1正课 2非正课）")
    private Integer normalCourseFlag;

    @ApiModelProperty("创建人ID")
    private Integer createUserId;

    @ApiModelProperty("创建人名称")
    private String createUserName;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("修改人ID")
    private Integer updateUserId;

    @ApiModelProperty("修改人名称")
    private String updateUserName;

    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty("排序")
    private Integer sortNum;

    @ApiModelProperty("是否支持试看（1是 0否）")
    private Integer trySeeFlag;

    @ApiModelProperty("状态（1显示 2隐藏）")
    private Integer dataFlag;

    @ApiModelProperty("删除状态（1是 0否）")
    private Integer deleteFlag;

    @ApiModelProperty("课件精华")
    private String cream;

    @ApiModelProperty("作业模板ID")
    private Integer workModelId;

    @ApiModelProperty("课程学习方案")
    private String planScheme;

    @ApiModelProperty("课件归属分组ID")
    private Integer coursewareGroupId;

    @ApiModelProperty("课件归属分组名称")
    private String coursewareGroupName;

    @ApiModelProperty("是否已解锁")
    private Boolean isUnlocked;

    @ApiModelProperty("用户学习进度")
    private Integer userProgress;

    @ApiModelProperty("是否已听课")
    private Boolean hasListened;

    @ApiModelProperty("是否已提交作业")
    private Boolean hasSubmittedWork;
}
