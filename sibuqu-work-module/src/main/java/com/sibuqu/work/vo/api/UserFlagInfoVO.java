package com.sibuqu.work.vo.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class UserFlagInfoVO {
    @ApiModelProperty("用户ID")
    private Integer userId;
    @ApiModelProperty("用户姓名")
    private String username;
    private Integer userRole;

    private String phone;
    private Integer classGroupId;
    private String classTeacherName;

    @ApiModelProperty("是否显示新用户标识")
    private Boolean userFlag;

    @ApiModelProperty(value = "作业模板等级 a b c")
    private String level;

}
