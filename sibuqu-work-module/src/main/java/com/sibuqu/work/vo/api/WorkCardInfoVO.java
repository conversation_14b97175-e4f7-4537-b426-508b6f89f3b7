package com.sibuqu.work.vo.api;

import com.sibuqu.work.vo.admin.WorkModelDetailVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class WorkCardInfoVO {

    @ApiModelProperty(value = "主键id")
    private int id;

    /**
     * 课程id
     */
    @ApiModelProperty("课程id")
    private Integer courseId;
    /**
     * 作业模版id
     */
    @ApiModelProperty("作业模版id")
    private Integer workModelId;
    /**
     * 课程表id
     */
    @ApiModelProperty(value = "课程表id")
    private Integer courseTimeTableId;
    /**
     * 课程表id
     */
    @ApiModelProperty(value = "作业对应课件的标题")
    private String workTitle;
    /**
     * 开始提交作业时间
     */
    @ApiModelProperty(value = "开始提交作业时间")
    private LocalDateTime workStartTime;
    /**
     * 截止提交作业时间
     */
    @ApiModelProperty(value = "截止提交作业时间")
    private LocalDateTime workEndTime;
    /**
     * 我的分数
     */
    @ApiModelProperty(value = "我的分数")
    private Integer score;

    /**
     * 顶部提示
     */
    @ApiModelProperty(value = "顶部提示")
    private String topTip;
    /**
     * 作业卡信息
     */
    private List<WorkModelDetailVO> workModelDetails;
    @ApiModelProperty(value = "作业状态")
    private Integer workStatus;
    @ApiModelProperty(value = "附加作业信息")
    private AdditionalInfoVO additionalInfo;

    @ApiModelProperty(value = "班级id")
    private Integer classesId;

}
