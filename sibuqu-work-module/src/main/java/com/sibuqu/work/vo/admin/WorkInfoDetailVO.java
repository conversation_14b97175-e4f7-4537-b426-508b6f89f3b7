package com.sibuqu.work.vo.admin;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sibuqu.work.vo.api.AdditionalInfoVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 后台管理系统中的作业详情
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WorkInfoDetailVO {
    @ApiModelProperty("学员手机号")
    private String userPhone;
    @ApiModelProperty("学员姓名")
    private String userName;

    @ApiModelProperty(value ="课件名称")
    private String courseWareName;


    @ApiModelProperty("作业归属日期")
    private String courseBeginTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(value="作业提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;


    @ApiModelProperty(value ="当日合计得分")//某天作业 多个作业项之和
    private Integer dayTotalScore;

    @ApiModelProperty(value ="作业累计得总分")
    private Integer totalScore;


    @ApiModelProperty(value="推荐时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime recommendTime;

    @ApiModelProperty(value="是否推荐0未推荐1已推荐")
    private Integer recommendStatus;

    @ApiModelProperty(value="是否推荐给老师0未推荐1已推荐")
    private Integer recommendTeacherStatus;

    @ApiModelProperty(value="推荐给老师的时间")
    private LocalDateTime recommendTeacherTime;

    @ApiModelProperty(value="老师回复状态 0 未回复 1:已回复")
    private Integer teacherReplyStatus;

    @ApiModelProperty(value ="老师回复时间")//去最后面一次回复的时间
    private LocalDateTime teacherReplyDate;


    @ApiModelProperty("班级号")
    private String classesNo;

    @ApiModelProperty("班级名称")
    private String classesName;
    @ApiModelProperty("班主任手机号")
    private String classesTeacherPhone;
    @ApiModelProperty("班主任姓名")
    private String classesTeacherName;


/*    @ApiModelProperty(value ="作业累计得总分")
    private Integer totalScore;*/


    @ApiModelProperty("作业详情")
    private List<WorkModelDetailVO> workDetails;

    @ApiModelProperty("老师回复列表信息")
    private List<WorkTeacherReplyVO> workTeacherReplyVos;

   // @ApiModelProperty("附加作业详情")
    //private AdditionalInfoVO additionalInfo;
}
