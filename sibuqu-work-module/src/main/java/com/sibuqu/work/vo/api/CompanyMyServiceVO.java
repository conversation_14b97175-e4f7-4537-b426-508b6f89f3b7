package com.sibuqu.work.vo.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CompanyMyServiceVO {

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "跳转类型 1企业作业 2回复作业 3意见反馈 4在线客服 5我的证书")
    private Integer jumpType;

    @ApiModelProperty(value = "图片地址")
    private String imgUrl;

    @ApiModelProperty(value = "跳转链接")
    private String jumpUrl;
}
