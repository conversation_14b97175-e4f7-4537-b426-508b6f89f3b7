package com.sibuqu.work.vo.admin;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * 后台作业列表
 * <AUTHOR>
 */
@Data
public class WorkInfoListExportVO {


    @Excel(name="作业归属日期", orderNum = "1")
    private String courseBeginTime;

    /**
     * 创建时间
     */
    @Excel(name="作业提交时间" , orderNum = "2")
    private String createTimeStr;

    @Excel(name = "学员姓名" , orderNum = "4")
    private String userName;

    @Excel(name = "课件名称" , orderNum = "5")
    private String courseWareName;

    @Excel(name = "学员手机号" , orderNum = "6")
    private String userPhone;
    @Excel(name = "作业是否推荐" , orderNum = "7")
    private String recommendStatusStr;
    /**
     * 是否有班级群
     * 需要判断一下  true: 是 false:否
     */
    @Excel(name = "是否有班级" , orderNum = "8")
    private String haveClassStr;

    @Excel(name = "班级号" , orderNum = "10")
    private String classesNo;

    @Excel(name = "班级名称" , orderNum = "12")
    private String classesName;

    @Excel(name = "班主任姓名" , orderNum = "14")
    private String classesTeacherName;


    @Excel(name = "班主任手机号" , orderNum = "16")
    private String classesTeacherPhone;

    @Excel(name ="作业项1" , orderNum = "20")
    private String workContentOne;
    @Excel(name ="作业项2" , orderNum = "21")
    private String workContentTwo;
    @Excel(name ="作业项3", orderNum = "22")
    private String workContentThree;
    @Excel(name ="作业项项4", orderNum = "23")
    private String workContentFour;
    @Excel(name ="作业项5", orderNum = "24")
    private String workContentFive;

    @Excel(name = "当日合计得分" , orderNum = "29")
    private Integer dayTotalScore;
    @Excel(name = "作业累计得总分" , orderNum = "30")
    private Integer totalScore;
    /**
     * vo 转为 excel的  Model
     * @param vo
     * @return
     */
    public static WorkInfoListExportVO vo2ExportModel(WorkInfoListVO vo) {
        if (null == vo) {
            return null;
        }
        WorkInfoListExportVO result = new WorkInfoListExportVO();
        BeanUtils.copyProperties(vo, result);
        if(vo.getHaveClass()){
            result.setHaveClassStr("是");
        }else{
            result.setHaveClassStr("否");
        }
        if (vo.getRecommendStatus() != null && vo.getRecommendStatus() == 1) {
            result.setRecommendStatusStr("已推荐");
        }else {
            result.setRecommendStatusStr("未推荐");
        }

        result.setCreateTimeStr(DateUtil.format(vo.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN));

        return result;
    }

}
