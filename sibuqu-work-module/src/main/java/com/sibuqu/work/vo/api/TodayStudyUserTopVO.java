package com.sibuqu.work.vo.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/12/9-下午3:29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TodayStudyUserTopVO {
    @ApiModelProperty(value = "用户姓名")
    private String userName;
    @ApiModelProperty(value = "分数")
    private Integer score;
    @ApiModelProperty(value = "班主任姓名")
    private String teacherName;
    @ApiModelProperty(value = "班级号")
    private Integer classGroupId;
    private Integer userId;
    @ApiModelProperty("用户标识0新学员1老学员")
    private Boolean userFlag;

}
