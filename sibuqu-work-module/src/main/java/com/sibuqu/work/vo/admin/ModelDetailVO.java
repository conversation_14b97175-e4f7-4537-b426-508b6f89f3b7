package com.sibuqu.work.vo.admin;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ModelDetailVO {
    /**
     * id
     */
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 作业项名称
     */
    @ApiModelProperty(value="作业项名称")
    private String title;

    /**
     * 作业项类型 1-听课类型 2-打卡 3-文本 4-数字 5-评价 6-无打卡只显示提示 7-附加作业 8-选择题 9-打卡不加分 10-笃行 11-分享加分 12-多选题 13-NPS 14-语音加文本 15-动态多选题 16-动态单选题 17-读书
     */
    @ApiModelProperty(value="作业项类型 1-听课类型 2-打卡 3-文本 4-数字 5-评价 6-无打卡只显示提示 7-附加作业 8-选择题 9-打卡不加分 10-笃行 11-分享加分 12-多选题 13-NPS 14-语音加文本 15-动态多选题 16-动态单选题 17-读书")
    private Integer type; //0，1内容的 类型  1听课类型2打卡  9打卡不加分 11分享加分 12多选题

    /**
     * 作业提示
     */
    @ApiModelProperty(value="作业提示")
    private String prompt;

    /**
     * 描述
     */
    @ApiModelProperty(value="描述")
    private String description;

    @ApiModelProperty(value="描述1")
    private String descriptionOne;

    @ApiModelProperty(value="描述2")
    private String descriptionTwo;
    /**
     * 最大值
     */
    @ApiModelProperty(value="最大值")
    private Integer maxLength;

    /**
     * 最小值
     */
    @ApiModelProperty(value="最小值")
    private Integer minLength;

    /**
     * 是否可编辑1可手动编辑2不可手动编辑
     */
    @ApiModelProperty(value="是否可编辑1可手动编辑2不可手动编辑")
    private Integer manualStatus;

    /**
     * 分值
     */
    @ApiModelProperty(value="分值")
    private Integer score;

    /**
     * 排序
     */
    @ApiModelProperty(value="排序")
    private Integer sorted;

    @ApiModelProperty(value="作业内容")
    private String content;

    @ApiModelProperty(value="作业语音内容")
    private List<WorkModelAudioVO> audioList;

    @ApiModelProperty(value="默认值")
    private Integer defaultValue;

    @ApiModelProperty(value="展示状态0不展示1展示")
    private Integer showStatus;

    @ApiModelProperty(value="展示标题")
    private String showTitle;

    @ApiModelProperty(value="提示词")
    private String words;

    @ApiModelProperty(value="选择题答案")
    private String answer;

    @ApiModelProperty(value="作业项分享图")
    private String shareImageUrl;

    @ApiModelProperty(value="作业项分享状态1支持分享 0不支持")
    private Integer shareStatus;

    @ApiModelProperty(value = "选项列表")
    private List<String> optionList;

    @ApiModelProperty(value = "1-复选框 2-小红花")
    private Integer showStyle;

}
