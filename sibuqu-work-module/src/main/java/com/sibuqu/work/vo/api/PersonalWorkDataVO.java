package com.sibuqu.work.vo.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class PersonalWorkDataVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("截止时间")
    private LocalDateTime deadlineTime;

    @ApiModelProperty("累计作业率")
    private Double accWorkRate;

    @ApiModelProperty("累计听课率")
    private Double accListenRate;

    @ApiModelProperty("累计作业率")
    private String accWorkRateStr = "0.00%";

    @ApiModelProperty("累计听课率")
    private String accListenRateStr = "0.00%";

}
