package com.sibuqu.work.vo.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/12/9-下午3:38
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ClassSubRateVO {

    @ApiModelProperty(value = "学习班号")
    private Long classGroupId;
    @ApiModelProperty(value = "班主任姓名")
    private String classTeacherName;
    @ApiModelProperty(value = "作业率")
    private String subRate;
    @ApiModelProperty(value = "听课率")
    private String listenRate;

    private String classTeacherPhone;

    @ApiModelProperty("课件id")
    private Integer courseTimetableId;

}
