package com.sibuqu.work.vo;

import com.sibuqu.work.bo.ContentBO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@ApiModel(description = "inno周作业信息VO")
@Data
public class InnoWeeklyInfoVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "课程ID")
    private Integer courseId;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "用户积分")
    private Long userPoints;

    @ApiModelProperty(value = "周起始日期")
    private LocalDate weeklyBeginDate;

    @ApiModelProperty(value = "周截止日期")
    private LocalDate weeklyEndDate;

    @ApiModelProperty(value = "周作业开始时间")
    private LocalDateTime workBeginDatetime;

    @ApiModelProperty(value = "周作业截止时间")
    private LocalDateTime workEndDatetime;

    @ApiModelProperty(value = "周作业结束时间差值（秒）")
    private Long workEndDatetimeDiff;

    @ApiModelProperty(value = "作业内容最小字数")
    private Integer minContentLength = 10;

    @ApiModelProperty(value = "作业内容最大字数")
    private Integer maxContentLength = 2000;

    @ApiModelProperty(value = "内容列表")
    private List<ContentBO> contentList;

    @ApiModelProperty(value = "是否在部落里")
    private Boolean tribeFlag;

}