package com.sibuqu.work.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class WorkInnoInfoVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "课程id")
    private Integer courseId;

    @ApiModelProperty(value = "课件id")
    private Integer courseTimetableId;

    @ApiModelProperty(value = "课件名称")
    private String courseTimetableName;

    @ApiModelProperty(value = "用户积分")
    private Long userPoints = 0L;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "海报")
    private String poster;

    @ApiModelProperty(value = "作业内容最小字数")
    private Integer minContentLength = 5;

    @ApiModelProperty(value = "作业内容最大字数")
    private Integer maxContentLength = 60;

    @ApiModelProperty(value = "其他学员用户作业内容")
    private String otherUserContent = "本心之明，皎如白日";

    @ApiModelProperty(value = "推荐作业内容")
    private List<RecommendWorkItemDTO> recommendWorkList;

}