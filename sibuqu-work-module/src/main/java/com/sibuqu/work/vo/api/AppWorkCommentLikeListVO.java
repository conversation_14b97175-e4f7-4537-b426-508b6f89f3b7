package com.sibuqu.work.vo.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022年5月17日16:08:39
 * @Version 1.0
 * @Description 心得评论点赞列表VO  特殊说明：因为APP端想改动最小化  暂时所有字段用的老代码字段 做映射
 **/
@Data
public class AppWorkCommentLikeListVO {
    @ApiModelProperty(value = "主键ID",dataType = "Integer")
    private Integer id;

    @ApiModelProperty(value = "企业 id")
    private Integer companyId;

    @ApiModelProperty(value = "心得ID",dataType = "Integer")
    private Integer postId;

    @ApiModelProperty(value = "点赞用户ID",dataType = "Integer")
    private Integer postUserId;

    @ApiModelProperty(value = "点赞用户名称",dataType = "String")
    private String postUserName;

    public String getPostUserName()
    {
        if(null==postUserName)
        {
            return "匿名用户";
        }
        return postUserName;
    }

}
