package com.sibuqu.work.vo.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class CompanyRankVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("截止时间")
    private LocalDateTime deadlineTime;

    @ApiModelProperty("当期作业率")
    private Double currentWorkRate;

    @ApiModelProperty("当期听课率")
    private Double currentListenRate;

    @ApiModelProperty("累计作业率")
    private Double accWorkRate;

    @ApiModelProperty("累计听课率")
    private Double accListenRate;

    @ApiModelProperty("当期作业率")
    private String currentWorkRateStr = "0.00%";

    @ApiModelProperty("当期听课率")
    private String currentListenRateStr = "0.00%";

    @ApiModelProperty("累计作业率")
    private String accWorkRateStr = "0.00%";

    @ApiModelProperty("累计听课率")
    private String accListenRateStr = "0.00%";

    @ApiModelProperty("当前排名")
    private Integer currentRank;

    @ApiModelProperty("排序列表")
    private List<CompanyRankItemVO> rankList;

}
