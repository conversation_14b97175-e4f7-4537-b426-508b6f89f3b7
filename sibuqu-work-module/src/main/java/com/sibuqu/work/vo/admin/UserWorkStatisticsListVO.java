package com.sibuqu.work.vo.admin;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

@Data
public class UserWorkStatisticsListVO {
    @ApiModelProperty("所属日期")
    private LocalDate belongDate;
    @ApiModelProperty("用户手机号")
    private String userPhone;
    @ApiModelProperty("用户姓名")
    private String userName;
    @ApiModelProperty("听课状态")
    private Boolean listenStatus;
    @ApiModelProperty("作业状态")
    private Boolean workStatus;
    @ApiModelProperty("课程名称")
    private String courseName;
    @ApiModelProperty("班级号")
    private Integer classesNo;
    @ApiModelProperty("班级名称")
    private String classesName;
    @ApiModelProperty("小组id")
    private Integer teamId;
}
