package com.sibuqu.work.vo.api;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022年5月17日16:08:39
 * @Version 1.0
 * @Description 心得列表VO  特殊说明：因为APP端想改动最小化  暂时所有字段用的老代码字段 做映射
 **/
@Data
public class AppWorkInfoListVO {

    @ApiModelProperty(value = "学习心得ID",dataType = "Integer")
    private Integer id;

    @ApiModelProperty(value = "点赞状态（0未点赞 1已点赞）",dataType = "Integer")
    private Integer postState;

    @ApiModelProperty(value = "内容（老版本课程作业内容）",dataType = "String")
    private String postContent;

    @ApiModelProperty(value = "内容（新版本课程作业内容，有变化之前是字符串 现在是集合）",dataType = "Array")
    private List<AppWorkDetailListVO> workContentList;

    @ApiModelProperty(value = "班级ID",dataType = "Integer")
    private Integer groupId;

    @ApiModelProperty(value = "创建时间",dataType = "LocalDateTime")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "发布用户ID",dataType = "Integer")
    private Integer userId;

    @ApiModelProperty(value = "发布用户名字",dataType = "String")
    private String userName;

    @ApiModelProperty(value = "推荐状态（0未推荐 1已推荐）")
    private Integer recommendStatus;

    @ApiModelProperty(value = "所属日期")
    private String doneDate;

    @ApiModelProperty(value = "班主任名称",dataType = "String")
    private String classTeacherName;

    @ApiModelProperty(value = "今日分数",dataType = "Integer")
    private Integer score;

    @ApiModelProperty(value = "总分数",dataType = "Integer")
    private Integer scoreTotal;

    @ApiModelProperty(value = "评论列表信息",dataType = "List")
    private List<AppWorkCommentListVO> commentList;

    @ApiModelProperty(value = "点赞列表信息",dataType = "List")
    private List<AppWorkCommentLikeListVO> likeList;

    @ApiModelProperty(value = "老师回复心得的列表",dataType = "List")
    private List<AppWorkTeacherReplyVO> teacherReplyList;

    @ApiModelProperty("作业模版标识（0统一配置作业模版 1区分老学员配置作业模版）")
    private Integer workModelFlag;

    @ApiModelProperty("用户标识0新学员1老学员")
    private Integer userFlag;
    @ApiModelProperty("作业归属日期")
    private LocalDate belongDate;

    @ApiModelProperty("课程名称")
    private String courseTitle;

    @ApiModelProperty("课件名称")
    private String resourceTitle;

    @ApiModelProperty("学员头像")
    private String avatar;

    @ApiModelProperty("是否是我的企业")
    private Boolean isMyCompany;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("课件id")
    private Integer courseTimetableId;

    @ApiModelProperty(value = "作业模板等级 a b c")
    private String level;

    @ApiModelProperty("课程缩略图")
    private String courseImgUrl;

}
