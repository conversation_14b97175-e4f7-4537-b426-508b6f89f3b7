package com.sibuqu.work.vo.admin;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClassWorkStatisticsVO {
    @ApiModelProperty(value="听课总数")
    private Integer listenTotal;
    @ApiModelProperty(value="听课数")
    private Integer listenNum;
    @ApiModelProperty(value="听课率")
    private String listenRate;
    @ApiModelProperty(value="作业总数")
    private Integer workTotal;
    @ApiModelProperty(value="做作业数")
    private Integer workNum;
    @ApiModelProperty(value="作业率")
    private String workRate;
}
