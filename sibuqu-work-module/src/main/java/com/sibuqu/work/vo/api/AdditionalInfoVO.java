package com.sibuqu.work.vo.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/8/10-下午3:12
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AdditionalInfoVO {
    @ApiModelProperty("附加题id")
    private Integer additionalId;
    @ApiModelProperty("标题")
    private String title;
    @ApiModelProperty("链接地址")
    private String linkAddress;
    @ApiModelProperty("附加")
    private Integer additionalScore;
    @ApiModelProperty("简介")
    private String summary;
    @ApiModelProperty("图片链接")
    private String imgUrl;
    @ApiModelProperty("分享词")
    private String shareWords;
    @ApiModelProperty("附加作业完成状态")
    private Integer status;
}
