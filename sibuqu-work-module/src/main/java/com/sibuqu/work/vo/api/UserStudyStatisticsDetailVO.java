package com.sibuqu.work.vo.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserStudyStatisticsDetailVO {
    @ApiModelProperty("所属日期")
    private LocalDate belongDate;
    @ApiModelProperty("听课状态")
    private Boolean listenStatus;
    @ApiModelProperty("作业状态")
    private Boolean workStatus;
    @ApiModelProperty("分数")
    private Integer workScore;
    @ApiModelProperty("课程表id")
    private Integer courseTimetableId;
}
