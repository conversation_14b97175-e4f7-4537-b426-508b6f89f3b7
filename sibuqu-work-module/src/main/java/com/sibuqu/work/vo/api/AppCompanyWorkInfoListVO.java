package com.sibuqu.work.vo.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppCompanyWorkInfoListVO {
    @ApiModelProperty("归属日期")
    private LocalDate beginDate;

    @ApiModelProperty("课程名称")
    private String courseName;

    @ApiModelProperty("课件名称")
    private String resourceTitle;

    @ApiModelProperty("作业列表")
    private List<AppWorkInfoListVO> workList;
}
