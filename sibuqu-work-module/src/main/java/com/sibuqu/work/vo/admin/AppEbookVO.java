package com.sibuqu.work.vo.admin;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AppEbookVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "电子书id")
    private Long ebookId;

    @ApiModelProperty(value = "电子书名字")
    private String ebookName;

    @ApiModelProperty(value = "章节信息")
    private List<AppChapterVO> chapterList;

}
