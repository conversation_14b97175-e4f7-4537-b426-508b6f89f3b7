package com.sibuqu.work.vo.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class OpenCompanyWorkVO {
    @ApiModelProperty("学员名称")
    private String userName;
    @ApiModelProperty("学员手机号")
    private String phone;
    @ApiModelProperty("作业状态1已写 0未写")
    private Integer workStatus;
    @ApiModelProperty("听课状态1已听课0未听课")
    private Integer listenStatus;
    @ApiModelProperty("写作业时间")
    private LocalDateTime workTime;
    @ApiModelProperty("作业归属日期")
    private LocalDate workBelongToDate;
    @ApiModelProperty("当日得分")
    private Integer score;
}
