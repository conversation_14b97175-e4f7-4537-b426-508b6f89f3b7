package com.sibuqu.work.vo.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkingByDateVO {
    private Integer userId;
    @ApiModelProperty(value = "所属日期")
    private LocalDate belongDate;
    @ApiModelProperty(value = "用户名")
    private String username;
    @ApiModelProperty(value = "听课状态 true完成 false未完成")
    private Boolean listenStatus;
    @ApiModelProperty(value = "作业状态 true完成 false未完成")
    private Boolean workStatus;
    @ApiModelProperty(value = "积分")
    private Integer workScore;
    @ApiModelProperty(value = "累计积分")
    private Integer score;
    @ApiModelProperty("是否展示新学员标识")
    private Boolean userFlag = false;
    @ApiModelProperty("作业模版标识（0-统一配置作业模版 1-区分老学员配置作业模版 2-分等级作业模板）")
    private Integer workModelFlag;
    @ApiModelProperty(value = "作业模板等级 a b c")
    private String level;
}
