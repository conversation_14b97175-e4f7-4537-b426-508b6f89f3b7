package com.sibuqu.work.vo.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class PersonalWorkRankItemVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("序号")
    private Integer rankNum;

    @ApiModelProperty(value = "用户id")
    private Integer userId;

    @ApiModelProperty("学员名称")
    private String userName;

    @ApiModelProperty("累计作业率")
    private Double accWorkRate;

    @ApiModelProperty("累计听课率")
    private Double accListenRate;

    @ApiModelProperty("累计作业率")
    private String accWorkRateStr;

    @ApiModelProperty("累计听课率")
    private String accListenRateStr;

}
