package com.sibuqu.work.vo.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class JumpPageWorkRemindParamVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("班级 id")
    private Integer classesId;

    @ApiModelProperty("商品id")
    private Integer skuId;

    @ApiModelProperty("群组名称")
    private String className;

    @ApiModelProperty(value = "课程类型")
    private Integer courseType;
}
