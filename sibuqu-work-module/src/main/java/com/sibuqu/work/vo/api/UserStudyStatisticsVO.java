package com.sibuqu.work.vo.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserStudyStatisticsVO {
    @ApiModelProperty("用户id")
    private Integer userId;
    @ApiModelProperty("姓名")
    private String username;
    @ApiModelProperty("小组名")
    private String teamName;
    @ApiModelProperty("手机号")
    private String userPhone;
    @ApiModelProperty("分数")
    private Integer score;
    @ApiModelProperty("头像")
    private String avatar;

    @ApiModelProperty("学习情况明细")
    private List<UserStudyStatisticsDetailVO> userStudyDetails;
}
