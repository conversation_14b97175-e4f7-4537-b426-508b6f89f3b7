package com.sibuqu.work.vo.admin;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class ClassWorkInfoVO {

    @ApiModelProperty(value="班级号")
    private String classesNo;

    @ApiModelProperty(value="班主任用户ID")
    private Integer classesTeacherId;

    @ApiModelProperty("班主任姓名")
    private String classesTeacherName;

    @ApiModelProperty(value="班主任手机号")
    private String classTeacherPhone;

    @ApiModelProperty(value="作业所属日期")
    private LocalDate belongDate;

    @ApiModelProperty(value="累计作业上交率")
    private String totalSubRate;

    @ApiModelProperty(value="归属日期作业上交率")
    private String belongDateSubRate;

    @ApiModelProperty(value="归属日期作业未交人名称")
    private String unsubUserName;

    @ApiModelProperty(value="归属日期未学习人员名称")
    private String unListenUserName;

    @ApiModelProperty(value="课程ID")
    private Integer courseId;

    @ApiModelProperty("作业模版标识（0统一配置作业模版 1区分老学员配置作业模版）")
    private Integer workModelFlag;

    @ApiModelProperty("模版列表")
    private List<WorkModelVO> workModelList;
}
