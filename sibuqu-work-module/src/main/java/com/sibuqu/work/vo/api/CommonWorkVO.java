package com.sibuqu.work.vo.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;


@Data
public class CommonWorkVO {

    @ApiModelProperty(value = "作业id")
    private Integer id;

    // 作业内容
    @ApiModelProperty(value = "内容")
    private List<AppWorkDetailListVO> contentList;

    // 用户信息
    @ApiModelProperty(value = "用户id")
    private Integer userId;

    @ApiModelProperty(value = "用户姓名")
    private String userName;

    // 企业信息
    @ApiModelProperty(value = "企业id")
    private Integer companyId;

    @ApiModelProperty(value = "企业名称")
    private String companyName;

    // 课程信息
    @ApiModelProperty(value = "课程id")
    private Integer courseId;

    @ApiModelProperty(value = "课程名称")
    private String courseName;

    @ApiModelProperty("商品ID")
    private Integer goodsId;

    // 课件信息
    @ApiModelProperty(value = "课件id")
    private Integer courseTimetableId;

    @ApiModelProperty("课件名称")
    private String resourceTitle;

    // 班级信息
    @ApiModelProperty(value = "班级号")
    private Integer classesNo;

    @ApiModelProperty(value = "班级名称")
    private String classesName;

    @ApiModelProperty(value = "班主任名称")
    private String classesTeacherName;

    // 评论点赞信息
    @ApiModelProperty("评论数量")
    private Integer commentTotal;

    @ApiModelProperty("点赞数量")
    private Integer likeTotal;

    @ApiModelProperty("是否点过赞 1-已点赞 0-未点赞")
    private Integer likeFlag;

    @ApiModelProperty(value = "评论列表信息")
    private List<AppWorkCommentListVO> commentList;

    // 回应信息
    @ApiModelProperty(value = "老师回复心得的列表")
    private List<WorkReplyVO> replyList;

    // 时间  推荐
    @ApiModelProperty("时间（转换中文）")
    private String time;

    @ApiModelProperty(value = "是否推荐0未推荐1已推荐")
    private Integer recommendStatus;

    @ApiModelProperty(value = "作业时间")
    private LocalDateTime workTime;

}
