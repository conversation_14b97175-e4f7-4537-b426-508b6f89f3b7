package com.sibuqu.work.vo.admin;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 后台作业列表
 * <AUTHOR>
 */
@Data
public class WorkInfoListVO {
    @ApiModelProperty(value ="作业id")
    private Integer id;
    @ApiModelProperty(value ="学员Id")
    private Integer userId;

    @ApiModelProperty("作业归属日期")
    private String courseBeginTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(value="作业提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value ="学员姓名")
    private String userName;

    @ApiModelProperty(value ="课件名称")
    private String courseWareName;

    @ApiModelProperty(value ="学员手机号")
    private String userPhone;
    /**
     * 是否有班级群
     */
    @ApiModelProperty(value ="是否有班级 true: 是 false:否")
    private Boolean haveClass;

    @ApiModelProperty(value ="班级号")
    private String classesNo;

    @ApiModelProperty(value ="班级名称")
    private String classesName;

    @ApiModelProperty(value ="班主任姓名")//需要查
    private String classesTeacherName;


    @ApiModelProperty(value ="班主任手机号")//需要查
    private String classesTeacherPhone;

    @ApiModelProperty(value ="当日合计得分")//某天作业 多个作业项之和
    private Integer dayTotalScore;


    @ApiModelProperty(value ="内容项1")
    private String workContentOne;
    @ApiModelProperty(value ="内容项2")
    private String workContentTwo;
    @ApiModelProperty(value ="内容项3")
    private String workContentThree;
    @ApiModelProperty(value ="内容项4")
    private String workContentFour;
    @ApiModelProperty(value ="内容项5")
    private String workContentFive;
    @ApiModelProperty(value ="作业项1")
    private String workTitleOne;
    @ApiModelProperty(value ="作业项2")
    private String workTitleTwo;
    @ApiModelProperty(value ="作业项3")
    private String workTitleThree;
    @ApiModelProperty(value ="作业项4")
    private String workTitleFour;
    @ApiModelProperty(value ="作业项5")
    private String workTitleFive;


    @ApiModelProperty(value ="作业累计得总分")
    private Integer totalScore;

    @ApiModelProperty(value="推荐时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime recommendTime;

    @ApiModelProperty(value="是否推荐0未推荐1已推荐")
    private Integer recommendStatus;

    @ApiModelProperty(value="是否推荐给老师0未推荐1已推荐")
    private Integer recommendTeacherStatus;

    @ApiModelProperty(value="推荐给老师的时间")
    private LocalDateTime recommendTeacherTime;

    @ApiModelProperty(value="老师回复状态 0 未回复 1:已回复")
    private Integer teacherReplyStatus;

    @ApiModelProperty(value ="老师回复时间")//去最后面一次回复的时间
    private LocalDateTime teacherReplyDate;

    @ApiModelProperty(value="作业1显示 0隐藏'")
    private Integer showStatus;
}
