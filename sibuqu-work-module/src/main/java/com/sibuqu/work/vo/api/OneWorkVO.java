package com.sibuqu.work.vo.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class OneWorkVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "作业ID")
    private Integer id;

    @ApiModelProperty(value = "内容")
    private List<AppWorkDetailListVO> workContentList;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "发布用户ID")
    private Integer userId;

    @ApiModelProperty(value = "发布用户名字")
    private String userName;

    @ApiModelProperty(value = "老师回复心得的列表")
    private List<AppWorkTeacherReplyVO> teacherReplyList;

    @ApiModelProperty(value = "课程id")
    private Integer courseId;

    @ApiModelProperty("课程名称")
    private String courseTitle;

    @ApiModelProperty("课程缩略图")
    private String courseImgUrl;

    @ApiModelProperty("课件名称")
    private String resourceTitle;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("课件id")
    private Integer courseTimetableId;

    @ApiModelProperty("点赞数量")
    private Integer likeTotal;

    @ApiModelProperty("是否点过赞 1-已点赞 0-未点赞")
    private Integer likeFlag;

    @ApiModelProperty("标签列表")
    private List<CommonLabelVO> commonLabelVOS;

    @ApiModelProperty(value = "企业id")
    private Integer companyId;

}
