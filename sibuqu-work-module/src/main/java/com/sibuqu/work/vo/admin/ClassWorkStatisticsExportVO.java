package com.sibuqu.work.vo.admin;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.time.LocalDate;

@Data
public class ClassWorkStatisticsExportVO {

    @Excel(name = "所属日期", orderNum = "1")
    private LocalDate belongDate;

    @Excel(name = "课件名称", orderNum = "2")
    private String courseWareName;

    @Excel(name = "班级人数", orderNum = "3")
    private Integer memberTotal;

    @Excel(name = "听课人数", orderNum = "4")
    private Integer listenNum;

    @Excel(name = "未听课人数", orderNum = "5")
    private Integer unListenNum;

    @Excel(name = "交作业人数", orderNum = "6")
    private Integer workNum;

    @Excel(name = "未交作业人数", orderNum = "7")
    private Integer unWorkNum;

}
