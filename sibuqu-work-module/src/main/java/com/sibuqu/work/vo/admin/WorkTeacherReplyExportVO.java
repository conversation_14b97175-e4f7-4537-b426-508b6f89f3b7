package com.sibuqu.work.vo.admin;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.sibuqu.work.enums.RecommendStatusEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;

@Data
public class WorkTeacherReplyExportVO {

    @Excel(name = "学员姓名" , orderNum = "2")
    private String userName;
    @Excel(name = "学员手机号" , orderNum = "3")
    private String userPhone;
    @Excel(name="作业归属日期", orderNum = "4",width = 30)
    private String courseBeginTimeStr;
    @Excel(name="课件名称", orderNum = "5",width = 30)
    private String courseWareName;
    @Excel(name="老师回复的内容", orderNum = "6",width = 30)
    private String teacherReply;

    @Excel(name ="作业项1" , orderNum = "7")
    private String workContentOne;
    @Excel(name ="作业项2" , orderNum = "8")
    private String workContentTwo;

    @Excel(name="是否推荐0未推荐1已推荐" , orderNum = "9")
    private String recommendStatusStr;

    @Excel(name="是否AI回复 0:否 1:是" , orderNum = "10")
    private String aiReplyStatusStr;

    @Excel(name="回复人" , orderNum = "11")
    private String teacherName;

    @Excel(name = "回复时间" , orderNum = "12",width = 30)//去最后面一次回复的时间
    private String teacherReplyDateStr;

    @Excel(name="作业1显示 0显示 1隐藏" , orderNum = "13")
    private String showStatusStr;

    public static WorkTeacherReplyExportVO vo2ExportModel(WorkTeacherReplyVO vo) {
        if (null == vo) {
            return null;
        }
        WorkTeacherReplyExportVO result = new WorkTeacherReplyExportVO();
        BeanUtils.copyProperties(vo, result);

        result.setCourseBeginTimeStr(DateUtil.format(vo.getCourseBeginTime(), DatePattern.NORM_DATE_PATTERN));
        result.setTeacherReplyDateStr(DateUtil.format(vo.getTeacherReplyTime(), DatePattern.NORM_DATETIME_PATTERN));
        if(new Integer(1).equals(vo.getRecommendStatus())){
            result.setRecommendStatusStr("已推荐");
        }else{
            result.setRecommendStatusStr("未推荐");
        }
        if(new Integer(1).equals(vo.getShowStatus())){
            result.setShowStatusStr("隐藏");
        }else{
            result.setShowStatusStr("显示");
        }
        if(new Integer(1).equals(vo.getAiReplyStatus())){
            result.setAiReplyStatusStr("是");
        }else{
            result.setAiReplyStatusStr("否");
        }
        return result;
    }

}
