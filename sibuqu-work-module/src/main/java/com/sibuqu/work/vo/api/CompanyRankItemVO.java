package com.sibuqu.work.vo.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class CompanyRankItemVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("序号")
    private Integer rankNum;

    @ApiModelProperty("企业ID")
    private Integer companyId;

    @ApiModelProperty("企业名称")
    private String companyName;

    @ApiModelProperty("当期作业率")
    private Double currentWorkRate;

    @ApiModelProperty("当期听课率")
    private Double currentListenRate;

    @ApiModelProperty("当期作业率")
    private String currentWorkRateStr;

    @ApiModelProperty("当期听课率")
    private String currentListenRateStr;

}
