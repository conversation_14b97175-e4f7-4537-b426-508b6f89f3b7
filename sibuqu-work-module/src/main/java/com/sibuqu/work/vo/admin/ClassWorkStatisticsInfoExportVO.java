package com.sibuqu.work.vo.admin;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.time.LocalDate;

@Data
public class ClassWorkStatisticsInfoExportVO {

    @Excel(name = "所属日期", orderNum = "1")
    private LocalDate belongDate;

    @Excel(name = "学员名称", orderNum = "2")
    private String userName;

    @Excel(name = "学员手机号 ", orderNum = "3")
    private String userPhone;

    @Excel(name = "当日合计得分", orderNum = "4")
    private Integer dayTotalScore;

    @Excel(name = "作业累计得总分", orderNum = "5")
    private Integer totalScore;

}
