package com.sibuqu.work.vo.admin;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

@Data
public class TeamWorkStatisticsListVO {
    @ApiModelProperty(value="所属日期")
    private LocalDate belongDate;
    @ApiModelProperty("班级id")
    private Integer classesId;
    @ApiModelProperty(value="班级号")
    private String classesNo;
    @ApiModelProperty(value="班级名称")
    private String classesName;
    @ApiModelProperty(value="听课人数")
    private Integer listenNum;
    @ApiModelProperty(value="作业人数")
    private Integer workNum;
    @ApiModelProperty(value="课程名称")
    private String courseName;
}
