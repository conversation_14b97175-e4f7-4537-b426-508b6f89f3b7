package com.sibuqu.work.vo.admin;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class UserWorkListExportVO {

    @Excel(name = "所属日期", orderNum = "1")
    private LocalDate belongDate;

    @Excel(name = "用户手机", orderNum = "2")
    private String userPhone;

    @Excel(name = "用户姓名", orderNum = "3")
    private String userName;

    @Excel(name = "作业分", orderNum = "4")
    private Integer score;

    @Excel(name = "内容项1", orderNum = "5")
    private String workContentOne;

    @Excel(name = "内容项2", orderNum = "6")
    private String workContentTwo;

    @Excel(name = "课件名称", orderNum = "7")
    private String resourceTitle;

    @Excel(name = "所属企业", orderNum = "8")
    private String companyName;
    @Excel(name = "作业提交时间", orderNum = "9")
    private String subWorkTime;

    @Excel(name = "课程名称", orderNum = "10")
    private String courseName;

    @Excel(name = "班级号", orderNum = "11")
    private Integer classesNo;

    @Excel(name = "班级名称", orderNum = "12")
    private String classesName;

    @Excel(name = "班主任手机号", orderNum = "13")
    private String classesTeacherPhone;

    @Excel(name = "班主任姓名", orderNum = "14")
    private String classesTeacherName;

    @Excel(name = "小组", orderNum = "15")
    private String teamName;

    @Excel(name = "是否推荐",replace = {"是_1","否_0"}, orderNum = "16")
    private Integer recommendStatus;

    @Excel(name = "作业推荐时间", orderNum = "17")
    private LocalDateTime recommendTime;

    @Excel(name = "推荐来源",replace = {"管理后台_1","企业管理员_2","班主任_3"}, orderNum = "18")
    private Integer recommendSource;

    @Excel(name = "推荐状态",replace = {"已推送_1","未推送_0"}, orderNum = "19")
    private Integer recommendTeacherStatus;

    @Excel(name = "回复状态",replace = {"已回复_1","未回复 _0"}, orderNum = "20")
    private Integer teacherReplyStatus;

    @Excel(name = "老师回复时间", orderNum = "21")
    private LocalDateTime teacherReplyTime;










}
