package com.sibuqu.work.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

@ApiModel(description = "inno周作业配置VO")
@Data
public class InnoWeeklyConfigVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "课程ID")
    private Integer courseId;

    @ApiModelProperty(value = "课程名称")
    private String courseName;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "能量果")
    private Long userPoints;

    @ApiModelProperty(value = "课程起始日期")
    private LocalDate courseBeginDate;

    @ApiModelProperty(value = "课程截止日期")
    private LocalDate courseEndDate;

    @ApiModelProperty(value = "周作业开始时间-星期几（1-7）")
    private Integer workBeginWeekday;

    @ApiModelProperty(value = "周作业开始时间-几点几分")
    private LocalTime workBeginTime;

    @ApiModelProperty(value = "周作业截止时间-星期几（1-7）")
    private Integer workEndWeekday;

    @ApiModelProperty(value = "周作业截止时间-几点几分")
    private LocalTime workEndTime;

    @ApiModelProperty(value = "创建人ID")
    private Integer createUserId;

    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改人ID")
    private Integer updateUserId;

    @ApiModelProperty(value = "修改人名称")
    private String updateUserName;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

}