package com.sibuqu.work.vo.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MakeUpWorkListVO {
    /**
     * 课程id
     */
    @ApiModelProperty(value = "课程id", required = true)
    private Integer courseId;
    /**
     * 作业模版id
     */
    @ApiModelProperty(value = "作业模版id")
    private Integer workModelId;

    @ApiModelProperty("课程表id")
    private Integer courseTimeTableId;

    @ApiModelProperty("课件名称")
    private String courseTimeTableName;

    @ApiModelProperty("课程开始时间")
    private LocalDateTime courseBeginTime;

    @ApiModelProperty("听课状态")
    private Boolean listenStatus;

    @ApiModelProperty("作业状态")
    private Boolean workStatus;

    @ApiModelProperty("分数")
    private Integer workScore;

    @ApiModelProperty("归属日期")
    private String belongDate;

    @ApiModelProperty("是否可以补交 1-可以 0-不可以")
    private Integer makeUpFlag;

}
