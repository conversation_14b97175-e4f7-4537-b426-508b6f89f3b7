package com.sibuqu.work.vo.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LearningSituationVO {
    @ApiModelProperty("课程数")
    private Integer courseNum;
    @ApiModelProperty("学习天数")
    private Long learnDays;
    @ApiModelProperty("总学分")
    private Integer learnScore;
}
