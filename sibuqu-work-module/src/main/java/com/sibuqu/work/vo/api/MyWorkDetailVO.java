package com.sibuqu.work.vo.api;

import com.sibuqu.work.vo.admin.WorkModelAudioVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MyWorkDetailVO {
    private Integer workId;
    /**
     * 作业项名称
     */
    @ApiModelProperty(value = "作业项名称")
    private String title;

    /**
     * 作业项类型 1-听课类型 2-打卡 3-文本 4-数字 5-评价 6-无打卡只显示提示 7-附加作业 8-选择题 9-打卡不加分 10-笃行 11-分享加分 12-多选题 13-NPS 14-语音加文本 15-动态多选题 16-动态单选题 17-读书
     */
    @ApiModelProperty(value = "作业项类型 1-听课类型 2-打卡 3-文本 4-数字 5-评价 6-无打卡只显示提示 7-附加作业 8-选择题 9-打卡不加分 10-笃行 11-分享加分 12-多选题 13-NPS 14-语音加文本 15-动态多选题 16-动态单选题 17-读书")
    private Integer type;

    private String prompt;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 分值
     */
    @ApiModelProperty(value = "分值")
    private Integer score;

    /**
     * 作业内容
     */
    @ApiModelProperty(value = "作业内容")
    private String content;

    @ApiModelProperty(value="作业语音内容")
    private List<WorkModelAudioVO> audioList;

    @ApiModelProperty(value = "提示词")
    private String words;

    @ApiModelProperty(value = "选择题答案")
    private String answer;

    @ApiModelProperty(value="作业项分享图")
    private String shareImageUrl;

    @ApiModelProperty(value="作业项分享状态1支持分享 0不支持")
    private String shareStatus;

}
