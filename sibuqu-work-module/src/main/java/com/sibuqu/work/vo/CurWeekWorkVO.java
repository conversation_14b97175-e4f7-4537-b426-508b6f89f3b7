package com.sibuqu.work.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

@Data
public class CurWeekWorkVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("当前日期")
    private LocalDate curDate;

    @ApiModelProperty("目标")
    private String goal;

    @ApiModelProperty("周内作业数据")
    private List<WeekWorkItemVO> itemList;

}