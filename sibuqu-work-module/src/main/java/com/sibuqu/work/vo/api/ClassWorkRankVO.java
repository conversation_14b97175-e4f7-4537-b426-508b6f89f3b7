package com.sibuqu.work.vo.api;

import com.sibuqu.base.common.page.PageInfoBT;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ClassWorkRankVO implements Serializable {
    private static final long serialVersionUID = 1L;


    @ApiModelProperty("当前排名")
    private Integer currentRank;

    @ApiModelProperty("排序列表")
    private PageInfoBT<ClassWorkRankItemVO> rankPage;

}
