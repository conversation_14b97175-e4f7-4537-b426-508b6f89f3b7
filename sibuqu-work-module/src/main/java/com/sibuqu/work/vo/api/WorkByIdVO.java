package com.sibuqu.work.vo.api;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class WorkByIdVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    private int id;

    @ApiModelProperty(value = "作业id")
    private Integer workId;

    @ApiModelProperty(value = "用户id")
    private Integer userId;

    @ApiModelProperty(value = "课程id")
    private Integer courseId;

    @ApiModelProperty(value = "课程表id")
    private Integer courseTimetableId;

    @ApiModelProperty(value = "作业时间")
    private LocalDateTime workTime;

    @ApiModelProperty(value = "班级id")
    private Integer classesId;

    @ApiModelProperty(value = "班级号")
    private Integer classesNo;

    @ApiModelProperty(value = "企业id")
    private Integer companyId;

}
