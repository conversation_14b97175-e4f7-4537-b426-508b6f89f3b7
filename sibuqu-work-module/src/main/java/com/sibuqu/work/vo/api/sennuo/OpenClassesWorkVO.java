package com.sibuqu.work.vo.api.sennuo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class OpenClassesWorkVO {


//    班级号：1300890
//    姓名
//            预习原文
//    听课
//            不讲一句善意的谎言
//    不讲一句抱怨的话语
//            复盘
//    践行笔记
//            作业日期
//    创建日期

    @ApiModelProperty("学员名称")
    private String userName;
    @ApiModelProperty("手机号")
    private String userPhone;
    @ApiModelProperty("预习原文 1完成 0未")
    private Integer preview;
    @ApiModelProperty("听课状态 1完成 0未")
    private Integer listen;
    @ApiModelProperty("谎言 1完成 0未")
    private Integer lie;
    @ApiModelProperty("抱怨 1完成 0未")
    private Integer complain;
    @ApiModelProperty("复盘 1完成 0未")
    private Integer replay;
    @ApiModelProperty("笔记 有值完成 空值未完成")
    private String note;
    @ApiModelProperty("写作业时间")
    private LocalDateTime workTime;
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
}
