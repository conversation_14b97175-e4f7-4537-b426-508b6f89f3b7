package com.sibuqu.work.vo.admin;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class WorkListVO {
    @ApiModelProperty(value ="作业id")
    private Integer id;

    @ApiModelProperty(value ="所属日期")
    private LocalDate belongDate;

    @ApiModelProperty(value ="用户手机")
    private String userPhone;

    @ApiModelProperty(value ="用户姓名")
    private String userName;

    @ApiModelProperty(value ="作业分")
    private Integer score;

    @ApiModelProperty(value ="内容项1")
    private String workContentOne;

    @ApiModelProperty(value ="内容项2")
    private String workContentTwo;

    @ApiModelProperty(value ="作业提交时间")
    private String subWorkTime;

    @ApiModelProperty(value ="课程名称")
    private String courseName;

    @ApiModelProperty(value ="班级号")
    private String classesNo;

    @ApiModelProperty(value ="班级名称")
    private String classesName;

    @ApiModelProperty(value ="班主任手机号")
    private String classesTeacherPhone;
    @ApiModelProperty(value ="班主任姓名")
    private String classesTeacherName;
    @ApiModelProperty(value ="小组")
    private String teamName;

    @ApiModelProperty(value ="推荐状态0未推荐1已推荐")
    private Integer recommendStatus;

    @ApiModelProperty(value ="显示状态0隐藏1显示")
    private Integer showStatus;

    @ApiModelProperty(value ="所属企业名称")
    private String companyName;

    @ApiModelProperty(value ="课件名称")
    private String resourceTitle;

    @ApiModelProperty(value = "老师回复状态0未回复 1已回复")
    private Integer teacherReplyStatus;

    @ApiModelProperty(value = "是否推送给老师0未推送1已推送")
    private Integer recommendTeacherStatus;

    @ApiModelProperty(value = "老师回复时间")
    private LocalDateTime teacherReplyTime;

    @ApiModelProperty(value = "推荐来源1管理后台2企业管理员3班主任")
    private Integer recommendSource;

    @ApiModelProperty(value = "作业推荐时间")
    private LocalDateTime recommendTime;

    @ApiModelProperty("所属部门")
    private String dept;
}
