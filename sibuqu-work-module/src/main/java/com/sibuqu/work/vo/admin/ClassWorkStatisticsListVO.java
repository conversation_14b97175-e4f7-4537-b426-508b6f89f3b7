package com.sibuqu.work.vo.admin;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

@Data
public class ClassWorkStatisticsListVO {
    @ApiModelProperty(value="所属日期")
    private LocalDate belongDate;
    @ApiModelProperty("班级id")
    private Integer classesId;
    @ApiModelProperty(value="班级号")
    private String classesNo;
    @ApiModelProperty(value="班级名称")
    private String classesName;
    @ApiModelProperty(value="听课人数")
    private Integer listenNum;
    @ApiModelProperty(value="作业人数")
    private Integer workNum;
    @ApiModelProperty(value="课程名称")
    private String courseName;
    @ApiModelProperty(value="班级人数")
    private Integer memberTotal;

    @ApiModelProperty(value="课件名称")
    private String courseWareName;

    @ApiModelProperty(value="未听课人数")
    private Integer unListenNum;

    @ApiModelProperty(value="未交作业人数")
    private Integer unWorkNum;

    @ApiModelProperty(value="课程表ID")
    private Integer courseTimetableId;



}
