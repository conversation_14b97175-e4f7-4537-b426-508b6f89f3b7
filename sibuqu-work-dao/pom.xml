<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>sibuqu-app-work</artifactId>
        <groupId>com.sibuqu</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>sibuqu-work-dao</artifactId>

    <dependencies>

        <dependency>
            <groupId>com.sibuqu</groupId>
            <artifactId>sibuqu-work-module</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.sibuqu</groupId>
            <artifactId>sibuqu-work-common</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.5.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <distributionManagement>
        <repository>
            <id>2086546-release-Rjmoes</id>
            <name>生产库-release</name>
            <url>https://packages.aliyun.com/maven/repository/2086546-release-Rjmoes</url>
        </repository>
        <snapshotRepository>
            <id>2086546-snapshot-QsmlwU</id>
            <name>非生产库-snapshot</name>
            <url>https://packages.aliyun.com/maven/repository/2086546-snapshot-QsmlwU</url>
        </snapshotRepository>
    </distributionManagement>
    <repositories>
        <repository>
            <id>2086546-snapshot-QsmlwU</id>
            <url>https://packages.aliyun.com/maven/repository/2086546-snapshot-QsmlwU</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>

</project>