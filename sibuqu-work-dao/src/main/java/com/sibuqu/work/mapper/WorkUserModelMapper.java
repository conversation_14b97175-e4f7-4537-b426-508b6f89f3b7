package com.sibuqu.work.mapper;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sibuqu.base.common.enums.IsDeleteEnum;
import com.sibuqu.work.entity.WorkUserModel;

import java.util.ArrayList;
import java.util.List;

public interface WorkUserModelMapper extends BaseMapper<WorkUserModel> {

    default WorkUserModel getByUserIdAndCourseId(Integer userId, Integer courseId) {
        return selectOne(new LambdaQueryWrapper<WorkUserModel>()
                .eq(WorkUserModel::getUserId, userId)
                .eq(WorkUserModel::getCourseId, courseId)
                .eq(WorkUserModel::getDeleteFlag, IsDeleteEnum.NO.getCode())
                .last("limit 1")
        );
    }

    default List<WorkUserModel> getByUserIdListAndCourseId(List<Integer> userIdList, Integer courseId) {
        if (CollUtil.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        return selectList(new LambdaQueryWrapper<WorkUserModel>()
                .in(WorkUserModel::getUserId, userIdList)
                .eq(WorkUserModel::getCourseId, courseId)
                .eq(WorkUserModel::getDeleteFlag, IsDeleteEnum.NO.getCode())
        );
    }

}