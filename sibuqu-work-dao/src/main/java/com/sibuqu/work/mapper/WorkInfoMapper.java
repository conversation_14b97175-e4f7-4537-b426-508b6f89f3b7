package com.sibuqu.work.mapper;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.work.dto.*;
import com.sibuqu.work.entity.WorkInfo;
import com.sibuqu.work.entity.WorkInfoDetail;
import com.sibuqu.work.vo.admin.*;
import com.sibuqu.work.vo.api.*;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

public interface WorkInfoMapper extends BaseMapper<WorkInfo> {

    default int saveOrUpdate(WorkInfo workInfo) {
        if (workInfo.getId() == null) {
            return insert(workInfo);
        } else {
            return updateById(workInfo);
        }
    }

    ClassWorkStatisticsVO classWorkStatistics(@Param("classWorkStatisticsDTO") ClassWorkStatisticsDTO classWorkStatisticsDTO);

    IPage<ClassWorkStatisticsListVO> classWorkStatisticsList(@Param("page") Page<ClassWorkStatisticsListVO> page, @Param("classWorkStatisticsDTO") ClassWorkStatisticsDTO classWorkStatisticsDTO);


    ClassWorkStatisticsVO userWorkStatistics(@Param("userWorkStatisticsDTO") UserWorkStatisticsDTO userWorkStatisticsDTO, @Param("courseNum") Integer courseNum);

    List<WorkingByDateVO> classWorkingByDate(@Param("belongDate") LocalDate belongDate, @Param("courseId") Integer courseId, @Param("userIds") List<Integer> userIds, @Param("companyId") Integer companyId, @Param("courseTimetableId") Integer courseTimetableId);

    List<TeamStatisticsVO> teamStatistics(@Param("teamStatisticsDTO") TeamStatisticsDTO teamStatisticsDTO, @Param("courseTimetableId")  Integer courseTimetableId, @Param("companyId") Integer companyId);

    List<TeamStatisticsVO> teamStatisticsV2(@Param("teamStatisticsDTO") HistoryWorkingDTO teamStatisticsDTO, @Param("courseTimetableId")  Integer courseTimetableId, @Param("companyId") Integer companyId);

    List<UserStudyStatisticsDetailVO> userStudyStatistics(@Param("userId") Integer userId, @Param("courseId") Integer courseId, @Param("companyId") Integer companyId);

    List<WorkInfoDetail> selectWorkDetail(@Param("workIds") List<Integer> workIds);

    List<MyWorkDetailVO> workInfoDetail(@Param("id") Integer id);

    List<TeamStatisticsVO> classesStatistics(@Param("teamStatisticsDTO") TeamStatisticsDTO teamStatisticsDTO, @Param("courseTimetableId") Integer courseTimetableId, @Param("companyId") Integer companyId);

    List<TeamStatisticsVO> classesStatisticsV2(@Param("teamStatisticsDTO") HistoryWorkingDTO teamStatisticsDTO, @Param("courseTimetableId") Integer courseTimetableId, @Param("companyId") Integer companyId);

    IPage<UserWorkStatisticsListVO> userWorkStatisticsList(@Param("page") Page<UserWorkStatisticsListVO> page, @Param("userWorkStatisticsDTO") UserWorkStatisticsDTO userWorkStatisticsDTO);

    IPage<WorkInfo> selectTeacherReplyWorkPage(@Param("page") Page<WorkInfo> page, @Param("groupId") Integer groupId, @Param("courseId") Integer courseId);

    IPage<ClassWorkStatisticsListVO> selectClassWorkList(@Param("page") Page<Object> page, @Param("classWorkStatisticsDTO") ClassWorkStatisticsDTO classWorkStatisticsDTO);

    ClassWorkInfoVO selectClassInfo(@Param("classWorkInfoDTO") ClassWorkInfoDTO classWorkInfoDTO);

    IPage<ClassWorkStatisticsInfoVO> selectClassWorkInfoList(@Param("page") Page<Object> page, @Param("classWorkStatisticsInfoDTO") ClassWorkStatisticsInfoDTO classWorkStatisticsInfoDTO);

    UserStudyVO getUserStudy(@Param("userStudyDTO") UserStudyDTO userStudyDTO);

    IPage<WorkInfo> heartPerceptionList(@Param("page") Page<WorkInfo> page, @Param("courseId") Integer courseId, @Param("companyId") Integer companyId, @Param("userId") Integer userId, @Param("groupId") Integer groupId, @Param("workId") Integer workId, @Param("time") LocalDateTime time);

    IPage<WorkInfo> companyWorkList(@Param("page") Page<WorkInfo> page, @Param("courseId") Integer courseId, @Param("companyId") Integer companyId, @Param("userId") Integer userId, @Param("groupId") Integer groupId, @Param("teamId") Integer teamId);

    Integer checkOldUser(@Param("courseId") Integer courseId, @Param("userId") Integer userId);

    List<Integer> checkOldUserByUserIds(@Param("courseId") Integer courseId, @Param("userIds") List<Integer> userIds);

    List<Integer> checkOldStudentCount(@Param("dto") CheckOldStudentCountDTO dto);

    /**
     * 查询具体的一条作业
     */
    default WorkInfo queryOneWork(Integer userId, Integer courseId, Integer companyId, Integer courseTimeTableId) {
        return selectOne(new LambdaQueryWrapper<WorkInfo>()
                .eq(WorkInfo::getUserId, userId)
                .eq(WorkInfo::getCourseId, courseId)
                .eq(WorkInfo::getCompanyId, companyId)
                .eq(WorkInfo::getCourseTimetableId, courseTimeTableId)
                .last("limit 1")
        );
    }

    default WorkInfo getLastWork(Integer userId, Integer courseId, Integer companyId) {
        return selectOne(new LambdaQueryWrapper<WorkInfo>()
                .eq(WorkInfo::getUserId, userId)
                .eq(WorkInfo::getCourseId, courseId)
                .eq(WorkInfo::getCompanyId, companyId)
                .orderByDesc(WorkInfo::getCourseTimetableId)
                .last("limit 1"));
    }
}
