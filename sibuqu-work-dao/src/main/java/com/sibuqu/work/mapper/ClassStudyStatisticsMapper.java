package com.sibuqu.work.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sibuqu.work.entity.ClassStudyStatistics;
import com.sibuqu.work.vo.api.ClassStatisticsVO;
import com.sibuqu.work.vo.api.ClassSubRateVO;
import com.sibuqu.work.vo.api.TodayStudyUserTopVO;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

public interface ClassStudyStatisticsMapper extends BaseMapper<ClassStudyStatistics> {
    ClassStatisticsVO classStatistics(@Param("classesId") Integer classesId, @Param("classesNo") Integer classesNo, @Param("courseId") Integer courseId, @Param("userId") Integer userId, @Param("courseTimetableId") Integer courseTimetableId, @Param("companyId") Integer companyId);

    ClassStatisticsVO classStatisticsV2(@Param("classesId") Integer classesId, @Param("classesNo") String classesNo, @Param("courseId") Integer courseId, @Param("userId") Integer userId, @Param("courseTimetableId") Integer courseTimetableId, @Param("companyId") Integer companyId);

    ClassStatisticsVO classStatisticsByTime(@Param("courseId") Integer courseId, @Param("companyId") Integer companyId, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    Integer getCurrentRank(@Param("classStudyStatistics") ClassStudyStatistics classStudyStatistics);

    List<ClassSubRateVO> classStudyTop(@Param("courseTimetableIdList") List<Integer> courseTimetableIdList, @Param("sort") Integer sort, @Param("courseId") Integer courseId, @Param("companyId") Integer companyId);

    List<TodayStudyUserTopVO> userStudyTop(@Param("courseId") Integer courseId, @Param("companyId") Integer companyId, @Param("workBeginTime") LocalDateTime workBeginTime);

    ClassStatisticsVO classStatisticsHistory(@Param("classesId") Integer classesId, @Param("classesNo") Integer classesNo, @Param("courseId") Integer courseId, @Param("userId") Integer userId, @Param("courseTimetableId") Integer courseTimetableId, @Param("companyId") Integer companyId);

    ClassStatisticsVO classStatisticsHistoryV2(@Param("classesId") Integer classesId, @Param("classesNo") String classesNo, @Param("courseId") Integer courseId, @Param("userId") Integer userId, @Param("courseTimetableId") Integer courseTimetableId, @Param("companyId") Integer companyId);

}