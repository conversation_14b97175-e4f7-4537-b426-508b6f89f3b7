package com.sibuqu.work.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sibuqu.base.common.enums.IsDeleteEnum;
import com.sibuqu.course.dto.courseinfo.CourseInfoSearchDTO;
import com.sibuqu.course.vo.courseinfo.CourseListVO;
import com.sibuqu.work.dto.WorkReplyPageDTO;
import com.sibuqu.work.entity.WorkTeacherReply;
import com.sibuqu.work.enums.RecommendStatusEnums;
import com.sibuqu.work.vo.admin.WorkTeacherReplyVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <p>
 * 老师回复表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022年5月18日
 */
@Mapper
public interface WorkTeacherReplyMapper extends BaseMapper<WorkTeacherReply> {

    default List<WorkTeacherReply> queryByCourseId(Integer courseId) {
        return selectList(new LambdaQueryWrapper<WorkTeacherReply>()
                .eq(WorkTeacherReply::getCourseId, courseId)
                .eq(courseId.equals(797), WorkTeacherReply::getRecommendStatus, RecommendStatusEnums.YES.getCode())
                .eq(WorkTeacherReply::getDeleteFlag, IsDeleteEnum.NO.getCode()));
    }

    default boolean workHasReply(Integer workId, Integer replyUserType) {
        return selectOne(new LambdaQueryWrapper<WorkTeacherReply>()
                .eq(WorkTeacherReply::getWorkId, workId)
                .eq(WorkTeacherReply::getReplyUserType, replyUserType)
                .last("limit 1")
        ) != null;
    }

    IPage<WorkTeacherReplyVO> workReplyPage(Page<WorkTeacherReplyVO> page, @Param("searchDTO") WorkReplyPageDTO searchDTO);

}
