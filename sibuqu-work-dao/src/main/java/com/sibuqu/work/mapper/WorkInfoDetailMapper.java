package com.sibuqu.work.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sibuqu.work.entity.WorkInfoDetail;
import com.sibuqu.work.vo.api.MyWorkDetailVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WorkInfoDetailMapper extends BaseMapper<WorkInfoDetail> {
    List<MyWorkDetailVO> myWorkDetail(@Param("courseId") Integer courseId, @Param("userId") Integer userId);

}