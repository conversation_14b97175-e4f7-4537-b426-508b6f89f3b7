package com.sibuqu.work.mapper;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sibuqu.base.common.result.HeaderUserInfo;
import com.sibuqu.work.dto.WorkingByDateDTO;
import com.sibuqu.work.entity.UserStudyInfo;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

public interface UserStudyInfoMapper extends BaseMapper<UserStudyInfo> {

    default List<UserStudyInfo> workDate(WorkingByDateDTO dto, HeaderUserInfo headerUserInfo, List<Integer> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return new ArrayList<>();
        }
        return selectList(new LambdaQueryWrapper<UserStudyInfo>()
                .eq(UserStudyInfo::getCompanyId, headerUserInfo.getCurrentCompanyId())
                .eq(UserStudyInfo::getCourseId, dto.getCourseId())
                .in(UserStudyInfo::getUserId, userIds));

    }
}