package com.sibuqu.work.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sibuqu.work.entity.WorkInnoInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WorkInnoInfoMapper extends BaseMapper<WorkInnoInfo> {

    /**
     * 根据课程ID和课件ID查询作业数量
     * 
     * @param courseId          课程ID
     * @param courseTimetableId 课件ID
     * @return 作业数量
     */
    Integer countByCourseIdAndCttId(@Param("courseId") Integer courseId,
            @Param("courseTimetableId") Integer courseTimetableId,
            @Param("userIdList") List<Integer> userIdList);

}
