<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sibuqu.work.mapper.WorkTeacherReplyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sibuqu.work.entity.WorkTeacherReply">
        <id column="id" property="id" />
        <result column="course_id" property="courseId" />
        <result column="work_id" property="workId" />
        <result column="teacher_user_id" property="teacherUserId" />
        <result column="teacher_content" property="teacherContent" />
        <result column="teacher_reply_type" property="teacherReplyType" />
        <result column="source_type" property="sourceType" />
        <result column="teacher_reply_time" property="teacherReplyTime" />
        <result column="audio_time_length" property="audioTimeLength" />
        <result column="audio_size" property="audioSize" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, course_id, work_id, teacher_user_id, teacher_content, teacher_reply_type, source_type, teacher_reply_time, audio_time_length, audio_size, delete_flag
    </sql>

    <select id="workReplyPage" resultType="com.sibuqu.work.vo.admin.WorkTeacherReplyVO">
        SELECT
        t1.id,
        t2.user_name,
        t2.course_begin_time,
        t2.resource_title AS courseWareName,
        t2.user_phone,
        t2.content,
        t2.recommend_status,
        t1.user_name AS teacherReplyName,
        t1.teacher_content AS teacherReply,
        t1.teacher_reply_time,
        t1.delete_flag AS showStatus,
        t1.user_name AS teacherName,
        t1.teacher_user_id,
        t2.id AS postId
        FROM
        work_teacher_reply t1
        LEFT JOIN work_info t2 ON t1.work_id = t2.id
        WHERE
        t1.course_id = #{searchDTO.courseId}
        AND t2.course_id = #{searchDTO.courseId} and t2.show_status=1 and t2.work_status=1
        <if test="searchDTO.recommendStatus != null">
            AND t2.recommend_status = #{searchDTO.recommendStatus}
        </if>
        <if test="searchDTO.courseWareName != null and searchDTO.courseWareName != ''">
            AND t2.resource_title = #{searchDTO.courseWareName}
        </if>
        <if test="searchDTO.userName != null and searchDTO.userName != ''">
            AND t2.user_name = #{searchDTO.userName}
        </if>
        <if test="searchDTO.userPhone != null and searchDTO.userPhone != ''">
            AND t2.user_phone = #{searchDTO.userPhone}
        </if>
        <if test="searchDTO.teacherReplyName != null and searchDTO.teacherReplyName != ''">
            AND t1.user_name = #{searchDTO.teacherReplyName}
        </if>
        <if test="searchDTO.startDate != null and searchDTO.startDate != '' and searchDTO.endDate != null and searchDTO.endDate != ''">
            and date(t2.course_begin_time) &gt;= #{searchDTO.startDate} and date(t2.course_begin_time) &lt;=
            #{searchDTO.endDate}
        </if>
        <if test="searchDTO.startReplyDate != null and searchDTO.startReplyDate != '' and searchDTO.endReplyDate != null and searchDTO.endReplyDate != ''">
            and date(t1.teacher_reply_time) &gt;= #{searchDTO.startReplyDate} and date(t1.teacher_reply_time) &lt;=
            #{searchDTO.endReplyDate}
        </if>
        <if test="searchDTO.showStatus != null">
            AND t1.delete_flag = #{searchDTO.showStatus}
        </if>
        <if test="searchDTO.aiReplyStatus != null and searchDTO.aiReplyStatus == 0">
            AND t1.teacher_user_id != 9999999
        </if>
        <if test="searchDTO.aiReplyStatus != null and searchDTO.aiReplyStatus == 1">
            AND t1.teacher_user_id = 9999999
        </if>
        Order By t1.teacher_reply_time desc
    </select>

</mapper>
