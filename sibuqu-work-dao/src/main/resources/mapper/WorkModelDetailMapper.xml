<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sibuqu.work.mapper.WorkModelDetailMapper">
  <resultMap id="BaseResultMap" type="com.sibuqu.work.entity.WorkModelDetail">
    <!--@Table work_model_detail-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="work_model_id" jdbcType="INTEGER" property="workModelId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="prompt" jdbcType="VARCHAR" property="prompt" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="max_length" jdbcType="INTEGER" property="maxLength" />
    <result column="min_length" jdbcType="INTEGER" property="minLength" />
    <result column="manual_status" jdbcType="INTEGER" property="manualStatus" />
    <result column="show_status" jdbcType="INTEGER" property="showStatus" />
    <result column="show_title" jdbcType="VARCHAR" property="showTitle" />
    <result column="score" jdbcType="INTEGER" property="score" />
    <result column="sorted" jdbcType="INTEGER" property="sorted" />
    <result column="default_value" jdbcType="INTEGER" property="defaultValue" />
    <result column="create_user_id" jdbcType="INTEGER" property="createUserId" />
    <result column="update_user_id" jdbcType="INTEGER" property="updateUserId" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
    <result column="words" jdbcType="VARCHAR" property="words" />
  </resultMap>
  <sql id="Base_Column_List">
    id, work_model_id, title, `type`, prompt, description, max_length, min_length, manual_status, 
    show_status, show_title, score, sorted, default_value, create_user_id, update_user_id, 
    deleted, words
  </sql>
</mapper>