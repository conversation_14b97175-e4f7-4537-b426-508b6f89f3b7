<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sibuqu.work.mapper.WorkInfoDetailMapper">
    <select id="myWorkDetail" resultType="com.sibuqu.work.vo.api.MyWorkDetailVO">
        SELECT
            w.id as workId,
            d.title,
            d.type,
            d.description,
            ifnull(d.score,0) as score,
            CASE
                WHEN d.type = 1  THEN
                    if(d.content is null,0,d.content)
                WHEN d.type = 2  THEN
                    if(d.content is null,0,d.content)
                WHEN d.type = 3 or  d.type = 4 or d.type = 5 or d.type = 6 or d.type = 8 or d.type = 9 or d.type = 7 THEN
                    if(d.content is null,"",d.content)
                END as content
        FROM
            work_info w
                left join work_info_detail d on d.work_id = w.id
        where w.course_id = #{courseId}
        and w.user_id = #{userId}
        order by d.sorted asc
    </select>
</mapper>