<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sibuqu.work.mapper.WorkInnoInfoMapper">

    <select id="countByCourseIdAndCttId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM work_inno_info
        WHERE course_id = #{courseId}
          AND course_timetable_id = #{courseTimetableId}
          AND user_points > 0
          AND delete_flag = 0
          AND user_id IN
          <foreach collection="userIdList" item="userId" open="(" separator="," close=")">
              #{userId}
          </foreach>
    </select>

</mapper>
