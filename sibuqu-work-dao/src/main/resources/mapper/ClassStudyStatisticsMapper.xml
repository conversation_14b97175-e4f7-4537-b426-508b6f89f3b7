<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sibuqu.work.mapper.ClassStudyStatisticsMapper">
    <select id="classStatistics" resultType="com.sibuqu.work.vo.api.ClassStatisticsVO">
        SELECT
        COALESCE(FORMAT(sum(listen_total) / sum(class_member_total) * 100,2),0.00) as listenRate,
        COALESCE(FORMAT(sum(sub_total) / sum(class_member_total) * 100,2),0.00) as workRate,
        COALESCE(sum(listen_total),0) as listen_total,
        COALESCE(sum(sub_total),0) as sub_total,
        COALESCE(sum(class_member_total),0) as class_member_total
        <if test="classesId != null">
            ,unsub_user_name as unsubUserNames
        </if>
        FROM
        class_study_statistics
        WHERE
        course_id = #{courseId}
        and company_id = #{companyId}
        AND course_timetable_id = #{courseTimetableId}
        <if test="classesId != null">
            and classes_id = #{classesId}
        </if>
        <if test="classesNo != null">
            and classes_no = #{classesNo}
        </if>
    </select>

    <select id="classStatisticsV2" resultType="com.sibuqu.work.vo.api.ClassStatisticsVO">
        SELECT
        COALESCE(FORMAT(sum(listen_total) / sum(class_member_total) * 100,2),0.00) as listenRate,
        COALESCE(FORMAT(sum(sub_total) / sum(class_member_total) * 100,2),0.00) as workRate,
        COALESCE(sum(listen_total),0) as listen_total,
        COALESCE(sum(sub_total),0) as sub_total,
        COALESCE(sum(class_member_total),0) as class_member_total
        <if test="classesId != null">
            ,unsub_user_name as unsubUserNames
        </if>
        FROM
        class_study_statistics
        WHERE
        course_id = #{courseId}
        and company_id = #{companyId}
        AND course_timetable_id = #{courseTimetableId}
        <if test="classesId != null">
            and classes_id = #{classesId}
        </if>
        <if test="classesNo != null">
            and classes_no = #{classesNo}
        </if>
    </select>

    <select id="getCurrentRank" resultType="java.lang.Integer">
        SELECT
          count(1)
        from
             class_study_statistics
        WHERE course_id = #{classStudyStatistics.courseId}
          and company_id = #{classStudyStatistics.companyId}
          AND course_timetable_id = #{classStudyStatistics.courseTimetableId}
          and classes_id != 0
          and CONVERT(current_sub_rate,DECIMAL(6,2)) &gt;= CONVERT(  #{classStudyStatistics.currentSubRate} , DECIMAL(6,2))
          and  CONVERT(current_listen_rate,DECIMAL(6,2)) &gt;= CONVERT(#{classStudyStatistics.currentListenRate}, DECIMAL(6,2))
          and update_time &lt;= #{classStudyStatistics.updateTime}

    </select>
    <select id="classStudyTop" resultType="com.sibuqu.work.vo.api.ClassSubRateVO">
        select classes_teacher_phone as classTeacherPhone,
        classes_no classGroupId,
        current_sub_rate as subRate,
        current_listen_rate as listenRate,
        course_timetable_id
        from class_study_statistics
        where course_id = #{courseId}
        and course_timetable_id in
        <foreach collection="courseTimetableIdList" item="courseTimetableId" open="(" close=")" separator=",">
            #{courseTimetableId}
        </foreach>
        and company_id = #{companyId}
        and classes_id != 0
        <if test="sort == 1">
            ORDER BY CONVERT(current_sub_rate,DECIMAL(6,2)) DESC, CONVERT(current_listen_rate,DECIMAL(6,2))
            DESC,update_time asc ,class_member_total desc ,classes_id asc
        </if>
        <if test="sort == 2">
            ORDER BY CONVERT(current_sub_rate,DECIMAL(6,2)) ASC,CONVERT(current_listen_rate,DECIMAL(6,2))
            ASC,update_time Desc ,class_member_total ASC ,classes_id asc
        </if>
        limit 10
    </select>
    <select id="userStudyTop" resultType="com.sibuqu.work.vo.api.TodayStudyUserTopVO">
        select u.user_id,
               u.score as score
        from user_study_info u
        where company_id = #{companyId}
          and course_id = #{courseId}
        ORDER BY u.score desc, update_time asc, user_id asc limit 10
    </select>
    <select id="classStatisticsHistory" resultType="com.sibuqu.work.vo.api.ClassStatisticsVO">
        SELECT
            COALESCE(FORMAT(sum(sub_total) / sum(class_member_total) * 100 ,2),0.00) historyWorkRate,
            COALESCE(FORMAT(sum(listen_total) / sum(class_member_total) * 100 ,2),0.00) historyListenRate
        FROM
            class_study_statistics
        WHERE
            course_id = #{courseId}
            and company_id = #{companyId}
            AND course_timetable_id != #{courseTimetableId}
<!--        <if test="classesId != null">-->
<!--            AND classes_id = #{classesId}-->
<!--        </if>-->
        <if test="classesNo != null">
            and classes_no = #{classesNo}
        </if>
    </select>

    <select id="classStatisticsHistoryV2" resultType="com.sibuqu.work.vo.api.ClassStatisticsVO">
        SELECT
        COALESCE(FORMAT(sum(sub_total) / sum(class_member_total) * 100 ,2),0.00) historyWorkRate,
        COALESCE(FORMAT(sum(listen_total) / sum(class_member_total) * 100 ,2),0.00) historyListenRate
        FROM
        class_study_statistics
        WHERE
        course_id = #{courseId}
        and company_id = #{companyId}
        AND course_timetable_id != #{courseTimetableId}
        <if test="classesNo != null">
            and classes_no = #{classesNo}
        </if>
    </select>

    <select id="classStatisticsByTime" resultType="com.sibuqu.work.vo.api.ClassStatisticsVO">
        SELECT
        COALESCE(FORMAT(sum(listen_total) / sum(class_member_total) * 100,2),0.00) as listenRate,
        COALESCE(FORMAT(sum(sub_total) / sum(class_member_total) * 100,2),0.00) as workRate,
        COALESCE(sum(listen_total),0) as listen_total,
        COALESCE(sum(sub_total),0) as sub_total,
        COALESCE(sum(class_member_total),0) as class_member_total
        FROM
        class_study_statistics
        WHERE
        course_id = #{courseId}
        and company_id = #{companyId}
        and course_begin_time &gt;= #{startTime}
        and course_begin_time &lt;= #{endTime}
    </select>
</mapper>