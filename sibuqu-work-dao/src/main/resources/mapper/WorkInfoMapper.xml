<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sibuqu.work.mapper.WorkInfoMapper">
    <select id="classWorkStatistics" resultType="com.sibuqu.work.vo.admin.ClassWorkStatisticsVO">
        SELECT
        ifnull( sum( listen_total ), 0 ) AS listenNum,
        ifnull( sum( class_member_total ), 0 ) AS listenTotal,
        COALESCE ( FORMAT( sum( listen_total )/ sum( class_member_total )* 100, 2 ), 0.00 ) AS listenRate,
        ifnull( sum( sub_total ), 0 ) AS workNum,
        ifnull( sum( class_member_total ), 0 ) AS workTotal,
        COALESCE ( FORMAT( sum( sub_total )/ sum( class_member_total )* 100, 2 ), 0.00 ) AS workRate
        FROM
        class_study_statistics
        WHERE
        1=1
        <if test="classWorkStatisticsDTO.classesNo != null and classWorkStatisticsDTO.classesNo != ''">
            and classes_no = #{classWorkStatisticsDTO.classesNo}
        </if>
        <if test="classWorkStatisticsDTO.classesTeacherPhone != null and classWorkStatisticsDTO.classesTeacherPhone != ''">
            and classes_teacher_phone = #{classWorkStatisticsDTO.classesTeacherPhone}
        </if>
        <if test="classWorkStatisticsDTO.companyId != null and classWorkStatisticsDTO.companyId != ''">
            and company_id = #{classWorkStatisticsDTO.companyId}
        </if>
        <if test="classWorkStatisticsDTO.companyCode != null and classWorkStatisticsDTO.companyCode != ''">
            and company_no = #{classWorkStatisticsDTO.companyCode}
        </if>
        <if test="classWorkStatisticsDTO.courseId != null and classWorkStatisticsDTO.courseId != ''">
            and course_id = #{classWorkStatisticsDTO.courseId}
        </if>
        <if test="classWorkStatisticsDTO.companyName != null and classWorkStatisticsDTO.companyName != ''">
            and company_name =concat("%",#{classWorkStatisticsDTO.companyName},"%")
        </if>
        <if test="classWorkStatisticsDTO.startDate != null and classWorkStatisticsDTO.endDate != null">
            and date(course_begin_time) &gt;= #{classWorkStatisticsDTO.startDate} and date(course_begin_time) &lt;=
            #{classWorkStatisticsDTO.endDate}
        </if>

    </select>
    <select id="classWorkStatisticsList" resultType="com.sibuqu.work.vo.admin.ClassWorkStatisticsListVO">
        select date(course_begin_time) as belongDate,
        classes_id,
        classes_no,
        listen_total as listenNum,
        sub_total as workNum,
        classes_name,
        class_member_total as memberTotal

        from class_study_statistics
        where
        1=1
        <if test="classWorkStatisticsDTO.courseId != null and classWorkStatisticsDTO.courseId != ''">
            and course_id = #{classWorkStatisticsDTO.courseId}
        </if>
        <if test="classWorkStatisticsDTO.companyId != null and classWorkStatisticsDTO.companyId != ''">
            and company_id = #{classWorkStatisticsDTO.companyId}
        </if>
        <if test="classWorkStatisticsDTO.companyCode != null and classWorkStatisticsDTO.companyCode != ''">
            and company_code = #{classWorkStatisticsDTO.companyCode}
        </if>
        <if test="classWorkStatisticsDTO.companyName != null and classWorkStatisticsDTO.companyName != ''">
            and company_name like concat("%",#{classWorkStatisticsDTO.companyCode},"%")
        </if>
        <if test="classWorkStatisticsDTO.classesNo != null and classWorkStatisticsDTO.classesNo != ''">
            and classes_no = #{classWorkStatisticsDTO.classesNo}
        </if>
        <if test="classWorkStatisticsDTO.startDate != null and classWorkStatisticsDTO.endDate != null ">
            and date(course_begin_time) &gt;= #{classWorkStatisticsDTO.startDate} and date(course_begin_time) &lt;=
            #{classWorkStatisticsDTO.endDate}
        </if>
        <if test="classWorkStatisticsDTO.classesTeacherPhone != null and classWorkStatisticsDTO.classesTeacherPhone != ''">
            and classes_teacher_phone = #{classWorkStatisticsDTO.classesTeacherPhone}
        </if>
        GROUP BY
        DATE( course_begin_time ),classes_id
    </select>
    <select id="userWorkStatisticsList" resultType="com.sibuqu.work.vo.admin.UserWorkStatisticsListVO">
        SELECT
        date( course_begin_time ) belongDate,
        w.user_phone,
        w.user_name,
        ifnull(listen_status,0) listenStatus,
        ifnull(work_status,0) work_status,
        course_name,
        classes_no,
        classes_name,
        team_id
        FROM
        work_info w
        where 1=1
        <if test="userWorkStatisticsDTO.companyId != null and userWorkStatisticsDTO.companyId != ''">
            and w.company_id = #{userWorkStatisticsDTO.companyId}
        </if>
        <if test="userWorkStatisticsDTO.userPhone != null and userWorkStatisticsDTO.userPhone != ''">
            and w.user_phone = #{userWorkStatisticsDTO.userPhone}
        </if>
        <if test="userWorkStatisticsDTO.teamName != null and userWorkStatisticsDTO.teamName != ''">
            and w.team_name like concat_ws('%',#{userWorkStatisticsDTO.teamName},'%')
        </if>
        <if test="userWorkStatisticsDTO.classesNo != null and userWorkStatisticsDTO.classesNo != ''">
            and w.classes_no = #{userWorkStatisticsDTO.classesNo}
        </if>
        <if test="userWorkStatisticsDTO.classesTeacherPhone != null and userWorkStatisticsDTO.classesTeacherPhone != ''">
            and w.classes_teacher_phone = #{userWorkStatisticsDTO.classesTeacherPhone}
        </if>
        <if test="userWorkStatisticsDTO.companyCode != null and userWorkStatisticsDTO.companyCode != ''">
            and w.company_code = #{userWorkStatisticsDTO.companyCode}
        </if>
        <if test="userWorkStatisticsDTO.courseId != null and userWorkStatisticsDTO.courseId != ''">
            and w.course_id = #{userWorkStatisticsDTO.courseId}
        </if>
        <if test="userWorkStatisticsDTO.companyName != null and userWorkStatisticsDTO.companyName != ''">
            and w.company_name =concat("%",#{userWorkStatisticsDTO.companyName},"%")
        </if>
        <if test="userWorkStatisticsDTO.startDate != null and userWorkStatisticsDTO.endDate != null">
            and date(w.course_begin_time) &gt;= #{userWorkStatisticsDTO.startDate} and date(w.course_begin_time) &lt;=
            #{userWorkStatisticsDTO.endDate}
        </if>
    </select>
    <select id="userWorkStatistics" resultType="com.sibuqu.work.vo.admin.ClassWorkStatisticsVO">
        select #{courseNum} as listenTotal,
        l.listenNum,
        COALESCE(FORMAT((l.listenNum / #{courseNum}) * 100,2),0.00) as listenRate,
        '0' as workTotal,
        ww.workNum,
        COALESCE(FORMAT((ww.workNum / #{courseNum}) * 100,2),0.00) as workRate
        from
        (SELECT
        count(1) as listenNum
        FROM
        work_info w,work_info_detail d
        WHERE
        w.id = d.work_id
        and d.content = '1'
        <if test="userWorkStatisticsDTO.companyId != null and userWorkStatisticsDTO.companyId != ''">
            and w.user_phone = #{userWorkStatisticsDTO.companyId}
        </if>
        <if test="userWorkStatisticsDTO.userPhone != null and userWorkStatisticsDTO.userPhone != ''">
            and w.user_phone = #{userWorkStatisticsDTO.userPhone}
        </if>
        <if test="userWorkStatisticsDTO.teamName != null and userWorkStatisticsDTO.teamName != ''">
            and w.team_name like concat_ws('%',#{userWorkStatisticsDTO.teamName},'%')
        </if>
        <if test="userWorkStatisticsDTO.classesNo != null and userWorkStatisticsDTO.classesNo != ''">
            and w.classes_no = #{userWorkStatisticsDTO.classesNo}
        </if>
        <if test="userWorkStatisticsDTO.classesTeacherPhone != null and userWorkStatisticsDTO.classesTeacherPhone != ''">
            and w.classes_teacher_phone = #{userWorkStatisticsDTO.classesTeacherPhone}
        </if>
        <if test="userWorkStatisticsDTO.companyCode != null and userWorkStatisticsDTO.companyCode != ''">
            and w.company_no = #{userWorkStatisticsDTO.companyCode}
        </if>
        <if test="userWorkStatisticsDTO.courseId != null and userWorkStatisticsDTO.courseId != ''">
            and w.course_id = #{userWorkStatisticsDTO.courseId}
        </if>
        <if test="userWorkStatisticsDTO.companyName != null and userWorkStatisticsDTO.companyName != ''">
            and w.company_name =concat("%",#{userWorkStatisticsDTO.companyName},"%")
        </if>
        <if test="userWorkStatisticsDTO.startDate != null and userWorkStatisticsDTO.endDate != null">
            and date(w.course_begin_time) &gt;= #{userWorkStatisticsDTO.startDate} and date(w.course_begin_time) &lt;=
            #{userWorkStatisticsDTO.endDate}
        </if>

        ) l ,

        (SELECT
        count(1) as workNum
        FROM
        work_info w
        WHERE
        w.work_status = '1'
        <if test="userWorkStatisticsDTO.userPhone != null and userWorkStatisticsDTO.userPhone != ''">
            and w.user_phone = #{userWorkStatisticsDTO.userPhone}
        </if>
        <if test="userWorkStatisticsDTO.teamName != null and userWorkStatisticsDTO.teamName != ''">
            and w.team_name like concat_ws('%',#{userWorkStatisticsDTO.teamName},'%')
        </if>
        <if test="userWorkStatisticsDTO.classesNo != null and userWorkStatisticsDTO.classesNo != ''">
            and w.classes_no = #{userWorkStatisticsDTO.classesNo}
        </if>
        <if test="userWorkStatisticsDTO.classesTeacherPhone != null and userWorkStatisticsDTO.classesTeacherPhone != ''">
            and w.classes_teacher_phone = #{userWorkStatisticsDTO.classesTeacherPhone}
        </if>
        <if test="userWorkStatisticsDTO.companyCode != null and userWorkStatisticsDTO.companyCode != ''">
            and w.company_no = #{userWorkStatisticsDTO.companyCode}
        </if>
        <if test="userWorkStatisticsDTO.courseId != null and userWorkStatisticsDTO.courseId != ''">
            and w.course_id = #{userWorkStatisticsDTO.courseId}
        </if>
        <if test="userWorkStatisticsDTO.companyName != null and userWorkStatisticsDTO.companyName != ''">
            and w.company_name =concat("%",#{userWorkStatisticsDTO.companyName},"%")
        </if>
        <if test="userWorkStatisticsDTO.startDate != null and userWorkStatisticsDTO.endDate != null">
            and date(w.course_begin_time) &gt;= #{userWorkStatisticsDTO.startDate} and date(w.course_begin_time) &lt;=
            #{userWorkStatisticsDTO.endDate}
        </if>
        ) ww

    </select>
    <select id="classWorkingByDate" resultType="com.sibuqu.work.vo.api.WorkingByDateVO">
        SELECT
        ifnull(w.score,0) AS workScore,
        date( w.course_begin_time ) AS belongDate,
        ifnull(w.work_status,0) work_status,
        ifnull(w.listen_status,0) listenStatus,
        u.user_id,
        ifnull( u.score, 0 ) AS score,
        w.level
        FROM
        user_study_info u
        LEFT JOIN work_info w ON u.user_id = w.user_id
        AND w.course_id = u.course_id
        and date( w.course_begin_time ) = #{belongDate} and w.course_id = #{courseId} and w.company_id = u.company_id
        <if test="courseTimetableId != null">
            and w.course_timetable_id = #{courseTimetableId}
        </if>
        WHERE
        u.company_id = #{companyId}
        and u.course_id = #{courseId}
        and u.user_id in
        <foreach collection="userIds" item="userId" index="index" open="(" close=")" separator=",">
            #{userId}
        </foreach>

    </select>

    <select id="teamStatistics" resultType="com.sibuqu.work.vo.api.TeamStatisticsVO">
        SELECT course_id,
        classes_id,
        date (course_begin_time) as belongDate,
        sum(listen_total) listen_num,
        sum(sub_total) as workNum,
        sum(score) score,
        course_timetable_id
        FROM
        team_study_statistics
        WHERE
        course_id = #{teamStatisticsDTO.courseId}
        and course_timetable_id != #{courseTimetableId}
        <if test="teamStatisticsDTO.classesId != null">
            AND classes_id = #{teamStatisticsDTO.classesId}
        </if>
        <if test="teamStatisticsDTO.teamId != null">
            AND team_id = #{teamStatisticsDTO.teamId}
        </if>
        and company_id = #{companyId}
        group by course_timetable_id
    </select>

    <select id="teamStatisticsV2" resultType="com.sibuqu.work.vo.api.TeamStatisticsVO">
        SELECT course_id,
        classes_id,
        date(course_begin_time) as belongDate,
        sum(listen_total)          listen_num,
        sum(sub_total)          as workNum,
        sum(score)                 score,
        course_timetable_id
        FROM team_study_statistics
        WHERE course_id = #{teamStatisticsDTO.courseId}
        and course_timetable_id != #{courseTimetableId}
        <if test="teamStatisticsDTO.classesId != null">
            AND classes_id = #{teamStatisticsDTO.classesId}
        </if>
        <if test="teamStatisticsDTO.teamId != null">
            AND team_id = #{teamStatisticsDTO.teamId}
        </if>
        and company_id = #{companyId}
        group by course_timetable_id
    </select>

    <select id="userStudyStatistics" resultType="com.sibuqu.work.vo.api.UserStudyStatisticsDetailVO">
        SELECT w.score as workScore,
               date (w.course_begin_time) as belongDate,
               w.work_status,
               listen_status,
               course_timetable_id
        FROM
            work_info w
        WHERE
            w.course_id = #{courseId}
          and w.user_id = #{userId}
          and w.company_id = #{companyId}
    </select>
    <select id="selectWorkDetail" resultType="com.sibuqu.work.entity.WorkInfoDetail">
        SELECT
        d.*
        FROM
        work_info_detail d
        WHERE
        d.content is not null
        AND d.type = '3'
        and d.work_id in
        <foreach collection="workIds" open="(" close=")" index="index" separator="," item="workId">
            #{workId}
        </foreach>
    </select>
    <select id="workInfoDetail" resultType="com.sibuqu.work.vo.api.MyWorkDetailVO">
        SELECT w.id               as workId,
               d.title,
               d.type,
               d.description,
               ifnull(d.score, 0) as score,
               CASE
                   WHEN d.type = 1 THEN
                       if(d.content is null, 0, d.content)
                   WHEN d.type = 2 THEN
                       if(d.content is null, 0, d.content)
                   WHEN d.type = 3 or d.type = 4 or d.type = 5 or d.type = 6 or d.type = 8 or d.type = 9 or d.type = 7
                       THEN
                       if(d.content is null, "", d.content)
                   END            as content
        FROM work_info w
                 LEFT JOIN work_info_detail d ON w.id = d.work_id
        where w.work_status = '1'
          and w.id = #{id}
    </select>
    <select id="classesStatistics" resultType="com.sibuqu.work.vo.api.TeamStatisticsVO">
        SELECT course_id,
        classes_id,
        date (course_begin_time) belongDate,
        sum(listen_total) listen_num,
        sum(sub_total) workNum,
        sum(score) score,
        course_timetable_id as courseTimeTableId
        FROM
        class_study_statistics
        WHERE
        course_id = #{teamStatisticsDTO.courseId}
        and course_timetable_id != #{courseTimetableId}
        <if test="teamStatisticsDTO.classesId != null">
            AND classes_id = #{teamStatisticsDTO.classesId}

        </if>
        <if test="teamStatisticsDTO.classesNo != null">
            AND classes_no = #{teamStatisticsDTO.classesNo}

        </if>
        and company_id = #{companyId}
        group by course_timetable_id
    </select>

    <select id="selectTeacherReplyWorkPage" resultType="com.sibuqu.work.entity.WorkInfo">
        select w.*
        from work_teacher_reply r
                 LEFT JOIN work_info w on r.work_id = w.id
        where w.company_id = -1
          and r.course_id = #{courseId}
          and r.delete_flag = 0
          and w.content_show_status = 1
        group by w.course_begin_time, w.id
        order by w.work_time desc
    </select>

    <select id="selectClassWorkList" resultType="com.sibuqu.work.vo.admin.ClassWorkStatisticsListVO">
        select date(course_begin_time) as belongDate,
        classes_no,
        course_timetable_id,
        class_member_total as memberTotal,
        listen_total as listenNum,
        unlisten_total as unListenNum,
        sub_total as workNum,
        (class_member_total - sub_total) as unWorkNum
        from class_study_statistics
        where classes_no = #{classWorkStatisticsDTO.classesNo}
        <if test="classWorkStatisticsDTO.courseTimetableId != null and classWorkStatisticsDTO.courseTimetableId != ''">
            and course_timetable_id = #{classWorkStatisticsDTO.courseTimetableId}
        </if>
        <if test="classWorkStatisticsDTO.startDate != null and classWorkStatisticsDTO.endDate != null ">
            and date(course_begin_time) &gt;= #{classWorkStatisticsDTO.startDate} and date(course_begin_time) &lt;=
            #{classWorkStatisticsDTO.endDate}
        </if>
        GROUP BY
        DATE( course_begin_time )
    </select>

    <select id="selectClassInfo" resultType="com.sibuqu.work.vo.admin.ClassWorkInfoVO">
        SELECT
            date ( course_begin_time ) AS belongDate,
            course_id,
            classes_no,
            classes_teacher_phone AS classTeacherPhone,
            COALESCE (
                    FORMAT(((
                        SELECT
                            sum( current_sub_rate )
                        FROM
                            class_study_statistics
                        WHERE
                            classes_no = #{classWorkInfoDTO.classesNo}
                          AND CURDATE() > Date( course_begin_time ))) / (
                               SELECT
                                   count(*)
                               FROM
                                   class_study_statistics
                               WHERE
                                   classes_no = #{classWorkInfoDTO.classesNo}
                                 AND CURDATE() > Date( course_begin_time )),
                           2
                    ),
                    0.00
            ) AS totalSubRate,
            current_sub_rate AS belongDateSubRate,
            unsub_user_name
        FROM
            class_study_statistics
        WHERE
            classes_no = #{classWorkInfoDTO.classesNo}
          AND date( course_begin_time ) = #{classWorkInfoDTO.belongDate}
    </select>

    <select id="selectClassWorkInfoList" resultType="com.sibuqu.work.vo.admin.ClassWorkStatisticsInfoVO">
        SELECT w1.id,
        w1.work_model_id,
        w1.classes_no,
        w1.score as dayTotalScore,
        date(w1.course_begin_time) as belongDate,
        w1.work_time,
        w1.user_name,
        w1.user_phone,
        w1.course_id,
        w1.content,
        (SELECT SUM(w2.score) FROM work_info w2 WHERE w2.classes_no = #{classWorkStatisticsInfoDTO.classesNo} AND
        w1.user_id = w2.user_id) as totalScore
        FROM work_info w1
        WHERE DATE(w1.course_begin_time) = #{classWorkStatisticsInfoDTO.belongDate} AND w1.classes_no =
        #{classWorkStatisticsInfoDTO.classesNo}
        <if test="classWorkStatisticsInfoDTO.workModelId != null">
            and w1.work_model_id = #{ classWorkStatisticsInfoDTO.workModelId}
        </if>
        <if test="classWorkStatisticsInfoDTO.userName != null and classWorkStatisticsInfoDTO.userName != ''">
            and w1.user_name like concat('%',#{classWorkStatisticsInfoDTO.userName},'%')
        </if>
        <if test="classWorkStatisticsInfoDTO.userPhone != null and classWorkStatisticsInfoDTO.userPhone != ''">
            and w1.user_phone = #{classWorkStatisticsInfoDTO.userPhone}
        </if>
        order by w1.create_time desc
    </select>
    <select id="getUserStudy" resultType="com.sibuqu.work.vo.api.UserStudyVO">
        select sum(work_status) workNum,sum(listen_status) listenNum
        from work_info
        where user_id = #{userStudyDTO.userId}
          and course_id = #{userStudyDTO.courseId}
          and company_id = -1
          and work_time is not null
          and work_time &gt;= #{userStudyDTO.createTime}
    </select>
    <select id="heartPerceptionList" resultType="com.sibuqu.work.entity.WorkInfo">
        SELECT id,
        user_id,
        course_timetable_id,
        work_model_id,
        course_model_id,
        course_id,
        course_begin_time,
        course_name,
        company_id,
        company_code,
        company_name,
        classes_id,
        classes_no,
        classes_name,
        classes_teacher_phone,
        team_id,
        work_time create_time,
        work_time update_time,
        score,
        user_phone,
        user_name,
        work_status,
        listen_status,
        team_name,
        content,
        work_time,
        recommend_status,
        recommend_time,
        recommend_teacher_status,
        recommend_teacher_time,
        teacher_reply_status,
        content_show_status,
        automatic,
        additional,
        level
        FROM work_info
        WHERE company_id = #{companyId}
        AND course_id = #{courseId}
        AND content_show_status = 1
        <if test="groupId != null and groupId != 0">
            AND (classes_no = #{groupId} or user_id = #{userId})
        </if>
        <if test="groupId != null and groupId == 0">
            AND user_id = #{userId}
        </if>
        and update_time &lt;= #{time}
        and show_status = 1
        ORDER BY
        <if test="workId != null and workId > 0">
            id != #{workId} ,
        </if>
        work_time DESC

    </select>
    <select id="companyWorkList" resultType="com.sibuqu.work.entity.WorkInfo">
        SELECT id,
        user_id,
        course_timetable_id,
        work_model_id,
        course_model_id,
        course_id,
        course_begin_time,
        course_name,
        company_id,
        company_code,
        company_name,
        classes_id,
        classes_no,
        classes_name,
        classes_teacher_phone,
        team_id,
        work_time create_time,
        work_time update_time,
        score,
        user_phone,
        user_name,
        work_status,
        listen_status,
        team_name,
        content,
        work_time,
        recommend_status,
        recommend_time,
        recommend_teacher_status,
        recommend_teacher_time,
        teacher_reply_status,
        content_show_status,
        automatic,
        additional,user_flag
        FROM work_info
        WHERE company_id = #{companyId}
        AND course_id = #{courseId}
        AND content_show_status = 1
        <if test="groupId != null">
            AND classes_id = #{groupId}
        </if>
        <if test="teamId != null">
            AND team_id = #{teamId}
        </if>
        and show_status = 1
        ORDER BY course_begin_time desc,
        work_time DESC
    </select>
    <select id="checkOldUser" resultType="java.lang.Integer">
        select count(1)
        from work_old_user_info
        where
            course_id = #{courseId}
          and user_id = #{userId}

    </select>
    <select id="checkOldUserByUserIds" resultType="java.lang.Integer">
        select user_id
        from work_old_user_info
        where course_id = #{courseId}
        <choose>
            <when test="userIds != null and userIds.size() != 0">
                and user_id in
                <foreach collection="userIds" open="(" close=")" item="userId" separator=",">
                    #{userId}
                </foreach>
            </when>
            <otherwise>
                and false
            </otherwise>
        </choose>
    </select>

    <select id="checkOldStudentCount" resultType="java.lang.Integer">
        select user_id
        from work_old_user_info
        where course_id = #{dto.courseId}
        <choose>
            <when test="dto.userIdList != null and dto.userIdList.size() != 0">
                and user_id in
                <foreach collection="dto.userIdList" open="(" close=")" item="userId" separator=",">
                    #{userId}
                </foreach>
            </when>
            <otherwise>
                and false
            </otherwise>
        </choose>
    </select>

    <select id="classesStatisticsV2" resultType="com.sibuqu.work.vo.api.TeamStatisticsVO">
        SELECT course_id,
        classes_id,
        date(course_begin_time) belongDate,
        sum(listen_total)       listen_num,
        sum(sub_total)          workNum,
        sum(score)              score,
        course_timetable_id
        FROM class_study_statistics
        WHERE course_id = #{teamStatisticsDTO.courseId}
        and course_timetable_id != #{courseTimetableId}
        <if test="teamStatisticsDTO.classesId != null">
            AND classes_id = #{teamStatisticsDTO.classesId}
        </if>
        and company_id = #{companyId}
        group by course_timetable_id
    </select>
</mapper>
