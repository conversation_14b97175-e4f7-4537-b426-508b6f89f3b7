<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>

    <modules>
        <module>sibuqu-work-api</module>
        <module>sibuqu-work-module</module>
        <module>sibuqu-work-dao</module>
        <module>sibuqu-work-common</module>
        <module>sibuqu-work-service</module>
    </modules>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.4.RELEASE</version>
        <relativePath/>
    </parent>

    <groupId>com.sibuqu</groupId>
    <artifactId>sibuqu-app-work</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>sibuqu-app-work</name>
    <description>sibuqu-app-work</description>

    <properties>
        <hutool.version>5.7.16</hutool.version>
        <java.version>1.8</java.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
    </properties>

    <dependencies>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
            <!--            <exclusions>-->
            <!--                <exclusion>-->
            <!--                    <groupId>org.junit.vintage</groupId>-->
            <!--                    <artifactId>junit-vintage-engine</artifactId>-->
            <!--                </exclusion>-->
            <!--            </exclusions>-->
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>
    </dependencies>

    <distributionManagement>
        <repository>
            <id>2086546-release-Rjmoes</id>
            <name>生产库-release</name>
            <url>https://packages.aliyun.com/maven/repository/2086546-release-Rjmoes</url>
        </repository>
        <snapshotRepository>
            <id>2086546-snapshot-QsmlwU</id>
            <name>非生产库-snapshot</name>
            <url>https://packages.aliyun.com/maven/repository/2086546-snapshot-QsmlwU</url>
        </snapshotRepository>
    </distributionManagement>
    <repositories>
        <repository>
            <id>2086546-snapshot-QsmlwU</id>
            <url>https://packages.aliyun.com/maven/repository/2086546-snapshot-QsmlwU</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
        <repository>
            <id>aliyun</id>
            <name>aliyun</name>
            <url>http://maven.aliyun.com/nexus/content/groups/public</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

</project>
