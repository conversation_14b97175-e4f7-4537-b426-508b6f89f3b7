# 周日时间计算逻辑说明

## 功能概述

在查询周作业信息时，需要获取当前周的周日最后一秒作为截止时间，用于过滤作业开始时间。

## 时间计算逻辑

### 核心代码
```java
LocalDateTime endOfSunday = LocalDateTime.now()
        .with(DayOfWeek.SUNDAY)
        .withHour(23)
        .withMinute(59)
        .withSecond(59)
        .withNano(999999999);
```

### 计算步骤

1. **获取当前时间**: `LocalDateTime.now()`
2. **调整到周日**: `.with(DayOfWeek.SUNDAY)`
3. **设置为最后一秒**: 
   - 小时: 23 (晚上11点)
   - 分钟: 59
   - 秒: 59
   - 纳秒: 999999999 (最大纳秒值)

### 示例场景

假设当前时间是 `2024-01-15 14:30:25` (星期一)

**计算过程**:
1. 当前时间: `2024-01-15 14:30:25.123456789` (星期一)
2. 调整到周日: `2024-01-21 14:30:25.123456789` (下个星期日)
3. 设置为最后一秒: `2024-01-21 23:59:59.999999999`

**结果**: `2024-01-21 23:59:59.999999999`

### 不同星期的示例

| 当前日期 | 星期 | 计算结果的周日 |
|---------|------|---------------|
| 2024-01-15 | 星期一 | 2024-01-21 23:59:59.999999999 |
| 2024-01-16 | 星期二 | 2024-01-21 23:59:59.999999999 |
| 2024-01-17 | 星期三 | 2024-01-21 23:59:59.999999999 |
| 2024-01-18 | 星期四 | 2024-01-21 23:59:59.999999999 |
| 2024-01-19 | 星期五 | 2024-01-21 23:59:59.999999999 |
| 2024-01-20 | 星期六 | 2024-01-21 23:59:59.999999999 |
| 2024-01-21 | 星期日 | 2024-01-21 23:59:59.999999999 |

## 业务逻辑

### 查询条件
```sql
WHERE course_id = ? 
  AND delete_flag = 0 
  AND work_begin_datetime <= '2024-01-21 23:59:59.999999999'
```

### 作用
- 查询作业开始时间在当前周日结束之前的所有周作业
- 确保只返回当前周及之前的作业，不包括未来周的作业
- 提供精确到纳秒级的时间边界

## 注意事项

1. **时区**: 使用系统默认时区
2. **精度**: 精确到纳秒级别
3. **边界**: 包含当前周日的所有时间点
4. **周的定义**: 以周日为一周的结束日

## 相关方法

- `LocalDateTime.with(DayOfWeek.SUNDAY)`: 调整到指定星期
- `withHour(23)`: 设置小时为23
- `withMinute(59)`: 设置分钟为59
- `withSecond(59)`: 设置秒为59
- `withNano(999999999)`: 设置纳秒为最大值
