package com.sibuqu.work.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sibuqu.base.common.enums.YesOrNoEnum;
import com.sibuqu.base.common.exception.BusinessException;
import com.sibuqu.base.common.result.HeaderUserInfo;
import com.sibuqu.classes.vo.ClassTeacherVO;
import com.sibuqu.course.entity.CourseTimetable;
import com.sibuqu.course.vo.coursetimetable.CourseTimetableUnlockDetailVO;
import com.sibuqu.prefecture.enums.PrefectureContentAsyncTypeEnum;
import com.sibuqu.starter.mongohelper.bean.Page;
import com.sibuqu.starter.mongohelper.bean.SortBuilder;
import com.sibuqu.starter.mongohelper.utils.CriteriaAndWrapper;
import com.sibuqu.starter.mongohelper.utils.CriteriaOrWrapper;
import com.sibuqu.starter.mongohelper.utils.CriteriaWrapper;
import com.sibuqu.starter.mongohelper.utils.MongoHelper;
import com.sibuqu.work.bo.WorkInfoPojoQueryDTO;
import com.sibuqu.work.dto.AppWorkHeartPerceptionDTO;
import com.sibuqu.work.dto.AppWorkRecommendSearchDTO;
import com.sibuqu.work.dto.AppWorkReplySearchDTO;
import com.sibuqu.work.dto.PersonalWorkRankDTO;
import com.sibuqu.work.dto.PreviousWorkDTO;
import com.sibuqu.work.dto.UserStudyDTO;
import com.sibuqu.work.dto.UserWorkListDTO;
import com.sibuqu.work.dto.UserWorkStatisticsDTO;
import com.sibuqu.work.dto.WorkInfoListDTO;
import com.sibuqu.work.entity.WorkInfo;
import com.sibuqu.work.enums.RecommendStatusEnums;
import com.sibuqu.work.enums.WorkModelFlagEnum;
import com.sibuqu.work.mongo.WorkInfoPojo;
import com.sibuqu.work.util.AESUtil;
import com.sibuqu.work.vo.admin.ClassWorkStatisticsVO;
import com.sibuqu.work.vo.admin.UserWorkStatisticsListVO;
import com.sibuqu.work.vo.api.UserStudyStatisticsDetailVO;
import com.sibuqu.work.vo.api.UserStudyVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WorkInfoPojoMongoService {

    @Autowired
    private MongoHelper mongoHelper;

    @Autowired
    private MongoTemplate mongoTemplate;
    private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public WorkInfoPojo getById(Integer workId) {
        return getByWorkId(workId);
    }

    public WorkInfoPojo getByWorkId(Integer workId) {
        log.info("workInfoPojo getByWorkId,入参:{}", workId);
        if (Objects.isNull(workId)) {
            throw new BusinessException("作业未查询到");
        }
        if (Objects.equals(workId, 0)) {
            throw new BusinessException("作业查询错误");
        }
        WorkInfoPojo workInfoPojo = mongoHelper.findOneByQuery(new CriteriaAndWrapper().eq(WorkInfoPojo::getId, workId), WorkInfoPojo.class);
        log.info("workInfoPojo getByWorkId,出参:{}", JSON.toJSONString(workInfoPojo));
        return workInfoPojo;
    }

    public WorkInfoPojo querySimpleOne(WorkInfoPojoQueryDTO dto) {
        log.info("workInfoPojo querySimpleOne,入参:{}", JSON.toJSONString(dto));
        CriteriaAndWrapper wrapper = buildSimpleCriteria(dto);
        WorkInfoPojo workInfoPojo = mongoHelper.findOneByQuery(wrapper, WorkInfoPojo.class);
        log.info("workInfoPojo querySimpleOne,出参:{}", JSON.toJSONString(workInfoPojo));
        return workInfoPojo;
    }

    public void saveOrUpdate(WorkInfoPojo workInfo) {
        log.info("workInfoPojo saveOrUpdate,入参:{}", JSON.toJSONString(workInfo));
        mongoHelper.getMongoTemplate().save(workInfo);
        log.info("workInfoPojo saveOrUpdate,结束");
    }

    public void saveOrUpdate(WorkInfo workInfo) {
        log.info("workInfo mongo saveOrUpdate,开始,入参:{}", JSON.toJSONString(workInfo));
        WorkInfoPojo workInfoPojo = getByWorkId(workInfo.getId());
        if (Objects.isNull(workInfoPojo)) {
            workInfoPojo = BeanUtil.copyProperties(workInfo, WorkInfoPojo.class, "serialVersionUID");
            workInfoPojo.setWorkId(workInfo.getId());
        } else {
            BeanUtil.copyProperties(workInfo, workInfoPojo, "serialVersionUID");
        }
        mongoHelper.getMongoTemplate().save(workInfoPojo);
        log.info("workInfo mongo saveOrUpdate,结束");
    }

    public IPage<WorkInfoPojo> previousWorkList(PreviousWorkDTO dto, HeaderUserInfo headerUserInfo, CourseTimetableUnlockDetailVO courseTimetableVO) {
        log.info("workInfoPojo previousWorkList,入参 dto:{},headerUserInfo:{},courseTimetableUnlockDetailVO:{}", JSON.toJSONString(dto), headerUserInfo.getId(), JSON.toJSONString(courseTimetableVO));
        CriteriaAndWrapper wrapper = new CriteriaAndWrapper()
                .eq(WorkInfoPojo::getUserId, headerUserInfo.getId())
                .eq(WorkInfoPojo::getCompanyId, headerUserInfo.getCurrentCompanyId())
                .eq(WorkInfoPojo::getCourseId, dto.getCourseId())
                .isNotNull(WorkInfoPojo::getContent)
                .lte(WorkInfoPojo::getCourseBeginTime, LocalDateTime.now());
        if (Objects.nonNull(courseTimetableVO)) {
            if (LocalDateTime.now().isAfter(courseTimetableVO.getWorkBeginTime()) && LocalDateTime.now().isBefore(courseTimetableVO.getWorkEndTime())) {
                wrapper.ne(WorkInfoPojo::getCourseTimetableId, courseTimetableVO.getId());
            }
        }
        Page<WorkInfoPojo> reqPage = new Page<>();
        reqPage.setCurr(dto.getPageNum());
        reqPage.setLimit(dto.getPageSize());
        SortBuilder sortBuilder = new SortBuilder().add(WorkInfoPojo::getCourseBeginTime, Sort.Direction.DESC);
        Page<WorkInfoPojo> page = mongoHelper.findPage(wrapper, sortBuilder, reqPage, WorkInfoPojo.class);
        return convertPage(page);
    }

    public IPage<WorkInfoPojo> heartPerceptionList(AppWorkHeartPerceptionDTO dto, HeaderUserInfo headerUserInfo) {
        log.info("workInfoPojo heartPerceptionList,入参 dto:{},headerUserInfo:{}", JSON.toJSONString(dto), headerUserInfo.getId());
        CriteriaAndWrapper wrapper = new CriteriaAndWrapper()
                .eq(WorkInfoPojo::getCompanyId, headerUserInfo.getCurrentCompanyId())
                .eq(WorkInfoPojo::getCourseId, dto.getCourseId())
                .eq(WorkInfoPojo::getContentShowStatus, 1)
                .eq(WorkInfoPojo::getShowStatus, 1);
        Integer classesNo = StrUtil.isNotBlank(dto.getClassesNo()) ? Integer.parseInt(dto.getClassesNo()) : null;
        if (classesNo == null) {
            classesNo = dto.getGroupId();
        }
        if (classesNo != null) {
            if (classesNo == 0) {
                wrapper.eq(WorkInfoPojo::getUserId, headerUserInfo.getId());
            } else {
                wrapper.and(new CriteriaOrWrapper().eq(WorkInfoPojo::getClassesNo, classesNo)
                        .eq(WorkInfoPojo::getUserId, headerUserInfo.getId()));
            }
        } else {
            wrapper.eq(WorkInfoPojo::getUserId, headerUserInfo.getId());
        }
        Page<WorkInfoPojo> reqPage = new Page<>();
        reqPage.setCurr(dto.getPageNum());
        reqPage.setLimit(dto.getPageSize());
        SortBuilder sortBuilder = new SortBuilder().add(WorkInfoPojo::getWorkTime, Sort.Direction.DESC);
        Page<WorkInfoPojo> page = mongoHelper.findPage(wrapper, sortBuilder, reqPage, WorkInfoPojo.class);
        page.getList().forEach(workInfoPojo -> {
            workInfoPojo.setCreateTime(workInfoPojo.getWorkTime());
            workInfoPojo.setUpdateTime(workInfoPojo.getWorkTime());
        });
        if (dto.getPageNum() == 1 && dto.getWorkId() != null && dto.getWorkId() > 0) {
            boolean hasWorkId = false;
            List<WorkInfoPojo> records = page.getList();
            for (int i = 0; i < records.size(); i++) {
                WorkInfoPojo record = records.get(i);
                if (record.getWorkId().equals(dto.getWorkId())) {
                    records.remove(i);
                    records.add(0, record);
                    hasWorkId = true;
                    break;
                }
            }
            if (!hasWorkId) {
                WorkInfoPojo workInfoPojo = mongoHelper.findOneByQuery(new CriteriaAndWrapper().eq(WorkInfoPojo::getId, dto.getWorkId()), WorkInfoPojo.class);
                if (workInfoPojo != null) {
                    records.add(0, workInfoPojo);
                }
            }
        }
        return convertPage(page);
    }

    public IPage<WorkInfoPojo> selectTeacherReplyWorkPage(AppWorkReplySearchDTO dto, List<Integer> workIdList, HeaderUserInfo headerUserInfo) {
        log.info("workInfoPojo selectTeacherReplyWorkPage,入参 dto:{},workIdList:{}", JSON.toJSONString(dto), workIdList);
        CriteriaAndWrapper wrapper = new CriteriaAndWrapper()
                .in(WorkInfoPojo::getWorkId, workIdList)
                .eq(WorkInfoPojo::getCourseId, dto.getCourseId())
                .eq(WorkInfoPojo::getCompanyId, headerUserInfo.getCurrentCompanyId())
                .eq(WorkInfoPojo::getContentShowStatus, 1)
                .eq(WorkInfoPojo::getShowStatus, 1);
        Page<WorkInfoPojo> reqPage = new Page<>();
        reqPage.setCurr(dto.getPageNum());
        reqPage.setLimit(dto.getPageSize());
        SortBuilder sortBuilder = new SortBuilder().add(WorkInfoPojo::getWorkTime, Sort.Direction.DESC);
        Page<WorkInfoPojo> page = mongoHelper.findPage(wrapper, sortBuilder, reqPage, WorkInfoPojo.class);
        page.getList().forEach(workInfoPojo -> {
            workInfoPojo.setCreateTime(workInfoPojo.getWorkTime());
            workInfoPojo.setUpdateTime(workInfoPojo.getWorkTime());
        });
        return convertPage(page);
    }

    public List<WorkInfoPojo> batchAsyncWork(Integer courseId, Integer contentType) {
        log.info("workInfoPojo saveOrUpdate,入参:courseId:{},contentType:{}", courseId, contentType);
        CriteriaAndWrapper wrapper = new CriteriaAndWrapper()
                .eq(WorkInfoPojo::getCourseId, courseId)
                .eq(WorkInfoPojo::getRecommendStatus, 1);
        if (PrefectureContentAsyncTypeEnum.TEACHER_REPLY.getCode().equals(contentType)) {
            wrapper.eq(WorkInfoPojo::getTeacherReplyStatus, YesOrNoEnum.YES.getCode());
        } else if (PrefectureContentAsyncTypeEnum.COMPANY_REPLY.getCode().equals(contentType)) {
            wrapper.eq(WorkInfoPojo::getCompanyReplyStatus, YesOrNoEnum.YES.getCode());
        }
        return mongoHelper.findListByQuery(wrapper, WorkInfoPojo.class);
    }

    public List<WorkInfoPojo> queryByWorkIdList(List<Integer> workIdList) {
        log.info("workInfoPojo queryByWorkIdList,入参:workIdList:{}", workIdList);
        CriteriaAndWrapper wrapper = new CriteriaAndWrapper()
                .in(WorkInfoPojo::getWorkId, workIdList);
        return mongoHelper.findListByQuery(wrapper, WorkInfoPojo.class);
    }

    public void insertAll(List<WorkInfoPojo> list) {
        log.info("insertAll 开始新增");
        mongoTemplate.insertAll(list);
        log.info("insertAll 结束新增");
    }

    public long todayRecommendCount(Integer companyId, Integer classesId) {
        log.info("workInfoPojo todayRecommendCount,入参:companyId:{},classesId:{}", companyId, classesId);
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 设置为当天的零点
        LocalDateTime startOfDay = LocalDateTime.of(currentDate, LocalTime.MIN);
        // 设置为明天的零点
        LocalDateTime endOfDay = LocalDateTime.of(currentDate.plusDays(1), LocalTime.MIN);
        return mongoHelper.findCountByQuery(new CriteriaAndWrapper()
                .eq(WorkInfoPojo::getCompanyId, companyId)
                .eq(WorkInfoPojo::getClassesId, classesId)
                .eq(WorkInfoPojo::getRecommendStatus, 1)
                .gt(WorkInfoPojo::getRecommendTime, startOfDay)
                .lt(WorkInfoPojo::getRecommendTime, endOfDay), WorkInfoPojo.class);
    }

    public IPage<WorkInfoPojo> recommendWorkList(AppWorkRecommendSearchDTO dto) {
        log.info("workInfoPojo recommendWorkList,入参:{}", JSON.toJSONString(dto));
        CriteriaAndWrapper wrapper = new CriteriaAndWrapper()
                .eq(WorkInfoPojo::getCompanyId, -1)
                .eq(WorkInfoPojo::getCourseId, dto.getCourseId())
                .eq(WorkInfoPojo::getRecommendStatus, 1)
                .eq(WorkInfoPojo::getContentShowStatus, 1)
                .eq(WorkInfoPojo::getRecommendTeacherStatus, 1);
        if (Objects.nonNull(dto.getReplyStatus()) && dto.getReplyStatus() >= 0) {
            wrapper.eq(WorkInfoPojo::getTeacherReplyStatus, dto.getReplyStatus());
        }
        SortBuilder sortBuilder = new SortBuilder().add(WorkInfoPojo::getWorkTime, Sort.Direction.DESC);
        Page<WorkInfoPojo> reqPage = new Page<>();
        reqPage.setCurr(dto.getPageNum());
        reqPage.setLimit(dto.getPageSize());
        Page<WorkInfoPojo> page = mongoHelper.findPage(wrapper, sortBuilder, reqPage, WorkInfoPojo.class);
        return convertPage(page);
    }

    public IPage<WorkInfoPojo> companyWorkList(AppWorkHeartPerceptionDTO dto, HeaderUserInfo headerUserInfo) {
        log.info("workInfoPojo companyWorkList,入参:{},headerUserInfo:{}", JSON.toJSONString(dto), headerUserInfo.getId());
        CriteriaAndWrapper wrapper = new CriteriaAndWrapper()
                .eq(WorkInfoPojo::getCompanyId, headerUserInfo.getCurrentCompanyId())
                .eq(WorkInfoPojo::getCourseId, dto.getCourseId())
                .eq(WorkInfoPojo::getContentShowStatus, 1)
                .eq(WorkInfoPojo::getShowStatus, 1);
        if (dto.getGroupId() != null) {
            wrapper.eq(WorkInfoPojo::getClassesId, dto.getGroupId());
        }
        if (dto.getTeamId() != null) {
            wrapper.eq(WorkInfoPojo::getTeamId, dto.getTeamId());
        }
        SortBuilder sortBuilder = new SortBuilder().add(WorkInfoPojo::getWorkTime, Sort.Direction.DESC);
        Page<WorkInfoPojo> reqPage = new Page<>();
        reqPage.setCurr(dto.getPageNum());
        reqPage.setLimit(dto.getPageSize());
        Page<WorkInfoPojo> page = mongoHelper.findPage(wrapper, sortBuilder, reqPage, WorkInfoPojo.class);
        page.getList().forEach(workInfoPojo -> {
            workInfoPojo.setCreateTime(workInfoPojo.getWorkTime());
            workInfoPojo.setUpdateTime(workInfoPojo.getWorkTime());
        });
        return convertPage(page);
    }

    public List<WorkInfoPojo> querySimpleList(WorkInfoPojoQueryDTO dto) {
        log.info("workInfoPojo querySimpleList,入参:{}", JSON.toJSONString(dto));
        CriteriaAndWrapper wrapper = buildSimpleCriteria(dto);
        List<WorkInfoPojo> list = mongoHelper.findListByQuery(wrapper, WorkInfoPojo.class);
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        log.info("workInfoPojo querySimpleList,查询结束");
        return list;
    }

    public long querySimpleCount(WorkInfoPojoQueryDTO dto) {
        log.info("workInfoPojo querySimpleCount,入参:{}", JSON.toJSONString(dto));
        CriteriaAndWrapper wrapper = buildSimpleCriteria(dto);
        return mongoHelper.findCountByQuery(wrapper, WorkInfoPojo.class);
    }

    public IPage<WorkInfoPojo> querySimplePage(WorkInfoPojoQueryDTO dto) {
        return querySimplePage(dto, new SortBuilder().add(WorkInfoPojo::getWorkTime, Sort.Direction.DESC));
    }

    public IPage<WorkInfoPojo> querySimplePage(WorkInfoPojoQueryDTO dto, SortBuilder sortBuilder) {
        log.info("workInfoPojo querySimplePage,入参:{}", JSON.toJSONString(dto));
        CriteriaAndWrapper wrapper = buildSimpleCriteria(dto);
        Query query = new Query(wrapper.build());
        query.with(sortBuilder.toSort());
        query.skip((long) (dto.getPageNum() - 1) * dto.getPageSize());// 从哪条记录开始
        query.limit(dto.getPageSize());// 取多少条记录
        List<WorkInfoPojo> list = mongoTemplate.find(query, WorkInfoPojo.class);
        IPage<WorkInfoPojo> voPage = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>();
        voPage.setRecords(list);
        voPage.setCurrent(dto.getPageNum());
        voPage.setSize(dto.getPageSize());
        voPage.setTotal(1);
        log.info("workInfoPojo querySimplePage,查询结束");
        return voPage;
    }

    public IPage<UserWorkStatisticsListVO> userWorkStatisticsList(UserWorkStatisticsDTO dto) {
        log.info("workInfoPojo userWorkStatisticsList,入参:{}", JSON.toJSONString(dto));
        CriteriaWrapper wrapper = new CriteriaAndWrapper()
                .eq(StrUtil.isNotBlank(dto.getCompanyId()), WorkInfoPojo::getCompanyId, StrUtil.isNotBlank(dto.getCompanyId()) ? Integer.parseInt(dto.getCompanyId()) : -1)
                .eq(StrUtil.isNotBlank(dto.getUserPhone()), WorkInfoPojo::getUserPhone, dto.getUserPhone())
                .like(StrUtil.isNotBlank(dto.getTeamName()), WorkInfoPojo::getTeamName, dto.getTeamName())
                .eq(StrUtil.isNotBlank(dto.getClassesNo()), WorkInfoPojo::getClassesNo, StrUtil.isNotBlank(dto.getClassesNo()) ? Integer.parseInt(dto.getClassesNo()) : -1)
                .eq(StrUtil.isNotBlank(dto.getClassesTeacherPhone()), WorkInfoPojo::getClassesTeacherPhone, dto.getClassesTeacherPhone())
                .eq(StrUtil.isNotBlank(dto.getCompanyCode()), WorkInfoPojo::getCompanyCode, dto.getCompanyCode())
                .eq(StrUtil.isNotBlank(dto.getCourseId()), WorkInfoPojo::getCourseId, StrUtil.isNotBlank(dto.getCourseId()) ? Integer.parseInt(dto.getCourseId()) : -1)
                .like(StrUtil.isNotBlank(dto.getCompanyName()), WorkInfoPojo::getCompanyName, dto.getCompanyName());
        if (Objects.nonNull(dto.getStartDate()) && Objects.nonNull(dto.getEndDate())) {
            // 设置为所属日期的零点
            LocalDateTime startOfDay = LocalDateTime.of(dto.getStartDate(), LocalTime.MIN);
            // 设置为所属日期第二天的零点
            LocalDateTime endOfDay = LocalDateTime.of(dto.getEndDate().plusDays(1), LocalTime.MIN);
            wrapper.gte(WorkInfoPojo::getCourseBeginTime, startOfDay)
                    .lte(WorkInfoPojo::getCourseBeginTime, endOfDay);
        }
        Page<WorkInfoPojo> reqPage = new Page<>();
        reqPage.setCurr(dto.getPageNum());
        reqPage.setLimit(dto.getPageSize());
        Page<WorkInfoPojo> page = mongoHelper.findPage(wrapper, reqPage, WorkInfoPojo.class);
        List<UserWorkStatisticsListVO> list = page.getList().stream().map(workInfoPojo -> {
            UserWorkStatisticsListVO vo = new UserWorkStatisticsListVO();
            vo.setBelongDate(workInfoPojo.getCourseBeginTime().toLocalDate());
            vo.setUserPhone(workInfoPojo.getUserPhone());
            vo.setUserName(workInfoPojo.getUserName());
            vo.setListenStatus(Objects.equals(workInfoPojo.getListenStatus(), 1));
            vo.setWorkStatus(Objects.equals(workInfoPojo.getWorkStatus(), 1));
            vo.setCourseName(workInfoPojo.getCourseName());
            vo.setClassesNo(workInfoPojo.getClassesNo());
            vo.setClassesName(workInfoPojo.getClassesName());
            vo.setTeamId(workInfoPojo.getTeamId());
            return vo;
        }).collect(Collectors.toList());
        IPage<UserWorkStatisticsListVO> voPage = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>();
        voPage.setRecords(list);
        voPage.setCurrent(page.getCurr());
        voPage.setSize(page.getLimit());
        voPage.setTotal(page.getCount());
        return voPage;
    }

    public IPage<WorkInfoPojo> getWorkInfoList(WorkInfoListDTO dto) {
        if (StrUtil.isNotBlank(dto.getUserPhone())) {
            if (Pattern.matches("^[1-9][0-9]*$", dto.getUserPhone())) {
                dto.setUserPhone(AESUtil.encrypt(dto.getUserPhone()));
            }
        }
        log.info("workInfoPojo getWorkInfoList,入参:{}", JSON.toJSONString(dto));
        CriteriaWrapper wrapper = new CriteriaAndWrapper()
                .eq(StrUtil.isNotBlank(dto.getCompanyId()) && Pattern.matches("^[1-9][0-9]*$", dto.getCompanyId()), WorkInfoPojo::getCompanyId, Integer.valueOf(dto.getCompanyId()))
                .eq(WorkInfoPojo::getCourseId, Integer.valueOf(dto.getCourseId()))
                .eq(Objects.nonNull(dto.getCourseTimetableId()), WorkInfoPojo::getCourseTimetableId, dto.getCourseTimetableId())
                .like(StrUtil.isNotBlank(dto.getClassesTeacherName()), WorkInfoPojo::getClassesTeacherName, dto.getClassesTeacherName())
                .eq(StrUtil.isNotBlank(dto.getClassesTeacherPhone()), WorkInfoPojo::getClassesTeacherPhone, dto.getClassesTeacherPhone())
                .eq(dto.getRecommendStatus() != null, WorkInfoPojo::getRecommendStatus, dto.getRecommendStatus())
                .eq(dto.getRecommendTeacherStatus() != null, WorkInfoPojo::getRecommendTeacherStatus, dto.getRecommendTeacherStatus())
                .eq(dto.getTeacherReplyStatus() != null, WorkInfoPojo::getTeacherReplyStatus, dto.getTeacherReplyStatus())
                .eq(StrUtil.isNotBlank(dto.getClassesNo()), WorkInfoPojo::getClassesNo, StrUtil.isNotBlank(dto.getClassesNo()) ? Integer.parseInt(dto.getClassesNo()) : -1)
                .eq(WorkInfoPojo::getUserFlag, Objects.nonNull(dto.getWorkModelFlag()) && dto.getWorkModelFlag() < 2 ? dto.getWorkModelFlag() : WorkModelFlagEnum.UNIFY.getCode())
                .eq(WorkModelFlagEnum.LEVEL_WORK_TEMPLATE.getCode().equals(dto.getWorkModelFlag()), WorkInfoPojo::getWorkModelFlag, dto.getWorkModelFlag())
                .eq(ObjectUtil.isNotNull(dto.getWorkModelId()), WorkInfoPojo::getWorkModelId, dto.getWorkModelId())
                .like(StrUtil.isNotBlank(dto.getUserName()), WorkInfoPojo::getUserName, dto.getUserName())
                .eq(StrUtil.isNotBlank(dto.getUserPhone()), WorkInfoPojo::getUserPhone, dto.getUserPhone())
                .isNotNull(WorkInfoPojo::getContent);
        if (StrUtil.isNotBlank(dto.getCourseBeginTimeStart()) && StrUtil.isNotBlank(dto.getCourseBeginTimeEnd())) {
            wrapper.gte(StrUtil.isNotBlank(dto.getCourseBeginTimeStart()), WorkInfoPojo::getCourseBeginTime, LocalDateTime.of(LocalDate.parse(dto.getCourseBeginTimeStart()), LocalTime.of(0, 0, 0)))
                    .lte(StrUtil.isNotBlank(dto.getCourseBeginTimeEnd()), WorkInfoPojo::getCourseBeginTime, LocalDateTime.of(LocalDate.parse(dto.getCourseBeginTimeEnd()), LocalTime.of(23, 59, 59)));
        }
        if (StrUtil.isNotBlank(dto.getRecommendTimeStart()) && StrUtil.isNotBlank(dto.getRecommendTimeEnd())) {
            wrapper.gte(WorkInfoPojo::getRecommendTime, LocalDateTime.of(LocalDate.parse(dto.getRecommendTimeStart()), LocalTime.of(0, 0, 0)))
                    .lte(WorkInfoPojo::getRecommendTime, LocalDateTime.of(LocalDate.parse(dto.getRecommendTimeEnd()), LocalTime.of(23, 59, 59)));
        }
        if (dto.getHaveClass() != null && dto.getHaveClass()) {
            wrapper.ne(WorkInfoPojo::getClassesNo, 0);
        } else if (dto.getHaveClass() != null) {
            wrapper.eq(WorkInfoPojo::getClassesNo, 0);
        }
        SortBuilder sortBuilder = new SortBuilder();
        if (Objects.nonNull(dto.getRecommendStatus()) && RecommendStatusEnums.YES.getCode().equals(dto.getRecommendStatus())) {
            sortBuilder.add(WorkInfoPojo::getRecommendTime, Sort.Direction.DESC);
        } else {
            sortBuilder.add(WorkInfoPojo::getWorkTime, Sort.Direction.DESC);
        }
        Page<WorkInfoPojo> reqPage = new Page<>();
        reqPage.setCurr(dto.getPageNum());
        reqPage.setLimit(dto.getPageSize());
        Page<WorkInfoPojo> page = mongoHelper.findPage(wrapper, sortBuilder, reqPage, WorkInfoPojo.class);
        IPage<WorkInfoPojo> voPage = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>();
        voPage.setRecords(page.getList());
        voPage.setCurrent(page.getCurr());
        voPage.setSize(page.getLimit());
        voPage.setTotal(page.getCount());
        return voPage;
    }

    public List<UserStudyStatisticsDetailVO> userStudyStatistics(Integer userId, Integer courseId, Integer companyId) {
        log.info("workInfoPojo userStudyStatistics,入参: userId:{},courseId:{},companyId:{}", userId, courseId, companyId);
        return querySimpleList(new WorkInfoPojoQueryDTO().setUserId(userId).setCourseId(courseId).setCompanyId(companyId))
                .stream().map(workInfoPojo -> {
                    UserStudyStatisticsDetailVO vo = new UserStudyStatisticsDetailVO();
                    vo.setWorkScore(workInfoPojo.getScore());
                    vo.setBelongDate(workInfoPojo.getCourseBeginTime().toLocalDate());
                    vo.setWorkStatus(Objects.equals(workInfoPojo.getWorkStatus(), 1));
                    vo.setListenStatus(Objects.equals(workInfoPojo.getListenStatus(), 1));
                    vo.setCourseTimetableId(workInfoPojo.getCourseTimetableId());
                    return vo;
                }).collect(Collectors.toList());
    }

    public IPage<WorkInfoPojo> convertPage(Page<WorkInfoPojo> page) {
        IPage<WorkInfoPojo> voPage = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>();
        voPage.setRecords(page.getList());
        voPage.setCurrent(page.getCurr());
        voPage.setSize(page.getLimit());
        voPage.setTotal(page.getCount());
        return voPage;
    }

    public CriteriaAndWrapper buildSimpleCriteria(WorkInfoPojoQueryDTO dto) {
        if (Objects.nonNull(dto.getCompanyId()) && Objects.nonNull(dto.getCourseId())
                && Objects.nonNull(dto.getCourseTimetableId()) && Objects.nonNull(dto.getWorkStatus())) {
            log.info("look look is who:{}", JSON.toJSONString(dto));
        }
        CriteriaAndWrapper wrapper = new CriteriaAndWrapper();
        if (dto.getCompanyId() != null) {
            wrapper.eq(WorkInfoPojo::getCompanyId, dto.getCompanyId());
        }
        if (dto.getCourseId() != null) {
            wrapper.eq(WorkInfoPojo::getCourseId, dto.getCourseId());
        }
        if (dto.getCourseTimetableId() != null) {
            wrapper.eq(WorkInfoPojo::getCourseTimetableId, dto.getCourseTimetableId());
        }
        if (CollUtil.isNotEmpty(dto.getCourseTimetableIdList())) {
            wrapper.in(WorkInfoPojo::getCourseTimetableId, dto.getCourseTimetableIdList());
        }
        if (dto.getWorkStatus() != null) {
            wrapper.eq(WorkInfoPojo::getWorkStatus, dto.getWorkStatus());
        }
        if (dto.getUserId() != null) {
            wrapper.eq(WorkInfoPojo::getUserId, dto.getUserId());
        }
        if (CollUtil.isNotEmpty(dto.getUserIdList())) {
            wrapper.in(WorkInfoPojo::getUserId, dto.getUserIdList());
        }
        if (dto.getClassesNo() != null) {
            wrapper.eq(WorkInfoPojo::getClassesNo, dto.getClassesNo());
        }
        if (dto.getRecommendStatus() != null) {
            wrapper.eq(WorkInfoPojo::getRecommendStatus, dto.getRecommendStatus());
        }
        if (dto.getShowStatus() != null) {
            wrapper.eq(WorkInfoPojo::getShowStatus, dto.getShowStatus());
        }
        if (dto.getContentShowStatus() != null) {
            wrapper.eq(WorkInfoPojo::getContentShowStatus, dto.getContentShowStatus());
        }
        if (dto.getCompanyReplyStatus() != null) {
            wrapper.eq(WorkInfoPojo::getCompanyReplyStatus, dto.getCompanyReplyStatus());
        }
        if (dto.getClassesId() != null) {
            wrapper.eq(WorkInfoPojo::getClassesId, dto.getClassesId());
        }
        if (dto.getTeamId() != null) {
            wrapper.eq(WorkInfoPojo::getTeamId, dto.getTeamId());
        }
        return wrapper;
    }

    public IPage<WorkInfoPojo> saasWorkList(UserWorkListDTO dto, List<CourseTimetable> courseTimetableList, List<ClassTeacherVO> classTeacherList) {
        CriteriaAndWrapper wrapper = new CriteriaAndWrapper();
        wrapper
                .eq(StrUtil.isNotBlank(dto.getCompanyId()), WorkInfo::getCompanyId, dto.getCompanyId())
                .eq(WorkInfo::getWorkStatus, 1)
                .in(courseTimetableList.size() > 0, WorkInfo::getCourseTimetableId, courseTimetableList.stream().map(CourseTimetable::getId).collect(Collectors.toList()))
                .like(StrUtil.isNotBlank(dto.getCompanyName()), WorkInfo::getCompanyName, dto.getCompanyName())
                .eq(StrUtil.isNotBlank(dto.getCourseId()), WorkInfo::getCourseId, dto.getCourseId())
                .eq(Objects.nonNull(dto.getClassesNo()), WorkInfo::getClassesNo, dto.getClassesNo())
                .eq(Objects.nonNull(dto.getTeamId()), WorkInfo::getTeamId, dto.getTeamId())
                .eq(Objects.nonNull(dto.getRecommendStatus()), WorkInfo::getRecommendStatus, dto.getRecommendStatus())
                .eq(StrUtil.isNotBlank(dto.getUserPhone()), WorkInfo::getUserPhone, dto.getUserPhone())
                .like(StrUtil.isNotBlank(dto.getUserName()), WorkInfo::getUserName, dto.getUserName())
                .eq(StrUtil.isNotBlank(dto.getClassesTeacherPhone()), WorkInfo::getClassesTeacherPhone, dto.getClassesTeacherPhone())
                .in(CollUtil.isNotEmpty(classTeacherList), WorkInfoPojo::getClassesTeacherPhone, classTeacherList.stream().map(ClassTeacherVO::getPhone).collect(Collectors.toList()));
        if (Objects.nonNull(dto.getStartDate()) && Objects.nonNull(dto.getEndDate())) {
            wrapper
                    .lte(WorkInfo::getCourseBeginTime, LocalDateTime.of(dto.getEndDate(), LocalTime.of(23, 59, 59)))
                    .gte(WorkInfo::getCourseBeginTime, LocalDateTime.of(dto.getStartDate(), LocalTime.of(0, 0, 0)));
        }
        SortBuilder sortBuilder = new SortBuilder().add(WorkInfoPojo::getWorkTime, Sort.Direction.DESC);
        Page<WorkInfoPojo> reqPage = new Page<>();
        reqPage.setCurr(dto.getPageNum());
        reqPage.setLimit(dto.getPageSize());
        Page<WorkInfoPojo> page = mongoHelper.findPage(wrapper, sortBuilder, reqPage, WorkInfoPojo.class);
        return convertPage(page);
    }

    public UserStudyVO getUserStudy(UserStudyDTO dto) {
        CriteriaWrapper wrapper = buildSimpleCriteria(new WorkInfoPojoQueryDTO().setUserId(dto.getUserId()).setCourseId(dto.getCourseId())
                .setCompanyId(dto.getCompanyId()));
        wrapper.gte(WorkInfoPojo::getWorkTime, dto.getCreateTime());
        List<WorkInfoPojo> list = mongoHelper.findListByQuery(wrapper, WorkInfoPojo.class);
        if (CollUtil.isEmpty(list)) {
            return new UserStudyVO();
        }
        long workStatusCount = list.stream().filter(workInfoPojo -> Objects.equals(workInfoPojo.getWorkStatus(), 1)).count();
        long listenStatusCount = list.stream().filter(workInfoPojo -> Objects.equals(workInfoPojo.getListenStatus(), 1)).count();
        UserStudyVO vo = new UserStudyVO();
        vo.setWorkNum((int) workStatusCount);
        vo.setListenNum((int) listenStatusCount);
        return vo;
    }

    public ClassWorkStatisticsVO userWorkStatistics(UserWorkStatisticsDTO dto, Integer courseNum) {
        CriteriaWrapper wrapper = new CriteriaAndWrapper()
                .eq(StrUtil.isNotBlank(dto.getCompanyId()), WorkInfoPojo::getCompanyId, StrUtil.isNotBlank(dto.getCompanyId()) ? Integer.parseInt(dto.getCompanyId()) : -1)
                .eq(StrUtil.isNotBlank(dto.getClassesNo()), WorkInfoPojo::getClassesNo, StrUtil.isNotBlank(dto.getClassesNo()) ? Integer.parseInt(dto.getClassesNo()) : -1)
                .like(StrUtil.isNotBlank(dto.getTeamName()), WorkInfoPojo::getTeamName, dto.getTeamName())
                .eq(StrUtil.isNotBlank(dto.getClassesTeacherPhone()), WorkInfoPojo::getClassesTeacherPhone, dto.getClassesTeacherPhone())
                .eq(StrUtil.isNotBlank(dto.getCompanyCode()), WorkInfoPojo::getCompanyCode, dto.getCompanyCode())
                .eq(StrUtil.isNotBlank(dto.getCourseId()), WorkInfoPojo::getCourseId, dto.getCourseId())
                .like(StrUtil.isNotBlank(dto.getCompanyName()), WorkInfoPojo::getCompanyName, dto.getCompanyName())
                .eq(StrUtil.isNotBlank(dto.getUserPhone()), WorkInfoPojo::getUserPhone, dto.getUserPhone());
        if (dto.getStartDate() != null && dto.getEndDate() != null) {
            wrapper
                    .lte(WorkInfo::getCourseBeginTime, LocalDateTime.of(dto.getEndDate(), LocalTime.of(23, 59, 59)))
                    .gte(WorkInfo::getCourseBeginTime, LocalDateTime.of(dto.getStartDate(), LocalTime.of(0, 0, 0)));
        }
        List<WorkInfoPojo> list = mongoHelper.findListByQuery(wrapper, WorkInfoPojo.class);
        ClassWorkStatisticsVO vo = new ClassWorkStatisticsVO();
        vo.setWorkTotal(courseNum);
        vo.setListenTotal(courseNum);
        if (CollUtil.isEmpty(list)) return vo;
        vo.setWorkNum((int) list.stream().filter(workInfoPojo -> Objects.equals(workInfoPojo.getWorkStatus(), 1)).count());
        vo.setListenNum((int) list.stream().filter(workInfoPojo -> Objects.equals(workInfoPojo.getListenStatus(), 1)).count());
        vo.setWorkRate(new BigDecimal(vo.getWorkNum()).divide(new BigDecimal(vo.getListenTotal()), 2, RoundingMode.UP).toString());
        vo.setListenRate(new BigDecimal(vo.getListenNum()).divide(new BigDecimal(vo.getListenTotal()), 2, RoundingMode.UP).toString());
        return vo;
    }

    public List<WorkInfoPojo> personalWorkRank(PersonalWorkRankDTO dto, Integer courseTimetableId) {
        log.info("personalWorkRank dto:{}, courseTimetableId:{}", JSON.toJSONString(dto), courseTimetableId);
        List<WorkInfoPojo> list = mongoHelper.findListByQuery(new CriteriaAndWrapper()
                .eq(WorkInfoPojo::getCompanyId, dto.getCompanyId())
                .eq(WorkInfoPojo::getCourseId, dto.getCourseId())
                .ne(WorkInfoPojo::getCourseTimetableId, courseTimetableId), WorkInfoPojo.class);
        log.info("personalWorkRank end");
        return list;
    }

    public List<WorkInfoPojo> weekWork(Integer courseId, Integer userId, List<Integer> cttList) {
        log.info("weekWork begin courseId:{}, userId:{}, cttList:{}", courseId, userId, cttList);
        List<WorkInfoPojo> list = mongoHelper.findListByQuery(new CriteriaAndWrapper()
                .eq(WorkInfoPojo::getCourseId, courseId)
                .eq(WorkInfoPojo::getUserId, userId)
                .in(WorkInfoPojo::getCourseTimetableId, cttList), WorkInfoPojo.class);
        log.info("weekWork end");
        return list;
    }

    /*课件播放页展示作业*/
    public IPage<WorkInfoPojo> heartPerceptionListByWareId(AppWorkHeartPerceptionDTO dto, HeaderUserInfo headerUserInfo) {
        log.info("workInfoPojo heartPerceptionListByWareId,入参 dto:{},headerUserInfo:{}", JSON.toJSONString(dto), headerUserInfo.getId());
        CriteriaAndWrapper wrapper = new CriteriaAndWrapper()
                .eq(WorkInfoPojo::getCourseTimetableId, dto.getCourseTimetableId())
                .eq(WorkInfoPojo::getShowStatus, 1)
                .eq(WorkInfoPojo::getWorkStatus, 1);


        Page<WorkInfoPojo> reqPage = new Page<>();
        reqPage.setCurr(dto.getPageNum());
        reqPage.setLimit(dto.getPageSize());
        SortBuilder sortBuilder = new SortBuilder();
        if (dto.getSortType().equals(1)){
            sortBuilder.add(WorkInfoPojo::getHotSort, Sort.Direction.DESC);
        }
        sortBuilder.add(WorkInfoPojo::getWorkTime, Sort.Direction.DESC);
        Page<WorkInfoPojo> page = mongoHelper.findPage(wrapper, sortBuilder, reqPage, WorkInfoPojo.class);
        page.getList().forEach(workInfoPojo -> {
            workInfoPojo.setCreateTime(workInfoPojo.getWorkTime());
            workInfoPojo.setUpdateTime(workInfoPojo.getWorkTime());
        });

        return convertPage(page);
    }

}
