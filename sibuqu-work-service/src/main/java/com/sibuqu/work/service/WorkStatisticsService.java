package com.sibuqu.work.service;

import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.user.vo.course.CxktResDateListVO;
import com.sibuqu.work.dto.*;
import com.sibuqu.work.vo.admin.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface WorkStatisticsService {
    ClassWorkStatisticsVO classWorkStatistics(ClassWorkStatisticsDTO classWorkStatisticsDTO);

    PageInfoBT<ClassWorkStatisticsListVO> classWorkStatisticsList(ClassWorkStatisticsDTO classWorkStatisticsDTO);

    ClassWorkStatisticsVO teamWorkStatistics(TeamWorkStatisticsDTO teamWorkStatisticsDTO);

    PageInfoBT<TeamWorkStatisticsListVO> teamWorkStatisticsList(TeamWorkStatisticsDTO teamWorkStatisticsDTO);

    ClassWorkStatisticsVO userWorkStatistics(UserWorkStatisticsDTO userWorkStatisticsDTO);

    PageInfoBT<UserWorkStatisticsListVO> userWorkStatisticsList(UserWorkStatisticsDTO userWorkStatisticsDTO);

    PageInfoBT<ClassWorkStatisticsListVO> selectClassWorkList(ClassWorkStatisticsDTO classWorkStatisticsDTO);

    ClassWorkInfoVO selectClassInfo(ClassWorkInfoDTO classWorkInfoDTO);

    List<WorkModelDetailVO> selectTextInfo(Integer workModelId);

    ResultInfo<PageInfoBT<ClassWorkStatisticsInfoVO>> selectClassWorkInfoList(ClassWorkStatisticsInfoDTO classWorkStatisticsInfoDTO);

    void classWorkInfoListExport(ClassWorkStatisticsInfoDTO classWorkStatisticsInfoDTO, HttpServletResponse response);

    List<CxktResDateListVO> queryCxktResDataByCourseId(Integer courseId);
}
