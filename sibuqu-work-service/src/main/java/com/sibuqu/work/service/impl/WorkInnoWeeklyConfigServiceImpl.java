package com.sibuqu.work.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sibuqu.base.common.exception.BusinessException;
import com.sibuqu.base.common.result.HeaderUserInfo;
import com.sibuqu.base.common.utils.BeanUtil;
import com.sibuqu.base.common.utils.FieldUtil;
import com.sibuqu.course.entity.CourseInfo;
import com.sibuqu.starter.redis.headeruserinfo.HeaderUserInfoUtil;
import com.sibuqu.work.dto.AddInnoWeeklyConfigDTO;
import com.sibuqu.work.dto.QueryInnoWeeklyConfigDTO;
import com.sibuqu.work.entity.WorkInnoWeeklyConfig;
import com.sibuqu.work.mapper.WorkInnoWeeklyConfigMapper;
import com.sibuqu.work.service.WorkInnoWeeklyConfigService;
import com.sibuqu.work.service.WorkInnoWeeklyInfoService;
import com.sibuqu.work.vo.InnoWeeklyConfigVO;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sibuqu.base.common.enums.IsDeleteEnum;
import com.sibuqu.work.componet.CourseComponent;

@Slf4j
@Service
public class WorkInnoWeeklyConfigServiceImpl extends ServiceImpl<WorkInnoWeeklyConfigMapper, WorkInnoWeeklyConfig>
        implements WorkInnoWeeklyConfigService {

    @Autowired
    private WorkInnoWeeklyInfoService workInnoWeeklyInfoService;

    @Autowired
    private CourseComponent courseComponent;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addInnoWeeklyConfig(AddInnoWeeklyConfigDTO dto) {
        if (dto.getWorkBeginWeekday() > dto.getWorkEndWeekday()) {
            throw new BusinessException("开始日期的不能晚于结束日期");
        }
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        LocalDateTime now = LocalDateTime.now();

        WorkInnoWeeklyConfig config = BeanUtil.copyProperties(dto, WorkInnoWeeklyConfig.class);
        FieldUtil.setCreateAndUpdate(config, headerUserInfo, now);
        this.save(config);

        // 根据周作业配置生成多个周作业信息
        workInnoWeeklyInfoService.generateWeeklyInfo(config);

        return config.getId();
    }

    @Override
    public InnoWeeklyConfigVO queryInnoWeeklyConfig(QueryInnoWeeklyConfigDTO dto) {
        WorkInnoWeeklyConfig config = this.getById(dto.getId());
        if (Objects.isNull(config) || config.getDeleteFlag().equals(1)) {
            return null;
        }

        InnoWeeklyConfigVO vo = new InnoWeeklyConfigVO();
        BeanUtil.copyProperties(config, vo);
        CourseInfo courseInfo = courseComponent.courseInfoById(config.getCourseId());
        if (Objects.nonNull(courseInfo)) {
            vo.setCourseName(courseInfo.getCourseTitle());
        }
        return vo;
    }

    @Override
    public List<InnoWeeklyConfigVO> queryInnoWeeklyConfigList() {
        // 查询所有未删除的配置
        List<WorkInnoWeeklyConfig> configList = this.list(new LambdaQueryWrapper<WorkInnoWeeklyConfig>()
                .eq(WorkInnoWeeklyConfig::getDeleteFlag, IsDeleteEnum.NO.getCode())
                .orderByDesc(WorkInnoWeeklyConfig::getId));

        // 转换为VO
        return configList.stream().map(config -> {
            InnoWeeklyConfigVO vo = new InnoWeeklyConfigVO();
            BeanUtil.copyProperties(config, vo);
            CourseInfo courseInfo = courseComponent.courseInfoById(config.getCourseId());
            if (Objects.nonNull(courseInfo)) {
                vo.setCourseName(courseInfo.getCourseTitle());
            }
            return vo;
        }).collect(Collectors.toList());
    }

}
