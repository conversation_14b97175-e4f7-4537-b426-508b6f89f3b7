package com.sibuqu.work.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.work.dto.AdminQueryInnoWorkPageDTO;
import com.sibuqu.work.dto.BatchQueryInnoCttStatusDTO;
import com.sibuqu.work.dto.InnoWorkRecommendDTO;
import com.sibuqu.work.dto.QueryInnoInfoByCourseIdAndCttIdDTO;
import com.sibuqu.work.dto.QueryInnoPosterPageDTO;
import com.sibuqu.work.dto.QueryInnoWorkDTO;
import com.sibuqu.work.dto.WriteInnoWorkDTO;
import com.sibuqu.work.entity.WorkInnoInfo;
import com.sibuqu.work.vo.AdminInnoWorkPageVO;
import com.sibuqu.work.vo.InnoPosterPageVO;
import com.sibuqu.work.vo.WorkInnoInfoVO;

import java.util.List;
import java.util.Map;

public interface WorkInnoInfoService extends IService<WorkInnoInfo> {

    /**
     * 写inno作业
     * 
     * @param dto 写作业参数
     * @return 作业id
     */
    Long writeInnoWork(WriteInnoWorkDTO dto);

    /**
     * 查询inno作业
     * 
     * @param dto 查询参数
     * @return 作业信息
     */
    WorkInnoInfoVO queryInnoWork(QueryInnoWorkDTO dto);

    Map<Integer, Boolean> batchQueryInnoCttStatus(BatchQueryInnoCttStatusDTO dto);

    Integer queryInnoInfoByCourseIdAndCttId(QueryInnoInfoByCourseIdAndCttIdDTO dto);

    Map<Integer, Long> queryByCourseIdAndCttIdList(Integer courseId, List<Integer> cttIdList, List<Integer> userIdList);

    /**
     * 分页查询inno海报
     *
     * @param dto 查询参数
     * @return 分页结果
     */
    PageInfoBT<InnoPosterPageVO> queryInnoPosterPage(QueryInnoPosterPageDTO dto);

    /**
     * 管理后台分页查询inno作业
     *
     * @param dto 查询参数
     * @return 分页结果
     */
    PageInfoBT<AdminInnoWorkPageVO> adminQueryInnoWorkPage(AdminQueryInnoWorkPageDTO dto);

    /**
     * 管理后台推荐/取消推荐inno作业
     *
     * @param dto 推荐参数
     * @return 操作结果
     */
    Boolean adminRecommendInnoWork(InnoWorkRecommendDTO dto);
}
