package com.sibuqu.work.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mongodb.client.result.UpdateResult;
import com.sibuqu.base.common.enums.IsDeleteEnum;
import com.sibuqu.base.common.enums.YesOrNoEnum;
import com.sibuqu.base.common.exception.BusinessException;
import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.mysqlmultiple.annotation.DataSource;
import com.sibuqu.mysqlmultiple.enums.DataSourceTypeEnum;
import com.sibuqu.work.dto.AddWorkTeacherReplyDTO;
import com.sibuqu.work.dto.WorkInfoListDTO;
import com.sibuqu.work.dto.WorkReplyPageDTO;
import com.sibuqu.work.entity.WorkInfo;
import com.sibuqu.work.entity.WorkTeacherReply;
import com.sibuqu.work.enums.ReplyUserTypeEnums;
import com.sibuqu.work.enums.WorkDetailTypeEnum;
import com.sibuqu.work.mapper.WorkInfoMapper;
import com.sibuqu.work.mapper.WorkTeacherReplyMapper;
import com.sibuqu.work.mongo.WorkInfoPojo;
import com.sibuqu.work.service.WorkTeacherReplyService;
import com.sibuqu.work.util.AESUtil;
import com.sibuqu.work.vo.admin.WorkModelDetailVO;
import com.sibuqu.work.vo.admin.WorkTeacherReplyVO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.sibuqu.work.enums.WorkDetailTypeEnum.DUSING;
import static com.sibuqu.work.enums.WorkDetailTypeEnum.TEXT;

/**
 * <p>
 * 老师回复表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022年5月18日
 */
@Service("workTeacherReplyService")
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class WorkTeacherReplyServiceImpl extends ServiceImpl<WorkTeacherReplyMapper, WorkTeacherReply> implements WorkTeacherReplyService {
    private final WorkInfoMapper workInfoMapper;
    private final MongoTemplate mongoTemplate;

    @Override
    public Boolean replayDelete(Integer id){
        Boolean result=false;
        WorkTeacherReply workTeacherReply = this.getById(id);
        if(workTeacherReply==null){
            throw new BusinessException("回复不存在");
        }
        if(IsDeleteEnum.YES.getCode().equals(workTeacherReply.getDeleteFlag())){
            result=true;
        }else{
                this.removeById(id);
                if (workTeacherReply.getReplyUserType().equals(ReplyUserTypeEnums.TEACHER.getCode())){
                    if (this.count(new QueryWrapper<WorkTeacherReply>().lambda()
                            .eq(WorkTeacherReply::getWorkId,workTeacherReply.getWorkId())
                            .eq(WorkTeacherReply::getReplyUserType,ReplyUserTypeEnums.TEACHER.getCode())) < 1){
                        workInfoMapper.update(new WorkInfo(),
                                new LambdaUpdateWrapper<WorkInfo>().eq(WorkInfo::getId,workTeacherReply.getWorkId())
                                .set(WorkInfo::getTeacherReplyStatus, IsDeleteEnum.NO.getCode())
                                .set(WorkInfo::getTeacherReplyTime,null)
                        );
                    }
                }else if (workTeacherReply.getReplyUserType().equals(ReplyUserTypeEnums.COMPANY_USER.getCode())){
                    if (this.count(new QueryWrapper<WorkTeacherReply>().lambda()
                            .eq(WorkTeacherReply::getWorkId,workTeacherReply.getWorkId())
                            .eq(WorkTeacherReply::getReplyUserType,ReplyUserTypeEnums.COMPANY_USER.getCode())) < 1){
                        workInfoMapper.update(new WorkInfo(),
                                new LambdaUpdateWrapper<WorkInfo>().eq(WorkInfo::getId,workTeacherReply.getWorkId())
                                        .set(WorkInfo::getCompanyReplyStatus, YesOrNoEnum.NO.getCode())
                                        .set(WorkInfo::getCompanyReplyTime,null)
                        );
                    }
                }
            WorkInfo workInfo = workInfoMapper.selectById(workTeacherReply.getWorkId());
            Query query = new Query().addCriteria(Criteria.where("workId").is(workTeacherReply.getWorkId()));
            WorkInfoPojo workInfoPojoFindOne = mongoTemplate.findOne(query, WorkInfoPojo.class);
            if (Objects.isNull(workInfoPojoFindOne)) {
                WorkInfoPojo workInfoPojo = new WorkInfoPojo();
                BeanUtils.copyProperties(workInfo, workInfoPojo);
                workInfoPojo.setWorkId(workInfo.getId());
                mongoTemplate.save(workInfoPojo);
            } else {
                WorkInfoPojo workInfoPojo = new WorkInfoPojo();
                BeanUtils.copyProperties(workInfo, workInfoPojo);
                workInfoPojo.setWorkId(workInfo.getId());
                mongoTemplate.remove(query, WorkInfoPojo.class);
                mongoTemplate.save(workInfoPojo);
            }

        }
        return  result;
    }

    @Override
    public List<WorkTeacherReply> queryByCourseId(Integer courseId) {
        return baseMapper.queryByCourseId(courseId);
    }

    @Override
    public boolean workHasReply(Integer workId, Integer replyUserType) {
        return baseMapper.workHasReply(workId, replyUserType);
    }

    @Override
    public List<WorkTeacherReply> listByWorkIdList(List<Integer> workIdList) {
        if (CollUtil.isEmpty(workIdList)) {
            return new ArrayList<>();
        }
        return list(new LambdaQueryWrapper<WorkTeacherReply>()
                .in(WorkTeacherReply::getWorkId, workIdList)
                .eq(WorkTeacherReply::getDeleteFlag, IsDeleteEnum.NO.getCode())
        );
    }

    @Override
    @DataSource(DataSourceTypeEnum.SLAVE)
    public PageInfoBT<WorkTeacherReplyVO> workReplyPage(WorkReplyPageDTO dto) {
        if (StringUtils.isNotEmpty(dto.getUserPhone())){
            dto.setUserPhone(AESUtil.encrypt(dto.getUserPhone()));
        }
        IPage<WorkTeacherReplyVO> workTeacherReplyVOIPage = baseMapper.workReplyPage(dto.getPage(), dto);
        if (Objects.nonNull(workTeacherReplyVOIPage.getRecords())) {
            workTeacherReplyVOIPage.getRecords().forEach(v -> {
                if (Objects.nonNull(v.getTeacherUserId()) && v.getTeacherUserId().equals(9999999)) {
                    v.setAiReplyStatus(1);
                } else {
                    v.setAiReplyStatus(0);
                }
                if (StringUtils.isNotBlank(v.getContent())) {
                    List<WorkModelDetailVO> workModelDetailVOS = JSONObject.parseArray(v.getContent(), WorkModelDetailVO.class);
                    if (CollectionUtil.isNotEmpty(workModelDetailVOS)) {
                        List<String> contentList = workModelDetailVOS.stream().filter(detail ->
                                        WorkDetailTypeEnum.TEXT.getCode().equals(detail.getType()) ||
                                                WorkDetailTypeEnum.DUSING.getCode().equals(detail.getType()) ||
                                                WorkDetailTypeEnum.AUDIO_TEXT.getCode().equals(detail.getType()))
                                .map(WorkModelDetailVO::getContent).collect(Collectors.toList());

                        List<String> collectListTitle = workModelDetailVOS.stream().filter(detail -> WorkDetailTypeEnum.TEXT.getCode().equals(detail.getType()) ||
                                        WorkDetailTypeEnum.DUSING.getCode().equals(detail.getType()) ||
                                        WorkDetailTypeEnum.AUDIO_TEXT.getCode().equals(detail.getType()))
                                .map(WorkModelDetailVO::getTitle).collect(Collectors.toList());
                        for (int i = 0; i < contentList.size(); i++) {
                            if (i == 0) {
                                v.setWorkContentOne(contentList.get(i));
                                v.setWorkTitleOne(collectListTitle.get(i));
                            }
                            if (i == 1) {
                                v.setWorkContentTwo(contentList.get(i));
                                v.setWorkTitleTwo(collectListTitle.get(i));
                            }
                        }
                    }
                }

            });
        }
        return PageInfoBT.fromPage(workTeacherReplyVOIPage);
    }

    @Override
    @DataSource(DataSourceTypeEnum.SLAVE)
    public List<WorkTeacherReplyVO> workReplyPageExport(WorkReplyPageDTO dto) {
        dto.setPageSize(3000);
        dto.setPageNum(1);
        if (StringUtils.isNotEmpty(dto.getUserPhone())){
            dto.setUserPhone(AESUtil.encrypt(dto.getUserPhone()));
        }
        IPage<WorkTeacherReplyVO> workTeacherReplyVOIPage = baseMapper.workReplyPage(dto.getPage(), dto);
        if (Objects.nonNull(workTeacherReplyVOIPage.getRecords())) {
            workTeacherReplyVOIPage.getRecords().forEach(v -> {
                if (Objects.nonNull(v.getTeacherUserId()) && v.getTeacherUserId().equals(9999999)) {
                    v.setAiReplyStatus(1);
                } else {
                    v.setAiReplyStatus(0);
                }
                if (StringUtils.isNotBlank(v.getContent())) {
                    List<WorkModelDetailVO> workModelDetailVOS = JSONObject.parseArray(v.getContent(), WorkModelDetailVO.class);
                    if (CollectionUtil.isNotEmpty(workModelDetailVOS)) {
                        List<String> contentList = workModelDetailVOS.stream().filter(detail ->
                                        WorkDetailTypeEnum.TEXT.getCode().equals(detail.getType()) ||
                                                WorkDetailTypeEnum.DUSING.getCode().equals(detail.getType()) ||
                                                WorkDetailTypeEnum.AUDIO_TEXT.getCode().equals(detail.getType()))
                                .map(WorkModelDetailVO::getContent).collect(Collectors.toList());

                        List<String> collectListTitle = workModelDetailVOS.stream().filter(detail -> WorkDetailTypeEnum.TEXT.getCode().equals(detail.getType()) ||
                                        WorkDetailTypeEnum.DUSING.getCode().equals(detail.getType()) ||
                                        WorkDetailTypeEnum.AUDIO_TEXT.getCode().equals(detail.getType()))
                                .map(WorkModelDetailVO::getTitle).collect(Collectors.toList());
                        for (int i = 0; i < contentList.size(); i++) {
                            if (i == 0) {
                                v.setWorkContentOne(contentList.get(i));
                                v.setWorkTitleOne(collectListTitle.get(i));
                            }
                            if (i == 1) {
                                v.setWorkContentTwo(contentList.get(i));
                                v.setWorkTitleTwo(collectListTitle.get(i));
                            }
                        }
                    }
                }

            });
        }
        return workTeacherReplyVOIPage.getRecords();
    }

    @Override
    public Integer workReplyHideShow(Integer id) {

        UpdateWrapper<WorkTeacherReply> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(WorkTeacherReply::getId, id);
        QueryWrapper<WorkTeacherReply> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(WorkTeacherReply::getId, id);
        WorkTeacherReply workTeacherReply1 = baseMapper.selectOne(queryWrapper);
        if (workTeacherReply1.getDeleteFlag().equals(IsDeleteEnum.NO.getCode())) {
            updateWrapper.lambda().set(WorkTeacherReply::getDeleteFlag, IsDeleteEnum.YES.getCode());
        } else if (workTeacherReply1.getDeleteFlag().equals(IsDeleteEnum.YES.getCode())){
            updateWrapper.lambda().set(WorkTeacherReply::getDeleteFlag, IsDeleteEnum.NO.getCode());
        }

        return baseMapper.update(new WorkTeacherReply(), updateWrapper);
    }

    @Override
    public Integer addWorkReply(AddWorkTeacherReplyDTO dto) {
        UpdateWrapper<WorkInfo> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(WorkInfo::getId, dto.getWorkId());
        updateWrapper.lambda().set(WorkInfo::getTeacherReplyStatus, 1);
        workInfoMapper.update(new WorkInfo(), updateWrapper);

//        // 创建查询条件
//        Query query = new Query(Criteria.where("workId").is(dto.getWorkId()));
//        List<WorkInfoPojo> workInfoPojos = mongoTemplate.find(query, WorkInfoPojo.class);
//        if (Objects.nonNull(workInfoPojos) && workInfoPojos.size() > 0 && !workInfoPojos.get(0).getTeacherReplyStatus().equals(1)) {
//            // 创建更新操作
//            Update update = new Update();
//            update.set("teacherReplyStatus", 1);
//            update.set("updateTime", new Date()); // 同时更新时间
//            // 执行更新
//            mongoTemplate.updateFirst(
//                    query,
//                    update,
//                    WorkInfoPojo.class
//            );
//        }

        WorkTeacherReply workTeacherReply = new WorkTeacherReply();
        workTeacherReply.setTeacherUserId(6666666);
        workTeacherReply.setUserName(dto.getReplyName());
        workTeacherReply.setTeacherContent(dto.getReplyContent());
        workTeacherReply.setWorkId(dto.getWorkId());
        workTeacherReply.setCourseId(dto.getCourseId());
        workTeacherReply.setDeleteFlag(0);
        return baseMapper.insert(workTeacherReply);
    }


}
