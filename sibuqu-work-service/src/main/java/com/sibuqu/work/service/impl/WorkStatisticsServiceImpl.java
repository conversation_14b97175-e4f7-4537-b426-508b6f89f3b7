package com.sibuqu.work.service.impl;


import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sibuqu.base.common.enums.YesOrNoEnum;
import com.sibuqu.base.common.exception.BusinessException;
import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.classes.vo.MemberListByCourseIdClassesNoTeamIdVO;
import com.sibuqu.course.entity.CourseInfo;
import com.sibuqu.course.vo.coursetimetable.CourseTimetableHistoryVO;
import com.sibuqu.mysqlmultiple.annotation.DataSource;
import com.sibuqu.mysqlmultiple.enums.DataSourceTypeEnum;
import com.sibuqu.user.dto.classs.ClassMemberUserInfoDTO;
import com.sibuqu.user.dto.course.CxktResDateQueryListDTO;
import com.sibuqu.user.vo.classs.ClassMemberUserInfoVO;
import com.sibuqu.user.vo.course.CxktResDateListVO;
import com.sibuqu.work.componet.ClassesComponent;
import com.sibuqu.work.componet.CourseComponent;
import com.sibuqu.work.componet.OldServerComponent;
import com.sibuqu.work.dto.ClassWorkInfoDTO;
import com.sibuqu.work.dto.ClassWorkStatisticsDTO;
import com.sibuqu.work.dto.ClassWorkStatisticsInfoDTO;
import com.sibuqu.work.dto.TeamWorkStatisticsDTO;
import com.sibuqu.work.dto.UserWorkStatisticsDTO;
import com.sibuqu.work.entity.WorkInfo;
import com.sibuqu.work.entity.WorkInfoDetail;
import com.sibuqu.work.entity.WorkModelDetail;
import com.sibuqu.work.enums.WorkDetailTypeEnum;
import com.sibuqu.work.feignclient.client.UserFeignClient;
import com.sibuqu.work.mapper.WorkInfoMapper;
import com.sibuqu.work.mapper.WorkModelDetailMapper;
import com.sibuqu.work.service.WorkInfoPojoMongoService;
import com.sibuqu.work.service.WorkStatisticsService;
import com.sibuqu.work.vo.admin.ClassWorkInfoVO;
import com.sibuqu.work.vo.admin.ClassWorkStatisticsInfoVO;
import com.sibuqu.work.vo.admin.ClassWorkStatisticsListVO;
import com.sibuqu.work.vo.admin.ClassWorkStatisticsVO;
import com.sibuqu.work.vo.admin.TeamWorkStatisticsListVO;
import com.sibuqu.work.vo.admin.UserWorkStatisticsListVO;
import com.sibuqu.work.vo.admin.WorkExtraVO;
import com.sibuqu.work.vo.admin.WorkModelDetailVO;
import com.sibuqu.work.vo.admin.WorkModelVO;
import com.sibuqu.work.vo.api.UserFlagInfoVO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service("WorkStatisticsService")
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class WorkStatisticsServiceImpl implements WorkStatisticsService {
    private final WorkInfoMapper workInfoMapper;
    private final CourseComponent courseComponent;
    private final WorkModelDetailMapper workModelDetailMapper;
    private final UserFeignClient userFeignClient;
    private final WorkInfoPojoMongoService workInfoPojoMongoService;
    private final OldServerComponent oldServerComponent;
    private final ClassesComponent classesComponent;

    @Override
    public ClassWorkStatisticsVO classWorkStatistics(ClassWorkStatisticsDTO classWorkStatisticsDTO) {
        return workInfoMapper.classWorkStatistics(classWorkStatisticsDTO);
    }

    @Override
    public PageInfoBT<ClassWorkStatisticsListVO> classWorkStatisticsList(ClassWorkStatisticsDTO classWorkStatisticsDTO) {
        return PageInfoBT.fromPage(workInfoMapper.classWorkStatisticsList(classWorkStatisticsDTO.getPage(), classWorkStatisticsDTO));
    }

    @Override
    public ClassWorkStatisticsVO teamWorkStatistics(TeamWorkStatisticsDTO teamWorkStatisticsDTO) {
        return null;
    }

    @Override
    public PageInfoBT<TeamWorkStatisticsListVO> teamWorkStatisticsList(TeamWorkStatisticsDTO teamWorkStatisticsDTO) {

        return null;
    }

    @Override
    public ClassWorkStatisticsVO userWorkStatistics(UserWorkStatisticsDTO dto) {
        if (StringUtils.isNotBlank(dto.getUserPhone())) {
            List<CourseTimetableHistoryVO> courseHistoryTimetable = courseComponent.getCourseHistoryTimetable(Integer.valueOf(dto.getCourseId()));
            int courseNum = courseHistoryTimetable.size();
            if (courseNum == 0) {
                return ClassWorkStatisticsVO
                        .builder()
                        .listenNum(0)
                        .workNum(0)
                        .listenRate("0.00")
                        .workRate("0.00")
                        .workTotal(0)
                        .listenTotal(0)
                        .build();
            }
            return workInfoPojoMongoService.userWorkStatistics(dto, courseNum);
        } else {
            ClassWorkStatisticsDTO classWorkStatisticsDTO = new ClassWorkStatisticsDTO();
            BeanUtils.copyProperties(dto, classWorkStatisticsDTO);
            return classWorkStatistics(classWorkStatisticsDTO);
        }
    }

    @Override
    public PageInfoBT<UserWorkStatisticsListVO> userWorkStatisticsList(UserWorkStatisticsDTO userWorkStatisticsDTO) {
        IPage<UserWorkStatisticsListVO> page = workInfoPojoMongoService.userWorkStatisticsList(userWorkStatisticsDTO);
        if (page.getRecords().size() < 1) {
            return PageInfoBT.from(0, new ArrayList<>());
        }
        return PageInfoBT.fromPage(page);
    }

    @Override
    @DataSource(DataSourceTypeEnum.SLAVE)
    public PageInfoBT<ClassWorkStatisticsListVO> selectClassWorkList(ClassWorkStatisticsDTO classWorkStatisticsDTO) {
        IPage<ClassWorkStatisticsListVO> classWorkStatisticsListVOIPage = workInfoMapper.selectClassWorkList(classWorkStatisticsDTO.getPage(), classWorkStatisticsDTO);
        if (CollectionUtils.isEmpty(classWorkStatisticsListVOIPage.getRecords())) {
            return PageInfoBT.from(0, new ArrayList<>());
        }
        // 查询课件名称
        List<CourseTimetableHistoryVO> courseHistoryTimetable = courseComponent.getCourseHistoryTimetable(Integer.valueOf(classWorkStatisticsDTO.getCourseId()));
        if (CollectionUtils.isEmpty(courseHistoryTimetable) || courseHistoryTimetable.size() < 1) {
            return PageInfoBT.from(0, new ArrayList<>());
        }

        Map<Integer, CourseTimetableHistoryVO> courseTimetableHistoryVOMap = courseHistoryTimetable.stream().collect(Collectors.toMap(CourseTimetableHistoryVO::getId, courseTimetableHistoryVO -> courseTimetableHistoryVO));
        // 设置课件名称
        return PageInfoBT.fromPage(classWorkStatisticsListVOIPage.convert(item -> {
            if (Objects.nonNull(courseTimetableHistoryVOMap.get(item.getCourseTimetableId()))) {
                item.setCourseWareName(courseTimetableHistoryVOMap.get(item.getCourseTimetableId()).getResourceTitle());
            }
            return item;
        }));
    }

    @Override
    @DataSource(DataSourceTypeEnum.SLAVE)
    public ClassWorkInfoVO selectClassInfo(ClassWorkInfoDTO classWorkInfoDTO) {
        // 1、查询作业详情
        ClassWorkInfoVO classWorkInfoVO = workInfoMapper.selectClassInfo(classWorkInfoDTO);
        // 2、根据手机号查询班主任信息
        ClassMemberUserInfoDTO classMemberUserInfoDTO = new ClassMemberUserInfoDTO();
        classMemberUserInfoDTO.setUserRole(3);
        classMemberUserInfoDTO.setCourseId(classWorkInfoVO.getCourseId());
        List<Integer> classGroupIds = Arrays.asList(Integer.valueOf(classWorkInfoVO.getClassesNo()));
        classMemberUserInfoDTO.setClassGroupIds(classGroupIds);
        ResultInfo<List<ClassMemberUserInfoVO>> listResultInfo = userFeignClient.classMemberUserByEntity(classMemberUserInfoDTO);
        List<ClassMemberUserInfoVO> data = listResultInfo.getData();
        if (!CollectionUtils.isEmpty(data)) {
            ClassMemberUserInfoVO classMemberUserInfoVO = data.get(0);
            classWorkInfoVO.setClassesTeacherId(classMemberUserInfoVO.getUserId());
            classWorkInfoVO.setClassesTeacherName(classMemberUserInfoVO.getUserName());
            classWorkInfoVO.setClassTeacherPhone(classWorkInfoVO.getClassTeacherPhone());
        }
        List<MemberListByCourseIdClassesNoTeamIdVO> memberList = classesComponent.memberListByCourseIdClassesNoTeamId(-1, classWorkInfoVO.getCourseId(), classWorkInfoVO.getClassesNo(), null);
        CourseInfo courseInfo = courseComponent.courseInfoById(classWorkInfoVO.getCourseId());
        if (CollUtil.isNotEmpty(memberList)) {
            List<String> unListenUserName = new ArrayList<>();
            List<Integer> userIds = new ArrayList<>();
            memberList.forEach(userInfoVO -> {
                userIds.add(userInfoVO.getUserId());
            });
            Map<Integer, MemberListByCourseIdClassesNoTeamIdVO> userInfoVOMap = memberList.stream().collect(Collectors.toMap(MemberListByCourseIdClassesNoTeamIdVO::getUserId, i -> i));
            List<WorkInfo> workInfos = workInfoMapper.selectList(new QueryWrapper<WorkInfo>().lambda()
                    .between(Objects.nonNull(classWorkInfoDTO.getBelongDate()) && Objects.nonNull(classWorkInfoDTO.getBelongDate()),WorkInfo::getWorkTime, classWorkInfoDTO.getBelongDate() + " 00:00:00", classWorkInfoDTO.getBelongDate() + " 23:59:59")
                    .eq(WorkInfo::getClassesNo, classWorkInfoDTO.getClassesNo())
                    .eq(WorkInfo::getCourseId, classWorkInfoVO.getCourseId())
                    .eq(WorkInfo::getListenStatus, YesOrNoEnum.NO.getCode())
                    .in(WorkInfo::getUserId, userIds));
            for (WorkInfo workInfo : workInfos) {
                MemberListByCourseIdClassesNoTeamIdVO userInfo = userInfoVOMap.get(workInfo.getUserId());
                unListenUserName.add(userInfo.getUsername());
            }
            classWorkInfoVO.setUnListenUserName(String.join(",", unListenUserName));
        }
        classWorkInfoVO.setWorkModelFlag(courseInfo.getWorkModelFlag());
        List<WorkModelVO> workModelVOS = new ArrayList<>();
        if (Objects.nonNull(courseInfo.getWorkModelId())){
            WorkModelVO workModelVO = new WorkModelVO();
            workModelVO.setWorkModelId(courseInfo.getWorkModelId());
            workModelVO.setWorkModelName(courseInfo.getWorkModelName());
            workModelVOS.add(workModelVO);
        }
        if (Objects.nonNull(courseInfo.getOldUserWorkModelId())){
            WorkModelVO workModelVO = new WorkModelVO();
            workModelVO.setWorkModelId(courseInfo.getOldUserWorkModelId());
            workModelVO.setWorkModelName(courseInfo.getOldUserWorkModelName());
            workModelVOS.add(workModelVO);
        }
        classWorkInfoVO.setWorkModelList(workModelVOS);
        return classWorkInfoVO;
    }

    @Override
    public List<WorkModelDetailVO> selectTextInfo(Integer workModelId) {
        LambdaQueryWrapper<WorkModelDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WorkModelDetail::getWorkModelId, workModelId);
        List<WorkModelDetail> workModelDetails = workModelDetailMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(workModelDetails)) {
            return null;
        }
        List<WorkModelDetailVO> workModelDetailVOList = workModelDetails.stream().map(item -> {
            WorkModelDetailVO workModelDetailVO = new WorkModelDetailVO();
            BeanUtils.copyProperties(item, workModelDetailVO);
            return workModelDetailVO;
        }).collect(Collectors.toList());
        return workModelDetailVOList;
    }

    @Override
    @DataSource(DataSourceTypeEnum.SLAVE)
    public ResultInfo<PageInfoBT<ClassWorkStatisticsInfoVO>> selectClassWorkInfoList(ClassWorkStatisticsInfoDTO classWorkStatisticsInfoDTO) {
        // 1、分页查询固定字段信息
        IPage<ClassWorkStatisticsInfoVO> classWorkStatisticsListVOIPage = workInfoMapper.selectClassWorkInfoList(classWorkStatisticsInfoDTO.getPage(), classWorkStatisticsInfoDTO);
        List<ClassWorkStatisticsInfoVO> records = classWorkStatisticsListVOIPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return ResultInfo.ok(PageInfoBT.fromPage(classWorkStatisticsListVOIPage));
        }
        // 2、获取对应模板id  和  作业id
        Integer workModelId = records.get(0).getWorkModelId();
        if (Objects.isNull(workModelId)) {
            return ResultInfo.ok(PageInfoBT.fromPage(classWorkStatisticsListVOIPage));
        }
        // 3、根据模板id查询模板配置详情
        LambdaQueryWrapper<WorkModelDetail> workModelDetailQueryWrapper = new LambdaQueryWrapper<>();
        workModelDetailQueryWrapper.eq(WorkModelDetail::getWorkModelId, workModelId)
            .eq(WorkModelDetail::getDeleted, 1);
        List<WorkModelDetail> workModelDetails = workModelDetailMapper.selectList(workModelDetailQueryWrapper);
        if (CollectionUtils.isEmpty(workModelDetails)) {
            return ResultInfo.ok(PageInfoBT.fromPage(classWorkStatisticsListVOIPage));
        }
        // 4、过滤不符合展示条件的项
        List<WorkModelDetail> workModelDetailsOk = workModelDetails.stream().sorted(Comparator.comparing(WorkModelDetail::getSorted)).filter(item -> (item.getType().equals(WorkDetailTypeEnum.TEXT.getCode()) || item.getType().equals(WorkDetailTypeEnum.DUSING.getCode()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(workModelDetailsOk)) {
            return ResultInfo.ok(PageInfoBT.fromPage(classWorkStatisticsListVOIPage));
        }
        // 4.1 构建表头
        List<WorkExtraVO> tableHeadList = new ArrayList<>();
        int i = 1;
        for (WorkModelDetail workModelDetail : workModelDetailsOk) {
            tableHeadList.add(new WorkExtraVO("v" + i++, workModelDetail.getTitle()));
        }
        List<ClassWorkStatisticsInfoVO> result = new ArrayList<>();
        classWorkStatisticsListVOIPage = classWorkStatisticsListVOIPage.convert(record -> {
            String content = record.getContent();
            // 设置表头
            record.setTableHeader(tableHeadList);
            if (StringUtils.isEmpty(content)) {
                // 组装值
                Map<String, Object> tableDate = new HashMap<>();
                for (WorkExtraVO workExtraVO : tableHeadList) {
                    tableDate.put(workExtraVO.getKey(), "");
                }
                record.setTableData(tableDate);
                return record;
            }
            List<WorkInfoDetail> details = JSONObject.parseArray(content, WorkInfoDetail.class);
            // 设置值
            Map<String, Object> tableDate = new HashMap<>();
            for (WorkExtraVO workExtraVO : tableHeadList) {
                String key = workExtraVO.getKey();
                String value = workExtraVO.getValue();
                for (WorkInfoDetail detail : details) {
                    if (StringUtils.isNotEmpty(detail.getTitle()) && detail.getTitle().equals(value)) {
                        tableDate.put(key, detail.getContent());
                    }
                }
                if (Objects.isNull(tableDate.get(key))) {
                    tableDate.put(key, "");
                }
            }
            record.setTableData(tableDate);
            return record;
        });
        return ResultInfo.ok(PageInfoBT.fromPage(classWorkStatisticsListVOIPage));
    }

    @Override
    public void classWorkInfoListExport(ClassWorkStatisticsInfoDTO classWorkStatisticsInfoDTO, HttpServletResponse response) {

        ResultInfo result = selectClassWorkInfoList(classWorkStatisticsInfoDTO);
        PageInfoBT<ClassWorkStatisticsInfoVO> resultData = (PageInfoBT<ClassWorkStatisticsInfoVO>) result.getData();
        List<ClassWorkStatisticsInfoVO> workModelDetailList = resultData.getRecords();
        if (!CollectionUtils.isEmpty(workModelDetailList)) {
            ClassWorkStatisticsInfoVO classWorkStatisticsInfoVO = workModelDetailList.get(0);
            List<WorkExtraVO> tableHeader = classWorkStatisticsInfoVO.getTableHeader();
            // 组装表头
            List<ExcelExportEntity> colList = new ArrayList<ExcelExportEntity>();
            colList.add(new ExcelExportEntity("作业归属日期", "belongDate"));
            colList.add(new ExcelExportEntity("学员姓名", "userName"));
            colList.add(new ExcelExportEntity("学员手机号", "userPhone"));
            for (WorkExtraVO workExtraVO : tableHeader) {
                colList.add(new ExcelExportEntity(workExtraVO.getValue(), workExtraVO.getKey()));
            }
            colList.add(new ExcelExportEntity("当日合计得分", "dayTotalScore"));
            colList.add(new ExcelExportEntity("作业累计得总分", "totalScore"));
            colList.add(new ExcelExportEntity("作业提交时间", "workTime"));

            List<Map<String, Object>> exportList = new ArrayList<Map<String, Object>>();
            for (ClassWorkStatisticsInfoVO record : workModelDetailList) {
                Map<String, Object> tableData = record.getTableData();
                Map<String, Object> valMap = new HashMap<String, Object>();
                valMap.put("belongDate", record.getBelongDate());
                valMap.put("userName", record.getUserName());
                valMap.put("userPhone", record.getUserPhone());
                for (WorkExtraVO workExtraVO : tableHeader) {
                    valMap.put(workExtraVO.getKey(), tableData.get(workExtraVO.getKey()));
                }
                valMap.put("dayTotalScore", record.getDayTotalScore());
                valMap.put("totalScore", record.getTotalScore());
                valMap.put("workTime", new DateTime(record.getWorkTime()).toString("yyyy-MM-dd HH:mm:ss"));
                exportList.add(valMap);
            }
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("班级作业详情", "班级作业详情"), colList, exportList);
            try {
                response.setCharacterEncoding("UTF-8");
                response.setHeader("content-Type", "application/vnd.ms-excel");
                response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("班级作业详情.xls", "UTF-8"));
                workbook.write(response.getOutputStream());
            } catch (IOException var4) {
                throw new BusinessException(var4.getMessage());
            }
        }
    }

    @Override
    public List<CxktResDateListVO> queryCxktResDataByCourseId(Integer courseId) {
        CxktResDateQueryListDTO cxktResDateQueryListDTO = new CxktResDateQueryListDTO();
        cxktResDateQueryListDTO.setCourseId(courseId);
        ResultInfo<List<CxktResDateListVO>> listResultInfo = userFeignClient.queryCxktResDataByCourseId(cxktResDateQueryListDTO);
        List<CxktResDateListVO> data = listResultInfo.getData();
        if (CollectionUtils.isEmpty(data)) {
            List<CxktResDateListVO> list = new ArrayList<>();
            return list;
        }
        List<CxktResDateListVO> result = data.stream().map(item -> {
            CxktResDateListVO cxktResDateListVO = new CxktResDateListVO();
            cxktResDateListVO.setId(item.getId());
            cxktResDateListVO.setResTitle(item.getResTitle());
            return cxktResDateListVO;
        }).collect(Collectors.toList());
        return result;
    }
}
