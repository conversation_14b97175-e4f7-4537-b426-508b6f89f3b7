package com.sibuqu.work.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sibuqu.work.entity.WorkOldUserInfo;
import com.sibuqu.work.mapper.WorkOldUserInfoMapper;
import com.sibuqu.work.service.WorkOldUserInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("WorkOldUserInfoService")
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class WorkOldUserInfoServiceImpl extends ServiceImpl<WorkOldUserInfoMapper, WorkOldUserInfo> implements WorkOldUserInfoService {
    @Override
    public boolean checkUserIsOld(Integer userId) {
        return this.getOne(new LambdaQueryWrapper<WorkOldUserInfo>().eq(WorkOldUserInfo::getUserId, userId)
                .last("limit 1")) != null;
    }
}
