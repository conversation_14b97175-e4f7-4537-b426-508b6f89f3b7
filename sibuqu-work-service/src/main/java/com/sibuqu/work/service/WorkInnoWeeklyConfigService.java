package com.sibuqu.work.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sibuqu.work.dto.AddInnoWeeklyConfigDTO;
import com.sibuqu.work.dto.QueryInnoWeeklyConfigDTO;
import com.sibuqu.work.entity.WorkInnoWeeklyConfig;
import com.sibuqu.work.vo.InnoWeeklyConfigVO;

import java.util.List;

public interface WorkInnoWeeklyConfigService extends IService<WorkInnoWeeklyConfig> {

    /**
     * 新增inno周作业配置
     * 
     * @param dto 新增配置参数
     * @return 配置ID
     */
    Long addInnoWeeklyConfig(AddInnoWeeklyConfigDTO dto);

    /**
     * 查询inno周作业配置详情
     * 
     * @param dto 查询参数
     * @return 配置详情
     */
    InnoWeeklyConfigVO queryInnoWeeklyConfig(QueryInnoWeeklyConfigDTO dto);

    /**
     * 管理后台查询inno周作业配置列表
     * 
     * @return 配置列表
     */
    List<InnoWeeklyConfigVO> queryInnoWeeklyConfigList();

}
