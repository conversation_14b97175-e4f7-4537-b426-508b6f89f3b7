package com.sibuqu.work.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.work.dto.EditInnoWeeklyInfoDTO;
import com.sibuqu.work.dto.QueryByCourseIdAndTimeDTO;
import com.sibuqu.work.dto.QueryInnoWeeklyInfoDTO;
import com.sibuqu.work.dto.QueryInnoWeeklyInfoDetailDTO;
import com.sibuqu.work.dto.QueryInnoWeeklyInfoListDTO;
import com.sibuqu.work.dto.QueryInnoWeeklyInfoPageDTO;
import com.sibuqu.work.dto.QueryInnoWeeklyInfoSelectDTO;
import com.sibuqu.work.entity.WorkInnoWeeklyConfig;
import com.sibuqu.work.entity.WorkInnoWeeklyInfo;
import com.sibuqu.work.vo.InnoWeeklyInfoDetailVO;
import com.sibuqu.work.vo.InnoWeeklyInfoPageVO;
import com.sibuqu.work.vo.InnoWeeklyInfoSelectVO;
import com.sibuqu.work.vo.InnoWeeklyInfoVO;
import com.sibuqu.work.vo.QueryInnoWeeklyInfoByIdListVO;
import com.sibuqu.work.vo.QueryInnoWeeklyInfoListVO;

import java.util.List;
import java.util.Map;

public interface WorkInnoWeeklyInfoService extends IService<WorkInnoWeeklyInfo> {

    List<QueryInnoWeeklyInfoListVO> queryInnoWeeklyInfoList(QueryInnoWeeklyInfoListDTO dto);

    /**
     * 查询周作业单条信息
     * 
     * @param dto 查询参数
     * @return 周作业信息
     */
    InnoWeeklyInfoVO queryInnoWeeklyInfo(QueryInnoWeeklyInfoDTO dto);

    /**
     * 管理后台分页查询inno周作业信息
     * 
     * @param dto 查询参数
     * @return 分页结果
     */
    PageInfoBT<InnoWeeklyInfoPageVO> queryInnoWeeklyInfoPage(QueryInnoWeeklyInfoPageDTO dto);

    /**
     * 管理后台查询inno周作业下拉列表
     * 
     * @param dto 查询参数
     * @return 下拉选项列表
     */
    List<InnoWeeklyInfoSelectVO> queryInnoWeeklyInfoSelect(QueryInnoWeeklyInfoSelectDTO dto);

    /**
     * 查询inno周作业详情
     * 
     * @param dto 查询参数
     * @return 周作业详情
     */
    InnoWeeklyInfoDetailVO queryInnoWeeklyInfoDetail(QueryInnoWeeklyInfoDetailDTO dto);

    /**
     * 编辑inno周作业信息
     * 
     * @param dto 编辑参数
     * @return 是否成功
     */
    Boolean editInnoWeeklyInfo(EditInnoWeeklyInfoDTO dto);

    /**
     * 根据周作业配置信息生成多个周作业信息
     *
     * @param config 周作业配置信息
     */
    void generateWeeklyInfo(WorkInnoWeeklyConfig config);

    Map<Long, QueryInnoWeeklyInfoByIdListVO> queryInnoWeeklyInfoByIdList(List<Long> idList);

    List<InnoWeeklyInfoVO> queryByCourseIdAndTime(QueryByCourseIdAndTimeDTO dto);

    List<InnoWeeklyInfoVO> queryWeeklyInfoByCourseId(Integer courseId);

    QueryInnoWeeklyInfoListVO queryCurrentTimeWeeklyInfo(Integer userId, Integer courseId);
}
