package com.sibuqu.work.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.inno.user.bo.UserPointsAddBO;
import com.inno.user.enums.PointsTypeEnum;
import com.sibuqu.base.common.enums.IsDeleteEnum;
import com.sibuqu.base.common.exception.BusinessException;
import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.base.common.result.HeaderUserInfo;
import com.sibuqu.base.common.utils.FieldUtil;
import com.sibuqu.bidata.dto.UserDoneActionInfoDTO;
import com.sibuqu.course.entity.CourseTimetable;
import com.sibuqu.course.vo.goodsinfo.InnoCourseGoodsDetailVO;
import com.sibuqu.starter.redis.headeruserinfo.HeaderUserInfoUtil;
import com.sibuqu.user.vo.user.UserInfoSearchSimpleVO;
import com.sibuqu.user.vo.user.UserInfoSearchSimpleVO;
import com.sibuqu.work.componet.BaseUserComponent;
import com.sibuqu.work.componet.BidataComponent;
import com.sibuqu.work.componet.ClassesComponent;
import com.sibuqu.work.componet.CourseComponent;
import com.sibuqu.work.componet.UserComponent;
import com.sibuqu.work.dto.AdminQueryInnoWorkPageDTO;
import com.sibuqu.work.dto.BatchQueryInnoCttStatusDTO;
import com.sibuqu.work.dto.InnoWorkRecommendDTO;
import com.sibuqu.work.dto.QueryInnoInfoByCourseIdAndCttIdDTO;
import com.sibuqu.work.dto.QueryInnoPosterPageDTO;
import com.sibuqu.work.dto.QueryInnoWorkDTO;
import com.sibuqu.work.dto.WriteInnoWorkDTO;
import com.sibuqu.work.entity.WorkInnoInfo;
import com.sibuqu.work.kafka.production.KafkaProduction;
import com.sibuqu.work.mapper.WorkInnoInfoMapper;
import com.sibuqu.work.service.WorkInnoInfoService;
import com.sibuqu.work.vo.AdminInnoWorkPageVO;
import com.sibuqu.work.vo.InnoPosterPageVO;
import com.sibuqu.work.vo.RecommendWorkItemDTO;
import com.sibuqu.work.vo.WorkInnoInfoVO;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WorkInnoInfoServiceImpl extends ServiceImpl<WorkInnoInfoMapper, WorkInnoInfo>
        implements WorkInnoInfoService {

    @Autowired
    private CourseComponent courseComponent;

    @Autowired
    private BaseUserComponent baseUserComponent;

    @Autowired
    private UserComponent userComponent;

    @Autowired
    private ClassesComponent classesComponent;

    @Autowired
    private KafkaProduction kafkaProduction;
    @Autowired
    private BidataComponent bidataComponent;

    @Override
    public Long writeInnoWork(WriteInnoWorkDTO dto) {
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        log.info("用户{}开始写inno作业, 课程id: {}, 课件id: {}, 类型: {}",
                headerUserInfo.getId(), dto.getCourseId(), dto.getCourseTimetableId(), dto.getType());

        // 查询是否已经提交过作业
        LambdaQueryWrapper<WorkInnoInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WorkInnoInfo::getUserId, headerUserInfo.getId())
                .eq(WorkInnoInfo::getCourseId, dto.getCourseId())
                .eq(WorkInnoInfo::getCourseTimetableId, dto.getCourseTimetableId())
                .eq(WorkInnoInfo::getDeleteFlag, 0)
                .last("limit 1");

        WorkInnoInfo existingWork = this.getOne(queryWrapper);
        boolean isNewWork = Objects.isNull(existingWork);

        if (isNewWork) {
            existingWork = new WorkInnoInfo();
            // 设置基本字段
            existingWork.setCourseId(dto.getCourseId());
            existingWork.setCourseTimetableId(dto.getCourseTimetableId());
            existingWork.setUserId(headerUserInfo.getId());
            existingWork.setDeleteFlag(0);
        }

        // 根据类型更新相应字段
        switch (dto.getType()) {
            case 1:
                if (!(dto.getUserPoints() >= 1 && dto.getUserPoints() <= 5)) {
                    throw new BusinessException("能量果错误");
                }
                existingWork.setUserPoints(dto.getUserPoints());
                break;
            case 2:
                existingWork.setContent(dto.getContent());
                break;
            case 3:
                existingWork.setPoster(dto.getPoster());
                break;
            case 4:
                existingWork.setContent(dto.getContent());
                existingWork.setPoster(dto.getPoster());
                break;
            default:
                log.warn("无效的作业类型: {}", dto.getType());
                break;
        }

        // 保存或更新
        if (isNewWork) {
            FieldUtil.setCreateAndUpdate(existingWork, headerUserInfo, LocalDateTime.now());
            this.save(existingWork);
            log.info("新建inno作业成功, 作业id: {}", existingWork.getId());
            if (Objects.equals(dto.getType(), 1)) {
                UserPointsAddBO userPointsAddBO = new UserPointsAddBO();
                userPointsAddBO.setUserId(headerUserInfo.getId());
                userPointsAddBO.setCourseId(dto.getCourseId());
                userPointsAddBO.setSceneName("提交浅作业");
                userPointsAddBO.setRequestId(UUID.randomUUID().toString());
                userPointsAddBO.setScore(dto.getUserPoints().intValue());
                userPointsAddBO.setPointsTypeEnum(PointsTypeEnum.SUBMIT_SHALLOW_HOMEWORK);
                kafkaProduction.send(userPointsAddBO);
            }
        } else {
            FieldUtil.setUpdate(existingWork, headerUserInfo, LocalDateTime.now());
            this.updateById(existingWork);
            log.info("更新inno作业成功, 作业id: {}", existingWork.getId());
            try{
                InnoCourseGoodsDetailVO innoCourseGoodsDetailVO = courseComponent.innoCourseInfo();
                if (existingWork.getCourseId().equals(innoCourseGoodsDetailVO.getCourseId())){
                    //判断是否有海报
                    UserDoneActionInfoDTO userDoneActionInfoDTO = new UserDoneActionInfoDTO();
                    userDoneActionInfoDTO.setUserId(existingWork.getUserId());
                    userDoneActionInfoDTO.setCourseId(existingWork.getCourseId());
                    userDoneActionInfoDTO.setDataId(existingWork.getCourseTimetableId());
                    userDoneActionInfoDTO.setDataType(1);
                    bidataComponent.updateDoneAction(userDoneActionInfoDTO);
                }

            } catch (Exception e) {
                log.error("更新海报异常：{}", e);
            }
        }

        return existingWork.getId();
    }

    @Override
    public WorkInnoInfoVO queryInnoWork(QueryInnoWorkDTO dto) {
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        log.info("用户{}查询inno作业, 课程id: {}, 课件id: {}",
                headerUserInfo.getId(), dto.getCourseId(), dto.getCourseTimetableId());

        // 查询作业信息
        LambdaQueryWrapper<WorkInnoInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WorkInnoInfo::getUserId, headerUserInfo.getId())
                .eq(WorkInnoInfo::getCourseId, dto.getCourseId())
                .eq(WorkInnoInfo::getCourseTimetableId, dto.getCourseTimetableId())
                .eq(WorkInnoInfo::getDeleteFlag, 0)
                .last("limit 1");

        WorkInnoInfo workInnoInfo = this.getOne(queryWrapper);
        CourseTimetable courseTimetableDetail = courseComponent.getCourseTimetableDetail(dto.getCourseTimetableId());
        WorkInnoInfoVO workInnoInfoVO = new WorkInnoInfoVO();
        workInnoInfoVO.setCourseId(dto.getCourseId());
        workInnoInfoVO.setCourseTimetableId(dto.getCourseTimetableId());
        if (Objects.nonNull(courseTimetableDetail)) {
            workInnoInfoVO.setCourseTimetableName("《" + courseTimetableDetail.getResourceTitle() + "》");
        }
        if (Objects.isNull(workInnoInfo)) {
            log.info("用户{}暂无该课件的作业记录", headerUserInfo.getId());
            return workInnoInfoVO;
        }

        log.info("查询到用户{}的作业记录, 作业id: {}", headerUserInfo.getId(), workInnoInfo.getId());
        BeanUtil.copyProperties(workInnoInfo, workInnoInfoVO);

        // 查询推荐的作业内容
        List<RecommendWorkItemDTO> recommendWorkList = queryRecommendWorkList(dto.getCourseId(), dto.getCourseTimetableId());
        workInnoInfoVO.setRecommendWorkList(recommendWorkList);

        return workInnoInfoVO;
    }

    @Override
    public Map<Integer, Boolean> batchQueryInnoCttStatus(BatchQueryInnoCttStatusDTO dto) {
        log.info("批量查询用户{}的课件作业状态, 课件数量: {}",
                dto.getUserId(),
                CollUtil.isEmpty(dto.getCourseTimetableIds()) ? 0 : dto.getCourseTimetableIds().size());

        if (CollUtil.isEmpty(dto.getCourseTimetableIds())) {
            log.warn("课件id列表为空, 返回空结果");
            return Collections.emptyMap();
        }
        return this.list(new LambdaQueryWrapper<WorkInnoInfo>()
                        .in(WorkInnoInfo::getCourseTimetableId, dto.getCourseTimetableIds())
                        .eq(WorkInnoInfo::getUserId, dto.getUserId())
                        .eq(WorkInnoInfo::getDeleteFlag, 0))
                .stream().collect(Collectors.toMap(WorkInnoInfo::getCourseTimetableId,
                        i -> Objects.nonNull(i.getUserPoints()) && i.getUserPoints() > 0));
    }

    @Override
    public Integer queryInnoInfoByCourseIdAndCttId(QueryInnoInfoByCourseIdAndCttIdDTO dto) {
        if (CollUtil.isEmpty(dto.getUserIdList())) {
            return 0;
        }
        return baseMapper.countByCourseIdAndCttId(dto.getCourseId(), dto.getCourseTimetableId(), dto.getUserIdList());
    }

    @Override
    public Map<Integer, Long> queryByCourseIdAndCttIdList(Integer courseId, List<Integer> cttIdList, List<Integer> userIdList) {
        if (CollUtil.isEmpty(cttIdList) || CollUtil.isEmpty(userIdList)) {
            return Collections.emptyMap();
        }
        return this.list(new LambdaQueryWrapper<WorkInnoInfo>()
                .in(WorkInnoInfo::getCourseId, courseId)
                .in(WorkInnoInfo::getCourseTimetableId, cttIdList)
                .in(WorkInnoInfo::getUserId, userIdList)
                .eq(WorkInnoInfo::getDeleteFlag, 0))
                .stream().collect(Collectors.groupingBy(WorkInnoInfo::getUserId,
                        Collectors.summingLong(WorkInnoInfo::getUserPoints)));
    }

    @Override
    public PageInfoBT<InnoPosterPageVO> queryInnoPosterPage(QueryInnoPosterPageDTO dto) {

        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();

        // 构建查询条件
        LambdaQueryWrapper<WorkInnoInfo> queryWrapper = new LambdaQueryWrapper<WorkInnoInfo>()
                .eq(WorkInnoInfo::getCourseId, dto.getCourseId())
                .eq(WorkInnoInfo::getUserId, headerUserInfo.getId())
                .eq(WorkInnoInfo::getDeleteFlag, IsDeleteEnum.NO.getCode())
                .isNotNull(WorkInnoInfo::getPoster)
                .ne(WorkInnoInfo::getPoster, "")
                .orderByDesc(WorkInnoInfo::getCreateTime);

        // 分页查询
        IPage<WorkInnoInfo> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        IPage<WorkInnoInfo> resultPage = this.page(page, queryWrapper);

        if (CollUtil.isEmpty(resultPage.getRecords())) {
            return PageInfoBT.noData();
        }

        // 转换为VO
        List<InnoPosterPageVO> voList = resultPage.getRecords().stream().map(entity -> {
            InnoPosterPageVO vo = new InnoPosterPageVO();
            BeanUtil.copyProperties(entity, vo);
            return vo;
        }).collect(Collectors.toList());

        // 构建分页结果
        PageInfoBT<InnoPosterPageVO> pageResult = new PageInfoBT<>();
        pageResult.setCurrent(resultPage.getCurrent());
        pageResult.setSize(resultPage.getSize());
        pageResult.setTotal(resultPage.getTotal());
        pageResult.setRecords(voList);

        return pageResult;
    }

    @Override
    public PageInfoBT<AdminInnoWorkPageVO> adminQueryInnoWorkPage(AdminQueryInnoWorkPageDTO dto) {
        log.info("管理后台分页查询inno作业, 课程id: {}, 页码: {}, 每页条数: {}, 推荐状态: {}",
                dto.getCourseId(), dto.getPageNum(), dto.getPageSize(), dto.getRecommendFlag());

        // 构建查询条件
        LambdaQueryWrapper<WorkInnoInfo> queryWrapper = new LambdaQueryWrapper<WorkInnoInfo>()
                .eq(WorkInnoInfo::getCourseId, dto.getCourseId())
                .eq(WorkInnoInfo::getDeleteFlag, IsDeleteEnum.NO.getCode());

        // 添加推荐状态过滤
        if (Objects.nonNull(dto.getRecommendFlag())) {
            queryWrapper.eq(WorkInnoInfo::getRecommendFlag, dto.getRecommendFlag());
        }

        // 添加课件id过滤
        if (Objects.nonNull(dto.getCourseTimetableId())) {
            queryWrapper.eq(WorkInnoInfo::getCourseTimetableId, dto.getCourseTimetableId());
        }

        // 字数大于
        if (Objects.nonNull(dto.getLengthGt())) {
            queryWrapper.apply("length(content) > {0}", dto.getLengthGt());
        }

        // 学员身份 1-学员 2-组长 3-团队负责人
        if (Objects.nonNull(dto.getUserQueryRole())) {
            if (Objects.equals(dto.getUserQueryRole(), 1)) {
                List<Integer> allLeaderId = classesComponent.getAllLeaderId(dto.getCourseId());
                if (CollUtil.isNotEmpty(allLeaderId)) {
                    queryWrapper.notIn(WorkInnoInfo::getUserId, allLeaderId);
                }
            } else if (Objects.equals(dto.getUserQueryRole(), 2)) {
                List<Integer> allLeaderId = classesComponent.getAllLeaderId(dto.getCourseId());
                if (CollUtil.isNotEmpty(allLeaderId)) {
                    queryWrapper.in(WorkInnoInfo::getUserId, allLeaderId);
                }
            } else if (Objects.equals(dto.getUserQueryRole(), 3)) {
                List<Integer> allTeamLeaderId = classesComponent.getAllTeamLeaderId(dto.getCourseId());
                if (CollUtil.isNotEmpty(allTeamLeaderId)) {
                    queryWrapper.in(WorkInnoInfo::getUserId, allTeamLeaderId);
                }
            }

        }

        // 根据用户姓名和手机号查询userId列表
        List<Integer> userIdList = new ArrayList<>();

        // 根据用户姓名查询userId列表
        if (StrUtil.isNotBlank(dto.getUserName())) {
            List<Integer> userIdsByName = baseUserComponent.getUserIdListByName(dto.getUserName());
            if (CollUtil.isNotEmpty(userIdsByName)) {
                userIdList.addAll(userIdsByName);
            }
        }

        // 根据手机号查询userId
        if (StrUtil.isNotBlank(dto.getUserPhone())) {
            Integer userIdByPhone = baseUserComponent.getUserIdByPhone(dto.getUserPhone());
            if (Objects.nonNull(userIdByPhone)) {
                userIdList.add(userIdByPhone);
            }
        }

        // 如果有用户条件但没有查到用户，直接返回空结果
        if ((StrUtil.isNotBlank(dto.getUserName()) || StrUtil.isNotBlank(dto.getUserPhone()))
            && CollUtil.isEmpty(userIdList)) {
            log.info("根据用户姓名或手机号未查询到用户，返回空结果");
            return PageInfoBT.noData();
        }

        // 添加用户id过滤
        if (CollUtil.isEmpty(userIdList)
                && (StrUtil.isNotBlank(dto.getUserName()) || StrUtil.isNotBlank(dto.getUserPhone()))) {
            return PageInfoBT.noData();
        }
        if (CollUtil.isNotEmpty(userIdList)) {
            queryWrapper.in(WorkInnoInfo::getUserId, userIdList);
        }

        queryWrapper.orderByDesc(WorkInnoInfo::getCreateTime);

        // 分页查询
        IPage<WorkInnoInfo> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        IPage<WorkInnoInfo> resultPage = this.page(page, queryWrapper);

        if (CollUtil.isEmpty(resultPage.getRecords())) {
            return PageInfoBT.noData();
        }

        // 获取用户ID列表，用于批量查询用户信息
        List<Integer> userIds = resultPage.getRecords().stream()
                .map(WorkInnoInfo::getUserId)
                .distinct()
                .collect(Collectors.toList());

        // 批量查询用户信息
        Map<Integer, UserInfoSearchSimpleVO> userInfoMap = new HashMap<>();
        if (CollUtil.isNotEmpty(userIds)) {
            try {
                List<UserInfoSearchSimpleVO> userInfoList = userComponent.userSimpleListByUserIds(userIds, -1);
                if (CollUtil.isNotEmpty(userInfoList)) {
                    userInfoMap = userInfoList.stream()
                            .collect(Collectors.toMap(UserInfoSearchSimpleVO::getUserId, Function.identity()));
                }
            } catch (Exception e) {
                log.warn("批量查询用户信息失败", e);
            }
        }

        final Map<Integer, UserInfoSearchSimpleVO> finalUserInfoMap = userInfoMap;

        // 转换为VO
        List<AdminInnoWorkPageVO> voList = resultPage.getRecords().stream().map(entity -> {
            AdminInnoWorkPageVO vo = new AdminInnoWorkPageVO();
            BeanUtil.copyProperties(entity, vo);

            // 查询课件名称
            if (Objects.nonNull(entity.getCourseTimetableId())) {
                try {
                    CourseTimetable courseTimetable = courseComponent.getCourseTimetableDetail(entity.getCourseTimetableId());
                    if (Objects.nonNull(courseTimetable)) {
                        vo.setCourseTimetableName("《" + courseTimetable.getResourceTitle() + "》");
                    }
                } catch (Exception e) {
                    log.warn("查询课件名称失败, 课件id: {}", entity.getCourseTimetableId(), e);
                }
            }

            // 设置用户信息
            UserInfoSearchSimpleVO userInfo = finalUserInfoMap.get(entity.getUserId());
            if (Objects.nonNull(userInfo)) {
                vo.setUserName(userInfo.getUserFullName());
                vo.setUserPhone(userInfo.getPhone());
            }

            return vo;
        }).collect(Collectors.toList());

        // 构建分页结果
        PageInfoBT<AdminInnoWorkPageVO> pageResult = new PageInfoBT<>();
        pageResult.setCurrent(resultPage.getCurrent());
        pageResult.setSize(resultPage.getSize());
        pageResult.setTotal(resultPage.getTotal());
        pageResult.setRecords(voList);

        log.info("管理后台分页查询inno作业完成, 总记录数: {}, 当前页记录数: {}",
                resultPage.getTotal(), voList.size());
        return pageResult;
    }

    @Override
    public Boolean adminRecommendInnoWork(InnoWorkRecommendDTO dto) {
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        log.info("管理后台推荐/取消推荐inno作业, 操作人: {}, 作业id: {}, 推荐标识: {}",
                headerUserInfo.getId(), dto.getId(), dto.getRecommendFlag());

        WorkInnoInfo workInnoInfo = this.getById(dto.getId());
        if (Objects.isNull(workInnoInfo)) {
            throw new BusinessException("作业不存在");
        }

        LocalDateTime now = LocalDateTime.now();

        // 设置推荐状态
        workInnoInfo.setRecommendFlag(dto.getRecommendFlag());

        if (dto.getRecommendFlag() == 1) {
            // 推荐
            workInnoInfo.setRecommendTime(now);
        } else {
            // 取消推荐，将推荐状态设为历史推荐
            if (Objects.nonNull(workInnoInfo.getRecommendTime())) {
                workInnoInfo.setRecommendFlag(2); // 2-历史推荐
            }
        }

        // 设置更新信息
        FieldUtil.setUpdate(workInnoInfo, headerUserInfo, now);

        boolean result = this.updateById(workInnoInfo);

        log.info("管理后台推荐/取消推荐inno作业完成, 作业id: {}, 结果: {}", dto.getId(), result);
        return result;
    }

    /**
     * 查询推荐的作业内容列表
     *
     * @param courseId 课程id
     * @param courseTimetableId 课件id
     * @return 推荐作业内容列表
     */
    private List<RecommendWorkItemDTO> queryRecommendWorkList(Integer courseId, Integer courseTimetableId) {
        // 查询同课程同课件下推荐的作业
        LambdaQueryWrapper<WorkInnoInfo> queryWrapper = new LambdaQueryWrapper<WorkInnoInfo>()
                .eq(WorkInnoInfo::getCourseId, courseId)
                .eq(WorkInnoInfo::getCourseTimetableId, courseTimetableId)
                .eq(WorkInnoInfo::getRecommendFlag, 1) // 推荐状态
                .eq(WorkInnoInfo::getDeleteFlag, IsDeleteEnum.NO.getCode())
                .isNotNull(WorkInnoInfo::getContent)
                .ne(WorkInnoInfo::getContent, "")
                .orderByDesc(WorkInnoInfo::getRecommendTime)
                .last("limit 3");

        List<WorkInnoInfo> recommendWorkList = this.list(queryWrapper);

        if (CollUtil.isEmpty(recommendWorkList)) {
            return Collections.emptyList();
        }

        return recommendWorkList.stream().map(work -> {
                    RecommendWorkItemDTO item = new RecommendWorkItemDTO();
                    item.setUserId(work.getUserId());
                    item.setUserName(work.getCreateUserName());
                    item.setContent(work.getContent());
                    return item;
                })
                .sorted(Comparator.comparingInt(o -> o.getContent().length()))
                .collect(Collectors.toList());
    }
}
