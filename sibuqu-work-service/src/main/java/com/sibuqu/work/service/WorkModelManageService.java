package com.sibuqu.work.service;

import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.base.common.result.HeaderUserInfo;
import com.sibuqu.work.dto.*;
import com.sibuqu.work.vo.AudioConfigVO;
import com.sibuqu.work.vo.admin.ModelDetailInfoVO;
import com.sibuqu.work.vo.admin.ModelDetailVO;
import com.sibuqu.work.vo.admin.WorkModelDetailVO;
import com.sibuqu.work.vo.admin.WorkModelInfoVO;
import com.sibuqu.work.vo.admin.WorkModelListVO;

import java.util.List;

public interface WorkModelManageService {
    PageInfoBT<WorkModelListVO> list(WorkModelListDTO workModelListDTO);

    WorkModelInfoVO info(Integer workModelId);

    List<ModelDetailVO> modelDetail(Integer workModelId);

    void addModel(WorkModelAddDTO workModelAddDTO, HeaderUserInfo headerUserInfo);

    void update(WorkModelUpdateDTO workModelUpdateDTO, HeaderUserInfo headerUserInfo);

    void addModelDetail(WorkModelDetailAddDTO workModelDetailDTO, HeaderUserInfo headerUserInfo);

    void updateModelDetail(WorkModelDetailUpdateDTO workModelDetailUpdateDTO, HeaderUserInfo headerUserInfo);

    ModelDetailInfoVO modelDetailInfo(Integer modelDetailId, HeaderUserInfo headerUserInfo);

    void deleteModelDetail(DeleteModelDetailDTO deleteModelDetailDTO, HeaderUserInfo headerUserInfo);

    AudioConfigVO audioConfig();
}
