package com.sibuqu.work.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sibuqu.base.common.enums.IsDeleteEnum;
import com.sibuqu.base.common.exception.BusinessException;
import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.base.common.result.HeaderUserInfo;
import com.sibuqu.base.common.utils.BeanUtil;
import com.sibuqu.base.common.utils.FieldUtil;
import com.sibuqu.common.vo.content.GetContentListByDataIdSubDataIdVO;
import com.sibuqu.work.componet.CommentComponent;
import com.sibuqu.work.componet.ClassesComponent;
import com.sibuqu.starter.redis.headeruserinfo.HeaderUserInfoUtil;
import com.sibuqu.work.componet.CourseComponent;
import com.sibuqu.work.dto.EditInnoWeeklyInfoDTO;
import com.sibuqu.work.dto.QueryByCourseIdAndTimeDTO;
import com.sibuqu.work.dto.QueryInnoWeeklyInfoDTO;
import com.sibuqu.work.dto.QueryInnoWeeklyInfoDetailDTO;
import com.sibuqu.work.dto.QueryInnoWeeklyInfoListDTO;
import com.sibuqu.work.dto.QueryInnoWeeklyInfoPageDTO;
import com.sibuqu.work.dto.QueryInnoWeeklyInfoSelectDTO;
import com.sibuqu.work.entity.WorkInnoWeeklyConfig;
import com.sibuqu.work.entity.WorkInnoWeeklyInfo;
import com.sibuqu.work.mapper.WorkInnoWeeklyInfoMapper;
import com.sibuqu.work.service.WorkInnoWeeklyInfoService;
import com.sibuqu.work.bo.ContentBO;
import com.sibuqu.work.vo.InnoWeeklyInfoDetailVO;
import com.sibuqu.work.vo.InnoWeeklyInfoPageVO;
import com.sibuqu.work.vo.InnoWeeklyInfoSelectVO;
import com.sibuqu.work.vo.InnoWeeklyInfoVO;
import com.sibuqu.work.vo.QueryInnoWeeklyInfoByIdListVO;
import com.sibuqu.work.vo.QueryInnoWeeklyInfoListVO;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WorkInnoWeeklyInfoServiceImpl extends ServiceImpl<WorkInnoWeeklyInfoMapper, WorkInnoWeeklyInfo>
        implements WorkInnoWeeklyInfoService {

    @Autowired
    private CommentComponent commentComponent;

    @Autowired
    private ClassesComponent classesComponent;

    @Autowired
    private CourseComponent courseComponent;

    @Override
    public List<QueryInnoWeeklyInfoListVO> queryInnoWeeklyInfoList(QueryInnoWeeklyInfoListDTO dto) {
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        Map<Long, GetContentListByDataIdSubDataIdVO> contentMap = commentComponent
                .getContentListByDataIdSubDataId(4, dto.getCourseId().longValue(), null, headerUserInfo.getId())
                .stream().collect(Collectors.toMap(GetContentListByDataIdSubDataIdVO::getSubDataId, Function.identity(),
                        (k1, k2) -> k1));
        List<WorkInnoWeeklyInfo> list = this.list(new LambdaQueryWrapper<WorkInnoWeeklyInfo>()
                .eq(WorkInnoWeeklyInfo::getCourseId, dto.getCourseId())
                .eq(Objects.nonNull(dto.getCoursewareGroupId()), WorkInnoWeeklyInfo::getCoursewareGroupId, dto.getCoursewareGroupId())
                // 开始时间小于等于当前时间
                .le(WorkInnoWeeklyInfo::getWorkBeginDatetime, LocalDateTime.now())
                .eq(WorkInnoWeeklyInfo::getDeleteFlag, 0)
                .orderByDesc(WorkInnoWeeklyInfo::getWorkBeginDatetime));
        LocalDate now = LocalDate.now();
        LocalDate startOfWeek = now.with(DayOfWeek.MONDAY);
        return list.stream().map(workInnoWeeklyInfo -> {
            QueryInnoWeeklyInfoListVO vo = new QueryInnoWeeklyInfoListVO();
            BeanUtils.copyProperties(workInnoWeeklyInfo, vo);
            GetContentListByDataIdSubDataIdVO getContentListByDataIdSubDataIdVO = contentMap.get(workInnoWeeklyInfo.getId());
            if (Objects.nonNull(getContentListByDataIdSubDataIdVO)) {
                vo.setCompleteFlag(true);
                vo.setContentId(getContentListByDataIdSubDataIdVO.getId());
            }
            // weeklyBeginDate 是否是当前周
            vo.setCurrentWeekFlag(workInnoWeeklyInfo.getWeeklyBeginDate().with(DayOfWeek.MONDAY).isEqual(startOfWeek));
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public InnoWeeklyInfoVO queryInnoWeeklyInfo(QueryInnoWeeklyInfoDTO dto) {
        WorkInnoWeeklyInfo weeklyInfo = this.getById(dto.getId());
        if (Objects.isNull(weeklyInfo) || !IsDeleteEnum.NO.getCode().equals(weeklyInfo.getDeleteFlag())) {
            return null;
        }

        InnoWeeklyInfoVO vo = BeanUtil.copyProperties(weeklyInfo, InnoWeeklyInfoVO.class);
        vo.setWorkEndDatetimeDiff(Duration.between(LocalDateTime.now(), weeklyInfo.getWorkEndDatetime()).getSeconds());
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        List<GetContentListByDataIdSubDataIdVO> contentList = commentComponent.getContentListByDataIdSubDataId(4,
                weeklyInfo.getCourseId().longValue(), weeklyInfo.getId(), headerUserInfo.getId());
        if (CollUtil.isNotEmpty(contentList)) {
            vo.setContentList(contentList.get(0).getContentList().stream().map(content -> {
                ContentBO contentBO = new ContentBO();
                BeanUtil.copyProperties(content, contentBO);
                return contentBO;
            }).collect(Collectors.toList()));
        }
        vo.setTribeFlag(classesComponent.isInTribe(headerUserInfo.getId(), weeklyInfo.getCourseId()));
        return vo;
    }

    @Override
    public PageInfoBT<InnoWeeklyInfoPageVO> queryInnoWeeklyInfoPage(QueryInnoWeeklyInfoPageDTO dto) {
        // 构建查询条件
        LambdaQueryWrapper<WorkInnoWeeklyInfo> queryWrapper = new LambdaQueryWrapper<WorkInnoWeeklyInfo>()
                .eq(WorkInnoWeeklyInfo::getWeeklyConfigId, dto.getWeeklyConfigId())
                .eq(WorkInnoWeeklyInfo::getDeleteFlag, 0);

        // 根据状态筛选
        LocalDateTime now = LocalDateTime.now();
        if (dto.getStatus() != null) {
            if (dto.getStatus() == 1) {
                // 已开始：作业开始时间 <= 当前时间
                queryWrapper.le(WorkInnoWeeklyInfo::getWorkBeginDatetime, now);
                queryWrapper.orderByDesc(WorkInnoWeeklyInfo::getWorkBeginDatetime);
            } else if (dto.getStatus() == 0) {
                // 未开始：作业开始时间 > 当前时间
                queryWrapper.gt(WorkInnoWeeklyInfo::getWorkBeginDatetime, now);
                queryWrapper.orderByAsc(WorkInnoWeeklyInfo::getWorkBeginDatetime);
            }
        }

        // 分页查询
        IPage<WorkInnoWeeklyInfo> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        IPage<WorkInnoWeeklyInfo> resultPage = this.page(page, queryWrapper);

        if (CollUtil.isEmpty(resultPage.getRecords())) {
            return PageInfoBT.noData();
        }

        List<Integer> coursewareGroupIdList = resultPage.getRecords().stream().map(WorkInnoWeeklyInfo::getCoursewareGroupId).distinct().collect(Collectors.toList());
        Map<Integer, String> coursewareGroupMap = courseComponent.queryByCoursewareGroupIdList(coursewareGroupIdList);

        // 转换为VO
        IPage<InnoWeeklyInfoPageVO> voPage = resultPage
                .convert(entity -> {
                    InnoWeeklyInfoPageVO innoWeeklyInfoPageVO = BeanUtil.copyProperties(entity, InnoWeeklyInfoPageVO.class);
                    innoWeeklyInfoPageVO.setCoursewareGroupName(coursewareGroupMap.get(entity.getCoursewareGroupId()));
                    return innoWeeklyInfoPageVO;
                });

        return PageInfoBT.fromPage(voPage);
    }

    @Override
    public List<InnoWeeklyInfoSelectVO> queryInnoWeeklyInfoSelect(QueryInnoWeeklyInfoSelectDTO dto) {
        // 构建查询条件
        LambdaQueryWrapper<WorkInnoWeeklyInfo> queryWrapper = new LambdaQueryWrapper<WorkInnoWeeklyInfo>()
                .eq(WorkInnoWeeklyInfo::getCourseId, dto.getCourseId())
                .eq(WorkInnoWeeklyInfo::getDeleteFlag, IsDeleteEnum.NO.getCode())
                .le(WorkInnoWeeklyInfo::getWorkBeginDatetime, LocalDateTime.now())
                .orderByDesc(WorkInnoWeeklyInfo::getWorkBeginDatetime);

        List<WorkInnoWeeklyInfo> list = this.list(queryWrapper);
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }

        // 转换为VO
        return list.stream().map(entity -> {
            InnoWeeklyInfoSelectVO vo = BeanUtil.copyProperties(entity, InnoWeeklyInfoSelectVO.class);
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public InnoWeeklyInfoDetailVO queryInnoWeeklyInfoDetail(QueryInnoWeeklyInfoDetailDTO dto) {
        WorkInnoWeeklyInfo weeklyInfo = this.getById(dto.getId());
        if (Objects.isNull(weeklyInfo) || !IsDeleteEnum.NO.getCode().equals(weeklyInfo.getDeleteFlag())) {
            return null;
        }

        InnoWeeklyInfoDetailVO vo = BeanUtil.copyProperties(weeklyInfo, InnoWeeklyInfoDetailVO.class);
        if (Objects.equals(vo.getCoursewareGroupId(), 0)) {
            vo.setWeeklyConfigId(null);
        }
        return vo;
    }

    @Override
    public Boolean editInnoWeeklyInfo(EditInnoWeeklyInfoDTO dto) {
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        LocalDateTime now = LocalDateTime.now();

        // 查询原记录
        WorkInnoWeeklyInfo weeklyInfo = this.getById(dto.getId());
        if (weeklyInfo == null || !IsDeleteEnum.NO.getCode().equals(weeklyInfo.getDeleteFlag())) {
            throw new BusinessException("周作业信息不存在");
        }

        if (dto.getWeeklyBeginDate().isAfter(dto.getWeeklyEndDate())) {
            throw new BusinessException("周起始日期不能大于周截止日期");
        }
        if (dto.getWorkBeginDatetime().isAfter(dto.getWorkEndDatetime())) {
            throw new BusinessException("周作业开始时间不能晚于周作业截止时间");
        }

        // 更新字段
        BeanUtil.copyProperties(dto, weeklyInfo);
        FieldUtil.setUpdate(weeklyInfo, headerUserInfo, now);
        return this.updateById(weeklyInfo);
    }

    @Override
    public void generateWeeklyInfo(WorkInnoWeeklyConfig config) {
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        LocalDateTime now = LocalDateTime.now();

        // 校验课程开始日期的周几是否晚于作业开始时间选择的周几
        DayOfWeek courseBeginDayOfWeek = config.getCourseBeginDate().getDayOfWeek();
        DayOfWeek workBeginDayOfWeek = DayOfWeek.of(config.getWorkBeginWeekday());
        if (courseBeginDayOfWeek.getValue() > workBeginDayOfWeek.getValue()) {
            throw new BusinessException(
                    "课程开始日期(" + courseBeginDayOfWeek + ")晚于作业开始时间选择的(" + workBeginDayOfWeek + ")，无法生成周作业");
        }

        // 校验课程结束日期的周几是否早于作业结束时间选择的周几
        DayOfWeek courseEndDayOfWeek = config.getCourseEndDate().getDayOfWeek();
        DayOfWeek workEndDayOfWeek = DayOfWeek.of(config.getWorkEndWeekday());
        if (courseEndDayOfWeek.getValue() < workEndDayOfWeek.getValue()) {
            throw new BusinessException(
                    "课程结束日期(" + courseEndDayOfWeek + ")早于作业结束时间选择的(" + workEndDayOfWeek + ")，无法生成周作业");
        }

        // 计算从课程开始到结束的所有周
        LocalDate currentWeekStart = config.getCourseBeginDate();
        LocalDate courseEndDate = config.getCourseEndDate();

        int weekNumber = 1;
        List<WorkInnoWeeklyInfo> weeklyInfoList = new ArrayList<>();
        // 计算从课程开始到结束的所有周
        while (!currentWeekStart.isAfter(courseEndDate)) {
            WorkInnoWeeklyInfo weeklyInfo = new WorkInnoWeeklyInfo();

            // 设置基本信息
            weeklyInfo.setWeeklyConfigId(config.getId());
            weeklyInfo.setCourseId(config.getCourseId());
            weeklyInfo.setName("第" + weekNumber + "周");
            weeklyInfo.setUserPoints(config.getUserPoints());

            // 计算当前周的开始和结束日期
            LocalDate weekStart, weekEnd;

            if (weekNumber == 1) {
                // 第一周：开始日期为课程开始日期
                weekStart = config.getCourseBeginDate();
            } else {
                // 其他周：开始日期为周一
                weekStart = currentWeekStart.with(DayOfWeek.MONDAY);
            }

            // 计算周结束日期
            LocalDate currentWeekEnd = currentWeekStart.with(DayOfWeek.SUNDAY);
            if (currentWeekEnd.isAfter(courseEndDate)) {
                // 最后一周：结束日期为课程结束日期
                weekEnd = courseEndDate;
            } else {
                // 其他周：结束日期为周日
                weekEnd = currentWeekEnd;
            }

            weeklyInfo.setWeeklyBeginDate(weekStart);
            weeklyInfo.setWeeklyEndDate(weekEnd);

            // 计算作业开始时间
            LocalDate workBeginDate = weekStart.with(DayOfWeek.of(config.getWorkBeginWeekday()));
            LocalDateTime workBeginDatetime = LocalDateTime.of(workBeginDate, config.getWorkBeginTime());

            // 计算作业截止时间
            LocalDate workEndDate = weekEnd.with(DayOfWeek.of(config.getWorkEndWeekday()));
            LocalDateTime workEndDatetime = LocalDateTime.of(workEndDate, config.getWorkEndTime());

            weeklyInfo.setWorkBeginDatetime(workBeginDatetime);
            weeklyInfo.setWorkEndDatetime(workEndDatetime);

            // 设置创建和更新信息
            FieldUtil.setCreateAndUpdate(weeklyInfo, headerUserInfo, now);

            // 保存周作业信息
            weeklyInfoList.add(weeklyInfo);

            // 移动到下一周
            currentWeekStart = currentWeekStart.plusWeeks(1);
            weekNumber++;
        }

        if (CollUtil.isNotEmpty(weeklyInfoList)) {
            this.saveBatch(weeklyInfoList);
        }
    }

    @Override
    public Map<Long, QueryInnoWeeklyInfoByIdListVO> queryInnoWeeklyInfoByIdList(List<Long> idList) {
        List<WorkInnoWeeklyInfo> list = this.listByIds(idList);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().map(entity -> BeanUtil.copyProperties(entity, QueryInnoWeeklyInfoByIdListVO.class))
                .collect(Collectors.toMap(QueryInnoWeeklyInfoByIdListVO::getId, Function.identity(), (k1, k2) -> k1));
    }

    @Override
    public List<InnoWeeklyInfoVO> queryByCourseIdAndTime(QueryByCourseIdAndTimeDTO dto) {
        return this.list(new LambdaQueryWrapper<WorkInnoWeeklyInfo>()
                .eq(WorkInnoWeeklyInfo::getCourseId, dto.getCourseId())
                .ge(WorkInnoWeeklyInfo::getWorkBeginDatetime, dto.getStartTime())
                .le(WorkInnoWeeklyInfo::getWorkBeginDatetime, dto.getEndTime())
                .eq(WorkInnoWeeklyInfo::getDeleteFlag, 0))
                .stream().map(entity -> BeanUtil.copyProperties(entity, InnoWeeklyInfoVO.class))
                .collect(Collectors.toList());
    }

    @Override
    public List<InnoWeeklyInfoVO> queryWeeklyInfoByCourseId(Integer courseId) {
        // 获取当前周的周日最后一秒
        LocalDateTime endOfSunday = LocalDateTime.now()
                .with(DayOfWeek.SUNDAY)
                .withHour(23)
                .withMinute(59)
                .withSecond(59);
        log.info("查询周作业信息, 课程id: {}, 截止时间: {}", courseId, endOfSunday);
        return this.list(new LambdaQueryWrapper<WorkInnoWeeklyInfo>()
                        .eq(WorkInnoWeeklyInfo::getCourseId, courseId)
                        .eq(WorkInnoWeeklyInfo::getDeleteFlag, 0)
                        .le(WorkInnoWeeklyInfo::getWorkBeginDatetime, endOfSunday))
                .stream().map(entity -> BeanUtil.copyProperties(entity, InnoWeeklyInfoVO.class))
                .collect(Collectors.toList());
    }

    @Override
    public QueryInnoWeeklyInfoListVO queryCurrentTimeWeeklyInfo(Integer userId, Integer courseId) {
        QueryInnoWeeklyInfoListVO result = new QueryInnoWeeklyInfoListVO();
        WorkInnoWeeklyInfo workInnoWeeklyInfo = this.getOne(new LambdaQueryWrapper<WorkInnoWeeklyInfo>()
                        .eq(WorkInnoWeeklyInfo::getCourseId, courseId)
                        .le(WorkInnoWeeklyInfo::getWorkBeginDatetime, LocalDateTime.now())
                        .ge(WorkInnoWeeklyInfo::getWeeklyEndDate, LocalDateTime.now())
                        .eq(WorkInnoWeeklyInfo::getDeleteFlag, IsDeleteEnum.NO.getCode())
                        .orderByDesc(WorkInnoWeeklyInfo::getId));
        if(ObjectUtil.isNull(workInnoWeeklyInfo)) return result;
        BeanUtils.copyProperties(workInnoWeeklyInfo, result);
        Map<Long, GetContentListByDataIdSubDataIdVO> contentMap = commentComponent
                .getContentListByDataIdSubDataId(4, courseId.longValue(), null, userId)
                .stream().collect(Collectors.toMap(GetContentListByDataIdSubDataIdVO::getSubDataId, Function.identity(),
                        (k1, k2) -> k1));
        result.setCompleteFlag(contentMap.containsKey(workInnoWeeklyInfo.getId()));
        return result;
    }
}
