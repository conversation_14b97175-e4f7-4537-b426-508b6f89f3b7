package com.sibuqu.work.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sibuqu.base.common.result.HeaderUserInfo;
import com.sibuqu.user.vo.user.UserInfoCompanySearchVO;
import com.sibuqu.work.componet.UserComponent;
import com.sibuqu.work.entity.WorkTeacherInfo;
import com.sibuqu.work.mapper.WorkTeacherInfoMapper;
import com.sibuqu.work.service.WorkTeacherInfoService;
import com.sibuqu.work.vo.api.CompanyMyServiceVO;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;


import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service("workTeacherInfoService")
@RefreshScope
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class WorkTeacherInfoServiceImpl extends ServiceImpl<WorkTeacherInfoMapper, WorkTeacherInfo> implements WorkTeacherInfoService {
    private final UserComponent userComponent;
    @Value("${userCenter.company:[" +
            "{\"title\":\"企业作业\",\"jumpType\":1,\"imgUrl\":\"https://resource.sibuqu.com/kecheng2/d2dd13c6e3d2ec67170bc0d2859f1082.png\",\"jumpUrl\":\"\"}," +
            "{\"title\":\"回复作业\",\"jumpType\":2,\"imgUrl\":\"https://resource.sibuqu.com/kecheng2/f2f25cdf36115d5755e47b5b5f730716.png\",\"jumpUrl\":\"\"}," +
            "{\"title\":\"我的证书\",\"jumpType\":5,\"imgUrl\":\"https://resource.sibuqu.com/kecheng2/80d13161bd3ea99c54af4ed0700105de.png\",\"jumpUrl\":\"\"}," +
            "{\"title\":\"在线客服\",\"jumpType\":4,\"imgUrl\":\"https://resource.sibuqu.com/kecheng2/cbf6e47c223658264fac5a58e6d19d26.png\",\"jumpUrl\":\"\"}" +
            "]}")
    private String companyMyService;

    @Override
    public List<CompanyMyServiceVO> companyMyServiceList(HeaderUserInfo headerUserInfo) {
        UserInfoCompanySearchVO userInfoCompanySearchVO = userComponent.userByUserIdCompanyId(headerUserInfo.getCurrentCompanyId(), headerUserInfo.getId());
        List<CompanyMyServiceVO> companyMyServiceVOS = JSON.parseArray(companyMyService, CompanyMyServiceVO.class);
        if (this.count(new QueryWrapper<WorkTeacherInfo>().lambda().eq(WorkTeacherInfo::getUserId, headerUserInfo.getId())) > 0 || (Objects.nonNull(userInfoCompanySearchVO) && Objects.nonNull(userInfoCompanySearchVO.getSpecialUserFlag()) && userInfoCompanySearchVO.getSpecialUserFlag().equals(1))) {
            return companyMyServiceVOS;
        }
        return companyMyServiceVOS.stream()
                .filter(companyMyServiceVO -> !companyMyServiceVO.getJumpType().equals(2))
                .collect(Collectors.toList());
    }
}
