package com.sibuqu.work.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sibuqu.activity.vo.ebook.HeartReadListVO;
import com.sibuqu.base.common.enums.IsDeleteEnum;
import com.sibuqu.base.common.enums.YesOrNoEnum;
import com.sibuqu.base.common.exception.BusinessException;
import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.base.common.result.BaseResultCode;
import com.sibuqu.base.common.result.HeaderUserInfo;
import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.bidata.dto.UserDoneActionInfoDTO;
import com.sibuqu.classes.vo.ClassTeacherVO;
import com.sibuqu.classes.vo.ClassesTeamDetailVO;
import com.sibuqu.classes.vo.GetSimpleClassInfoVO;
import com.sibuqu.classes.vo.InnerClassesUserInfoListVO;
import com.sibuqu.classes.vo.MemberListByCourseIdClassesNoTeamIdVO;
import com.sibuqu.classes.vo.MySimpleClassesDetailVO;
import com.sibuqu.common.dto.common.CommonPushMessageDTO;
import com.sibuqu.common.dto.common.SearchCommentAndLikeTotalDTO;
import com.sibuqu.common.enums.CommentTypeEnum;
import com.sibuqu.common.enums.LikeTypeEnum;
import com.sibuqu.common.enums.PushTypeConstant;
import com.sibuqu.common.vo.common.CommonLabelPageVO;
import com.sibuqu.common.vo.common.SearchCommentAndLikeTotalVO;
import com.sibuqu.course.dto.coursetimetable.CourseResourceUnlockUserDTO;
import com.sibuqu.course.dto.coursetimetable.CourseTimetableDetailDTO;
import com.sibuqu.course.dto.coursetimetable.CourseTimetableInfoDTO;
import com.sibuqu.course.dto.coursetimetable.QueryByCourseIdAndDateDTO;
import com.sibuqu.course.entity.CourseConfig;
import com.sibuqu.course.entity.CourseInfo;
import com.sibuqu.course.entity.CourseTimetable;
import com.sibuqu.course.vo.coursetimetable.CourseTimetableHistoryVO;
import com.sibuqu.course.vo.coursetimetable.CourseTimetableInfoVO;
import com.sibuqu.course.vo.coursetimetable.CourseTimetableUnlockDetailVO;
import com.sibuqu.course.vo.coursetimetable.GetMakeUpWorkCttListVO;
import com.sibuqu.course.vo.goodsinfo.GoodsIdListByCourseIdListVO;
import com.sibuqu.course.vo.goodsinfo.GoodsInfoDetailVO;
import com.sibuqu.course.vo.goodsinfo.InnoCourseGoodsDetailVO;
import com.sibuqu.order.entity.auth.LegalRightCourse;
import com.sibuqu.order.entity.auth.UserRightsCourse;
import com.sibuqu.prefecture.enums.PrefectureContentAsyncTypeEnum;
import com.sibuqu.starter.mongohelper.bean.SortBuilder;
import com.sibuqu.starter.redis.util.RedisUtil;
import com.sibuqu.starter.redis.util.RedissonLockUtil;
import com.sibuqu.user.bo.CompanyUserDynamicsBO;
import com.sibuqu.user.dto.classs.ClassMemberUserInfoDTO;
import com.sibuqu.user.dto.user.UserInfoEntityListSearchDTO;
import com.sibuqu.user.dto.user.UserInfoListSearchByPhoneDTO;
import com.sibuqu.user.enums.classgroup.UserRoleEnums;
import com.sibuqu.user.vo.api.UserInfoVO;
import com.sibuqu.user.vo.classs.ClassMemberUserInfoVO;
import com.sibuqu.user.vo.company.BatchGetCompanySimpleVO;
import com.sibuqu.user.vo.user.UserInfoCompanySearchVO;
import com.sibuqu.user.vo.user.UserInfoSearchSimpleVO;
import com.sibuqu.work.bo.ClassesMemberDynamicKafkaBO;
import com.sibuqu.work.bo.DoWorkSendKafkaBO;
import com.sibuqu.work.bo.WorkInfoPojoQueryDTO;
import com.sibuqu.work.commom.constants.RedisConstants;
import com.sibuqu.work.common.MongoBase;
import com.sibuqu.work.componet.*;
import com.sibuqu.work.dto.*;
import com.sibuqu.work.entity.ClassStudyStatistics;
import com.sibuqu.work.entity.CourseTimetableModelDetail;
import com.sibuqu.work.entity.UserStudyInfo;
import com.sibuqu.work.entity.WorkInfo;
import com.sibuqu.work.entity.WorkInfoDetail;
import com.sibuqu.work.entity.WorkModelDetail;
import com.sibuqu.work.entity.WorkTeacherInfo;
import com.sibuqu.work.entity.WorkTeacherReply;
import com.sibuqu.work.entity.WorkUserModel;
import com.sibuqu.work.enums.*;
import com.sibuqu.work.feignclient.client.CourseFeignClient;
import com.sibuqu.work.kafka.production.KafkaProduction;
import com.sibuqu.work.mapper.ClassStudyStatisticsMapper;
import com.sibuqu.work.mapper.CourseTimetableModelDetailMapper;
import com.sibuqu.work.mapper.UserStudyInfoMapper;
import com.sibuqu.work.mapper.WorkInfoDetailMapper;
import com.sibuqu.work.mapper.WorkInfoMapper;
import com.sibuqu.work.mapper.WorkModelDetailMapper;
import com.sibuqu.work.mapper.WorkTeacherInfoMapper;
import com.sibuqu.work.mapper.WorkTeacherReplyMapper;
import com.sibuqu.work.mongo.WorkInfoPojo;
import com.sibuqu.work.redislock.lock.JedisDistributedLock;
import com.sibuqu.work.redislock.pool.JedisPoolImpl;
import com.sibuqu.work.service.CommonAsyncService;
import com.sibuqu.work.service.WorkInfoPojoMongoService;
import com.sibuqu.work.service.WorkService;
import com.sibuqu.work.service.WorkTeacherReplyService;
import com.sibuqu.work.service.WorkUserModelService;
import com.sibuqu.work.util.AESUtil;
import com.sibuqu.work.util.HeaderUserInfoUtil;
import com.sibuqu.work.util.TimeUtil;
import com.sibuqu.work.vo.AppWorkConfigVO;
import com.sibuqu.work.vo.admin.AppChapterVO;
import com.sibuqu.work.vo.admin.AppEbookVO;
import com.sibuqu.work.vo.admin.AppOptionVO;
import com.sibuqu.work.vo.admin.CalcWorkVO;
import com.sibuqu.work.vo.admin.WorkInfoVO;
import com.sibuqu.work.vo.admin.WorkListVO;
import com.sibuqu.work.vo.admin.WorkModelDetailVO;
import com.sibuqu.work.vo.api.AdditionalInfoVO;
import com.sibuqu.work.vo.api.AppCompanyWorkInfoListVO;
import com.sibuqu.work.vo.api.AppWorkCommentLikeListVO;
import com.sibuqu.work.vo.api.AppWorkCommentListVO;
import com.sibuqu.work.vo.api.AppWorkDetailListVO;
import com.sibuqu.work.vo.api.AppWorkInfoListVO;
import com.sibuqu.work.vo.api.AppWorkReadHeartVO;
import com.sibuqu.work.vo.api.AppWorkTeacherReplyVO;
import com.sibuqu.work.vo.api.ClassStatisticsVO;
import com.sibuqu.work.vo.api.ClassSubRateVO;
import com.sibuqu.work.vo.api.ClassWorkDataVO;
import com.sibuqu.work.vo.api.ClassWorkRankItemVO;
import com.sibuqu.work.vo.api.ClassWorkRankVO;
import com.sibuqu.work.vo.api.CommonLabelVO;
import com.sibuqu.work.vo.api.CommonWorkVO;
import com.sibuqu.work.vo.api.CompanyRankItemVO;
import com.sibuqu.work.vo.api.CompanyRankVO;
import com.sibuqu.work.vo.api.GetRecommendVO;
import com.sibuqu.work.vo.api.JumpPageWorkRemindParamVO;
import com.sibuqu.work.vo.api.LazyAppWorkInfoVO;
import com.sibuqu.work.vo.api.LearningSituationVO;
import com.sibuqu.work.vo.api.MakeUpWorkListVO;
import com.sibuqu.work.vo.api.MyScoreVO;
import com.sibuqu.work.vo.api.MyWorkDetailVO;
import com.sibuqu.work.vo.api.MyWorkVO;
import com.sibuqu.work.vo.api.OneWorkVO;
import com.sibuqu.work.vo.api.PersonalWorkDataVO;
import com.sibuqu.work.vo.api.PersonalWorkRankItemVO;
import com.sibuqu.work.vo.api.PersonalWorkRankVO;
import com.sibuqu.work.vo.api.TeamStatisticsVO;
import com.sibuqu.work.vo.api.TodayStudyUserTopVO;
import com.sibuqu.work.vo.api.UserFlagInfoVO;
import com.sibuqu.work.vo.api.UserStudyStatisticsDetailVO;
import com.sibuqu.work.vo.api.UserStudyStatisticsVO;
import com.sibuqu.work.vo.api.UserStudyVO;
import com.sibuqu.work.vo.api.WorkByIdVO;
import com.sibuqu.work.vo.api.WorkCardInfoVO;
import com.sibuqu.work.vo.api.WorkReplyVO;
import com.sibuqu.work.vo.api.WorkStatusInfoVO;
import com.sibuqu.work.vo.api.WorkingByDateVO;
import com.sibuqu.work.webclient.model.dto.CourseClassTeamDTO;
import com.sibuqu.work.webclient.model.vo.ClassGroupRoleVO;
import com.sibuqu.work.webclient.model.vo.ClassHworkStatisticsVO;
import com.sibuqu.work.webclient.model.vo.ClassMemberRankVO;
import com.sibuqu.work.webclient.model.vo.ClassRankVO;
import com.sibuqu.work.webclient.model.vo.CourseWorkPlanSchemeOptionVO;
import com.sibuqu.work.webclient.model.vo.HistoryWorkingVO;
import com.sibuqu.work.webclient.model.vo.MyWorkDateVO;
import com.sibuqu.work.webclient.model.vo.PerSonMyWorkVO;
import com.sibuqu.work.webclient.model.vo.ToDayWorkVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import redis.clients.jedis.Jedis;

import javax.servlet.http.HttpServletRequest;
import java.text.DecimalFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.sibuqu.work.enums.WorkDetailTypeEnum.AUDIO_TEXT;
import static com.sibuqu.work.enums.WorkDetailTypeEnum.CLOCK_NOUN_SCORE;
import static com.sibuqu.work.enums.WorkDetailTypeEnum.DUSING;
import static com.sibuqu.work.enums.WorkDetailTypeEnum.DYNAMIC_MULTIPLE_CHOICE;
import static com.sibuqu.work.enums.WorkDetailTypeEnum.DYNAMIC_SINGLE_CHOICE;
import static com.sibuqu.work.enums.WorkDetailTypeEnum.EVALUATE;
import static com.sibuqu.work.enums.WorkDetailTypeEnum.LISTEN;
import static com.sibuqu.work.enums.WorkDetailTypeEnum.MULTIPLE_CHOICE;
import static com.sibuqu.work.enums.WorkDetailTypeEnum.NOUN_SCORE;
import static com.sibuqu.work.enums.WorkDetailTypeEnum.READ;
import static com.sibuqu.work.enums.WorkDetailTypeEnum.SHARE;
import static com.sibuqu.work.enums.WorkDetailTypeEnum.TEXT;
import static java.lang.System.currentTimeMillis;

@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class WorkServiceImpl extends ServiceImpl<WorkInfoDetailMapper, WorkInfoDetail> implements WorkService {
    private final UserStudyInfoMapper userStudyInfoMapper;
    private final WorkTeacherReplyMapper workTeacherReplyMapper;
    private final WorkInfoMapper workInfoMapper;
    private final ClassStudyStatisticsMapper classStudyStatisticsMapper;
    private final WorkModelDetailMapper workModelDetailMapper;
    private final CourseTimetableModelDetailMapper courseTimetableModelDetailMapper;
    private final CourseComponent courseComponent;
    private final ClassesComponent classesComponent;
    private final UserComponent userComponent;
    private final CommentComponent commentComponent;
    private final KafkaProduction kafkaProduction;
    private final ServerComponent serverComponent;
    private final OldServerComponent oldServerComponent;
    private final WorkTeacherReplyService workTeacherReplyService;
    private final RedisTemplate<String, Object> redisTemplate;
    private final CommonAsyncService commonAsyncService;
    private final CourseFeignClient courseFeignClient;
    private final RedisUtil redisUtil;
    private final WorkTeacherInfoMapper workTeacherInfoMapper;
    private final MongoBase mongoBase;
    private final PrefectureComponent prefectureComponent;
    private final OrderComponent orderComponent;
    private final WorkUserModelService workUserModelService;
    private final WorkInfoPojoMongoService workInfoPojoMongoService;
    private final ThreadPoolTaskExecutor quicklyExecutor;
    private final ActivityComponent activityComponent;
    private final BidataComponent bidataComponent;
    @Value("${kafka.topic.company.doWork:work}")
    private String workTopic;

    @Value("${kafka.topic.companyUserDynamics:companyUserDynamics-demo}")
    private String companyUserDynamicsTopic;

    @Override
    public WorkCardInfoVO workCardInfo(WorkCardInfoDTO workCardInfoDTO, HeaderUserInfo headerUserInfo) {
        //企业版 + 个人版 课程表
        CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(workCardInfoDTO.getCourseId(), workCardInfoDTO.getCourseTimeTableId());
        if (Objects.isNull(courseTimetableUnlockDetailVO)) {
            throw new BusinessException("课程尚未开始,请学习后再提交作业");
        } else {
            if (!(LocalDateTime.now().isAfter(courseTimetableUnlockDetailVO.getWorkBeginTime()) && LocalDateTime.now().isBefore(courseTimetableUnlockDetailVO.getWorkEndTime()))) {
                throw new BusinessException("课程尚未开始,请学习后再提交作业");
            }
        }
        WorkInfoPojo workInfo = workInfoPojoMongoService.querySimpleOne(new WorkInfoPojoQueryDTO()
                .setUserId(headerUserInfo.getId()).setCourseId(workCardInfoDTO.getCourseId())
                .setCourseTimetableId(courseTimetableUnlockDetailVO.getId()));
        if (Objects.nonNull(workInfo) && Objects.equals(workInfo.getWorkStatus(), 1)) {
            throw new BusinessException("今日已提交作业");
        }
        QueryWrapper<UserStudyInfo> userStudyInfoQueryWrapper = new QueryWrapper<>();
        userStudyInfoQueryWrapper
                .lambda()
                .eq(UserStudyInfo::getCourseId, workCardInfoDTO.getCourseId())
                .eq(UserStudyInfo::getCompanyId, headerUserInfo.getCurrentCompanyId())
                .eq(UserStudyInfo::getUserId, headerUserInfo.getId());
        UserStudyInfo userStudyInfo = userStudyInfoMapper.selectOne(userStudyInfoQueryWrapper);
        List<WorkModelDetailVO> detail = queryWorkModelDetailList(courseTimetableUnlockDetailVO.getWorkModelId(), workCardInfoDTO.getCourseId(), courseTimetableUnlockDetailVO);
        if (StringUtils.isNotBlank(courseTimetableUnlockDetailVO.getCream())) {
            detail.add(0, WorkModelDetailVO.builder()
                    .id(0)
                    .title("")
                    .manualStatus(0)
                    .score(0)
                    .description(courseTimetableUnlockDetailVO.getCream())
                    .type(NOUN_SCORE.getCode())
                    .build());
        }
        if (Objects.nonNull(workInfo)) {
            for (WorkModelDetailVO workModelDetailVO : detail) {
                if (workModelDetailVO.getType().equals(WorkDetailTypeEnum.LISTEN.getCode())) {
                    workModelDetailVO.setManualStatus(courseTimetableUnlockDetailVO.getManualClockSwitch());
                    if (workInfo.getListenStatus().equals(1)) {
                        workModelDetailVO.setContent("1");
                        break;
                    }

                }
                if (workModelDetailVO.getType().equals(SHARE.getCode())) {
                    workModelDetailVO.setScore(0);
                    workModelDetailVO.setShareStatus("0");
                }
            }
        } else {
            for (WorkModelDetailVO workModelDetailVO : detail) {
                if (workModelDetailVO.getType().equals(SHARE.getCode())) {
                    workModelDetailVO.setScore(0);
                    workModelDetailVO.setShareStatus("0");
                }
                if (workModelDetailVO.getType().equals(WorkDetailTypeEnum.LISTEN.getCode())) {
                    workModelDetailVO.setManualStatus(courseTimetableUnlockDetailVO.getManualClockSwitch());
                }
            }
        }
        return WorkCardInfoVO
                .builder()
                .workModelId(courseTimetableUnlockDetailVO.getWorkModelId())
                .courseId(workCardInfoDTO.getCourseId())
                .workEndTime(courseTimetableUnlockDetailVO.getWorkEndTime())
                .score(Objects.nonNull(userStudyInfo) ? userStudyInfo.getScore() - (Objects.nonNull(workInfo) ? workInfo.getScore() : 0) : 0)
                .workTitle(courseTimetableUnlockDetailVO.getResourceTitle())
                .workStartTime(courseTimetableUnlockDetailVO.getWorkBeginTime())
                .courseTimeTableId(courseTimetableUnlockDetailVO.getId())
                .workModelDetails(detail)
                .topTip("请提交《" + courseTimetableUnlockDetailVO.getResourceTitle() + "》课程作业\n" +
                        (courseTimetableUnlockDetailVO.getWorkEndTime().getMonthValue() < 10 ? "0" + courseTimetableUnlockDetailVO.getWorkEndTime().getMonthValue() : courseTimetableUnlockDetailVO.getWorkEndTime().getMonthValue()) + "月" +
                        (courseTimetableUnlockDetailVO.getWorkEndTime().getDayOfMonth() < 10 ? "0" + courseTimetableUnlockDetailVO.getWorkEndTime().getDayOfMonth() : courseTimetableUnlockDetailVO.getWorkEndTime().getDayOfMonth()) + "日" +
                        courseTimetableUnlockDetailVO.getWorkEndTime().getHour() + ":" +
                        (courseTimetableUnlockDetailVO.getWorkEndTime().getMinute() < 10 ? "0" + courseTimetableUnlockDetailVO.getWorkEndTime().getMinute() : courseTimetableUnlockDetailVO.getWorkEndTime().getMinute()) + "之后将无法补交，提交后不可修改。")
                .build();
    }

    @Override
    public PageInfoBT<AppWorkInfoListVO> heartPerceptionList(AppWorkHeartPerceptionDTO dto, HeaderUserInfo headerUserInfo) {
        // 企业 id
        UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(dto.getCourseId(), headerUserInfo.getId());
        headerUserInfo.setCurrentCompanyId(userRightsCourse.getCompanyId());
        long l1 = System.currentTimeMillis();
        IPage<WorkInfoPojo> workInfoIPage;
        if(Objects.nonNull(dto.getCourseTimetableId())
                && Objects.nonNull(dto.getSortType())
                && dto.getCourseTimetableId() > 0
                && dto.getSortType() > 0){
            workInfoIPage = workInfoPojoMongoService.heartPerceptionListByWareId(dto, headerUserInfo);
        }else {
            workInfoIPage = workInfoPojoMongoService.heartPerceptionList(dto, headerUserInfo);
        }
        if (CollUtil.isEmpty(workInfoIPage.getRecords())) {
            return PageInfoBT.noData();
        }
        CourseInfo courseInfo = courseComponent.courseInfoById(dto.getCourseId());
        long l2 = System.currentTimeMillis();
        List<Integer> workIds = workInfoIPage.getRecords().stream().map(WorkInfoPojo::getWorkId).collect(Collectors.toList());
        long l3 = System.currentTimeMillis();
        // 调用公共服务-批量获取心得评论数据
        Map<Integer, List<AppWorkCommentListVO>> commentMap = commentComponent.batchSearchCommentList(workIds, headerUserInfo.getId());
        // 调用公共服务-批量获取心得点赞数据
        long l4 = System.currentTimeMillis();
        Map<Integer, List<AppWorkCommentLikeListVO>> likeMap = commentComponent.batchSearchLikeList(workIds, headerUserInfo.getId());
        // 构建心得关联老师回复数据
        long l5 = System.currentTimeMillis();
//        Map<Integer, List<AppWorkTeacherReplyVO>> replyMap = findTeacherReplyListByWorkIds(workIds, headerUserInfo.getId(), null);
        Map<Integer, List<AppWorkTeacherReplyVO>> replyMap = null;
        // 新老学员
        Map<Integer, Integer> oldUserMap = getOldUserMapByWorkInfoPage(dto, workInfoIPage);
        // 非本企业下的用户昵称展示
        List<Integer> notCurCompanyIdList = notCurCompanyIdList(headerUserInfo, workInfoIPage, commentMap);
        if (CollUtil.isNotEmpty(likeMap.values())) {
            likeMap.values().forEach(list -> {
                list.forEach(like -> {
                    if (!Objects.equals(like.getCompanyId(), headerUserInfo.getCurrentCompanyId())) {
                        notCurCompanyIdList.add(like.getPostUserId());
                    }
                });
            });
        }
        Map<Integer, String> userFullNameMap = userComponent.userFullNameByIdList(notCurCompanyIdList);
        // 读书展示
        List<Long> readRecordIdList = new ArrayList<>();
        workInfoIPage.getRecords().forEach(workInfo -> {
            if (StrUtil.isNotBlank(workInfo.getContent())) {
                List<WorkModelDetailVO> workInfoDetailList = JSONArray.parseArray(workInfo.getContent(), WorkModelDetailVO.class);
                workInfoDetailList.stream().filter(workModelDetailVO -> WorkDetailTypeEnum.READ.getCode().equals(workModelDetailVO.getType()))
                        .forEach(workModelDetailVO -> {
                            readRecordIdList.add(workModelDetailVO.getReadRecordId());
                        });
            }
        });
        Map<Long, HeartReadListVO> readRecordMap = activityComponent.heartReadList(readRecordIdList);
        List<AppWorkInfoListVO> voList = new ArrayList<>();
        for (WorkInfoPojo workInfo : workInfoIPage.getRecords()) {
            AppWorkInfoListVO appWorkInfoListVO = convertAppWorkInfoListVO(workInfo, readRecordMap, true, headerUserInfo);
            if (CollUtil.isEmpty(appWorkInfoListVO.getWorkContentList())) continue;

            if(Objects.nonNull(dto.getCourseTimetableId())
                    && Objects.nonNull(dto.getSortType())
                    && dto.getCourseTimetableId() > 0
                    && dto.getSortType() > 0){
                Integer count = 0;
                for (AppWorkDetailListVO appWorkDetailListVO : appWorkInfoListVO.getWorkContentList()){
                    if (Objects.nonNull(appWorkDetailListVO)){
                        if (Objects.isNull(appWorkDetailListVO.getContent())){
                            count = count + 0;
                        }else {
                            count = count + appWorkDetailListVO.getContent().length();
                        }
                    }
                }
                if (count <= 5){
                    continue;
                }
            }

            // 从Map获取当前心得的评论数据
            if (ObjectUtil.isNotNull(commentMap)) {
                appWorkInfoListVO.setCommentList(commentMap.get(workInfo.getWorkId()));
                if (CollUtil.isNotEmpty(appWorkInfoListVO.getCommentList())) {
                    appWorkInfoListVO.getCommentList().forEach(comment -> {
                        if (comment.getCompanyId() != null && !Objects.equals(comment.getCompanyId(), headerUserInfo.getCurrentCompanyId())) {
                            comment.setUserName(userFullNameMap.get(comment.getUserId()));
                        }
                    });
                }
            }
            // 从Map获取当前心得的点赞数据
            if (ObjectUtil.isNotNull(likeMap)) {
                appWorkInfoListVO.setLikeList(likeMap.get(workInfo.getWorkId()));
                if (CollUtil.isNotEmpty(appWorkInfoListVO.getLikeList())) {
                    appWorkInfoListVO.getLikeList().forEach(like->{
                        if (like.getCompanyId() != null && !Objects.equals(like.getCompanyId(), headerUserInfo.getCurrentCompanyId())) {
                            like.setPostUserName(userFullNameMap.get(like.getPostUserId()));
                        }
                    });
                }
            }
            // 判断我的点赞状态
            if (CollectionUtil.isNotEmpty(appWorkInfoListVO.getLikeList())) {
                boolean myLikeFlag = appWorkInfoListVO.getLikeList().stream().anyMatch(like -> like.getPostUserId().equals(headerUserInfo.getId()));
                appWorkInfoListVO.setPostState(myLikeFlag ? 1 : 0);
            } else {
                appWorkInfoListVO.setPostState(0);
            }
            // 从Map获取当前心得的老师回复数据
            if (ObjectUtil.isNotNull(replyMap)) {
                appWorkInfoListVO.setTeacherReplyList(replyMap.get(workInfo.getWorkId()));
            }
            appWorkInfoListVO.setWorkModelFlag(courseInfo.getWorkModelFlag());
            if (WorkModelFlagEnum.DIFF_OLD_NEW.getCode().equals(courseInfo.getWorkModelFlag())) {
                appWorkInfoListVO.setUserFlag(oldUserMap.get(workInfo.getUserId()) == null ? 0 : 1);
            }
            // 用户昵称
            if (Objects.nonNull(workInfo.getCompanyId()) && !Objects.equals(workInfo.getCompanyId(), headerUserInfo.getCurrentCompanyId())) {
                appWorkInfoListVO.setUserName(userFullNameMap.get(workInfo.getUserId()));
            }
            appWorkInfoListVO.setCompanyName("");
            appWorkInfoListVO.setCourseImgUrl(courseInfo.getCourseImgUrl());
            voList.add(appWorkInfoListVO);
        }
        PageInfoBT<AppWorkInfoListVO> pageInfoBT = new PageInfoBT<>();
        pageInfoBT.setCurrent(workInfoIPage.getCurrent());
        pageInfoBT.setSize(workInfoIPage.getSize());
        pageInfoBT.setTotal(workInfoIPage.getTotal());
        pageInfoBT.setRecords(voList);
        long l6 = System.currentTimeMillis();
        log.info("心得列表接口耗时统计： 【l2-l1】:" + (l2 - l1) + " 【l3-l2】:" + (l3 - l2) + "【l4-l3】:" + (l4 - l3) + " 【l5-l4】:" + (l5 - l4) + "【l6-l5】:" + (l6 - l5));
        return pageInfoBT;
    }

    private int moveReadTypeToLast(WorkModelDetailVO o1, WorkModelDetailVO o2) {
        // 读书的排在最后面
        if (READ.getCode().equals(o1.getType()) && READ.getCode().equals(o2.getType())) {
            return o1.getSorted() - o2.getSorted();
        }
        if (READ.getCode().equals(o1.getType())) {
            return 1;
        }
        if (READ.getCode().equals(o2.getType())) {
            return -1;
        }
        return o1.getSorted() - o2.getSorted();
    }

    private Map<Integer, Integer> getOldUserMapByWorkInfoPage(AppWorkHeartPerceptionDTO dto, IPage<WorkInfoPojo> workInfoIPage) {
        List<Integer> oldUserIdList = workInfoIPage.getRecords().stream().filter(work -> WorkModelFlagEnum.DIFF_OLD_NEW.getCode().equals(work.getWorkModelFlag()))
                .map(WorkInfoPojo::getUserId).collect(Collectors.toList());
        if (CollUtil.isEmpty(oldUserIdList)) return new HashMap<>();
        return workInfoMapper.checkOldUserByUserIds(dto.getCourseId(), oldUserIdList)
                .stream().collect(Collectors.toMap(Function.identity(), Function.identity()));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doWork(DoWorkDTO doWorkDTO, HeaderUserInfo headerUserInfo) {
        DoWorkMsgDTO doWorkMsgDTO = new DoWorkMsgDTO();
        BeanUtils.copyProperties(doWorkDTO, doWorkMsgDTO);
        doWorkMsgDTO.setUserId(headerUserInfo.getId());
        doWorkMsgDTO.setUserName(headerUserInfo.getUserFullName());
        doWorkMsgDTO.setCompanyId(headerUserInfo.getCurrentCompanyId());
        doWorkMsgDTO.setAvatar(headerUserInfo.getAvatar());
        doWorkMsgDTO.setPhone(headerUserInfo.getPhone());
        if (headerUserInfo.getCurrentCompanyId() > 0) {
            Map<Integer, String> map = userComponent.queryUserCompanyNameById(headerUserInfo.getCurrentCompanyId(), Collections.singletonList(headerUserInfo.getId()));
            doWorkMsgDTO.setUserName(map.get(headerUserInfo.getId()));

        }
        doWorkMsgDTO.setTag(WorkKafkaMessageEnum.DO_WORK.getCode());
        this.syncDoWork(doWorkMsgDTO);
    }

    public void checkWorkTime(CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO, CourseConfig courseConfig) {
        if (Objects.isNull(courseTimetableUnlockDetailVO)) {
            log.info("checkWorkTime syncDoWork: 课程尚未开始,请学习后再提交作业");
            throw new BusinessException("课程尚未开始,请学习后再提交作业");
        } else {
            if (Objects.isNull(courseTimetableUnlockDetailVO.getWorkBeginTime()) || Objects.isNull(courseTimetableUnlockDetailVO.getWorkEndTime())) {
                log.info("listenAddScore 作业开始结束时间未设置-退出听课加分");
                throw new BusinessException("不在作业提交时间内");
            }
            if (Objects.nonNull(courseConfig) && courseConfig.getMakeUpWork().equals(YesOrNoEnum.YES.getCode()) && LocalDateTime.now().isAfter(courseTimetableUnlockDetailVO.getWorkEndTime())) {
                if (!(LocalDateTime.now().isBefore(courseTimetableUnlockDetailVO.getWorkEndTime().plusDays(courseConfig.getMakeUpWorkBeginWeek())))) {
                    log.info("checkWorkTime syncDoWork: 作业提交时间已过");
                    throw new BusinessException("作业提交时间已过");
                }
            } else {
                if (!(LocalDateTime.now().isAfter(courseTimetableUnlockDetailVO.getWorkBeginTime()) && LocalDateTime.now().isBefore(courseTimetableUnlockDetailVO.getWorkEndTime()))) {
                    log.info("checkWorkTime syncDoWork: 课程尚未开始,请学习后再提交作业");
                    throw new BusinessException("不在作业提交时间内");
                }
            }
        }
    }

    @Override
    public void syncDoWork(DoWorkMsgDTO dto) {
        log.info("syncDoWork doWorkDTO dto:{}", JSON.toJSONString(dto));
        long l1 = currentTimeMillis();
        // 企业版+个人版 课程表
        UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(dto.getCourseId(), dto.getUserId());
        dto.setCompanyId(userRightsCourse.getCompanyId());
        CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(dto.getCourseId(), dto.getCourseTimeTableId(), dto.getUserId());
        log.info("syncDoWork courseTimetableUnlockDetailVO:{}", JSON.toJSONString(courseTimetableUnlockDetailVO));
        CourseConfig courseConfig = courseComponent.courseConfigDetail(dto.getCompanyId(), dto.getCourseId());
        log.info("syncDoWork courseConfig:{}", JSON.toJSONString(courseConfig));
        // 校验写作业时间
        this.checkWorkTime(courseTimetableUnlockDetailVO, courseConfig);
        WorkInfo workInfo = workInfoMapper.queryOneWork(dto.getUserId(), dto.getCourseId(), dto.getCompanyId(), dto.getCourseTimeTableId());
        log.info("syncDoWork workInfo:{}", JSON.toJSONString(workInfo));
        if (Objects.nonNull(workInfo) && Objects.equals(workInfo.getWorkStatus(), 1)) {
            log.info("syncDoWork 已提交作业,userId_{}, courseId_{}, companyId_{}, courseTimeTableId_{}", dto.getUserId(), dto.getCourseId(), dto.getCompanyId(), dto.getCourseTimeTableId());
            throw new BusinessException("已提交作业");
        }
        Jedis jedis = JedisPoolImpl.getResource();
        JedisDistributedLock lock = new JedisDistributedLock(jedis, WorkKafkaMessageEnum.DO_WORK.getCode() + dto.getCompanyId() + "-" + dto.getCourseTimeTableId() + "-" + dto.getUserId(), 10000, 5000);// acquireTimeoutMillis:超过该时间没有获取到锁，直接返回false; expiryTimeMillis:锁过期时间)
        try {
            if (!lock.acquireEp()) {
                log.info("syncDoWork 加锁失败,userId_{}, courseId_{}, companyId_{}, courseTimeTableId_{}", dto.getUserId(), dto.getCourseId(), dto.getCompanyId(), dto.getCourseTimeTableId());
                throw new BusinessException("提交作业失败，请重新提交");
            }
            log.info("syncDoWork 加锁成功,userId_{}, courseId_{}, companyId_{}, courseTimeTableId_{}", dto.getUserId(), dto.getCourseId(), dto.getCompanyId(), dto.getCourseTimeTableId());
            AtomicReference<Integer> score = new AtomicReference<>(0);
            UserStudyInfo userStudyInfo = this.getUserStudyInfo(dto.getUserId(), dto.getCourseId(), dto.getCompanyId());
            long l2 = currentTimeMillis();
            if (Objects.isNull(workInfo)) {
                workInfo = this.workInfoBuild(courseTimetableUnlockDetailVO, dto.getUserId(), dto.getCourseId(), dto.getCompanyId());
                log.info("syncDoWork,作业在表里不存在,准备创建,build:{}", JSON.toJSONString(workInfo));
            }
            workInfo.setWorkModelFlag(courseTimetableUnlockDetailVO.getWorkModelFlag());
            boolean isOldFlag = getIsOldFlag(courseTimetableUnlockDetailVO.getWorkModelFlag(), dto.getUserId(), dto.getCourseId());
            workInfo.setUserFlag(isOldFlag ? 1 : 0);
            WorkUserModel workUserModel = getWorkUserModel(courseTimetableUnlockDetailVO.getWorkModelFlag(), dto.getUserId(), dto.getCourseId());
            // 如果已经有分或者已写过作业,就不能修改模板等级了
            workInfo.setLevel((Objects.equals(workInfo.getWorkStatus(), 1) || workInfo.getScore() > 0) && StrUtil.isNotBlank(workInfo.getLevel())
                    ? workInfo.getLevel() : Objects.nonNull(workUserModel) && StringUtils.isNotBlank(workUserModel.getLevel()) ? workUserModel.getLevel() : "");
            if (WorkModelFlagEnum.UNIFY.getCode().equals(workInfo.getWorkModelFlag()) && Objects.equals(courseTimetableUnlockDetailVO.getPlanFlag(), 1)) {
                workInfo.setLevel(courseTimetableUnlockDetailVO.getPlanSchemeOption());
            }
            Integer workModelId = getSimpleWorkModelIdByCourseTimetableUnlockDetailVO(courseTimetableUnlockDetailVO, isOldFlag, workUserModel, workInfo);
            workInfo.setWorkModelId(workModelId);
            // 获取班级小组信息
            ClassesTeamDetailVO classesTeamInfoByUser = this.getClassesTeamInfoByUser(dto.getCompanyId(), dto.getCourseId(), dto.getUserId());
            DoWorkSendKafkaBO doWorkSendKafkaBO = this.workSendKafkaBoBuild(dto.getCompanyId(), classesTeamInfoByUser.getClassesId(), classesTeamInfoByUser.getTeamId(), dto.getUserId(), dto.getCourseId(), dto.getCourseTimeTableId(), false, true);
            Map<Integer, WorkModelDetailVO> workModelDetailVOMap = queryWorkModelDetailList(workInfo.getWorkModelId(), dto.getCourseId(), courseTimetableUnlockDetailVO)
                    .stream().collect(Collectors.toMap(WorkModelDetailVO::getId, workModelDetailVO -> workModelDetailVO));

            // **核心** 计算作业项各项信息
            List<WorkModelDetailVO> workInfoDetailList = new ArrayList<>();
            calculateWorkModel(dto, workInfo, score, workInfoDetailList, workModelDetailVOMap);

            if (dto.getCompanyId().equals(-1)) {
                if (YesOrNoEnum.YES.getCode().equals(courseConfig.getExtendStudySwitch())) {
                    AdditionalInfoVO additionalInfoVO = oldServerComponent.additionalInfo(dto.getCourseId(), courseTimetableUnlockDetailVO.getWorkBeginTime().toLocalDate());
                    if (Objects.nonNull(additionalInfoVO)) {
                        additionalInfoVO.setStatus(0);
                        additionalInfoVO.setAdditionalScore(0);
                        workInfo.setAdditional(JSON.toJSONString(additionalInfoVO));
                    }
                }
            }
            if (workInfo.getCompanyId() > 0) {
                Map<Integer, String> map = userComponent.queryUserCompanyNameById(workInfo.getCompanyId(), Arrays.asList(workInfo.getUserId()));
                String userName = map.get(workInfo.getUserId());
                if (StrUtil.isNotBlank(userName)) {
                    workInfo.setUserName(userName);
                }
            }
            LocalDateTime now = LocalDateTime.now();
            workInfo.setResourceTitle(courseTimetableUnlockDetailVO.getResourceTitle());
            workInfo.setWorkStatus(WorkStatusEnum.OK.getCode());
            workInfo.setScore(workInfo.getScore() + score.get());
            workInfo.setWorkTime(now);
            workInfo.setContent(JSONObject.toJSONString(workInfoDetailList));
            workInfo.setUpdateTime(now);
            workInfo.setRecommendStatus(RecommendStatusEnums.NO.getCode());
            workInfo.setTeacherReplyStatus(0);
            workInfo.setRecommendTeacherStatus(0);
            long l3 = currentTimeMillis();
            workInfoMapper.saveOrUpdate(workInfo);
            workInfoPojoMongoService.saveOrUpdate(workInfo);
            userStudyInfo.setScore(userStudyInfo.getScore() + score.get());
            userStudyInfoMapper.updateById(userStudyInfo);
            doWorkSendKafkaBO.setScore(workInfo.getScore());
            doWorkSendKafkaBO.setTag(WorkKafkaMessageEnum.DO_WORK.getCode());
            String doWorkSendKafkaBOStr = JSONObject.toJSONString(doWorkSendKafkaBO);
            log.info("syncDoWork doWork,userId_{}, courseId_{}, companyId_{}, courseTimeTableId_{}, kafka消息内容:{}",
                    dto.getUserId(), dto.getCourseId(), dto.getCompanyId(), dto.getCourseTimeTableId(), doWorkSendKafkaBOStr);
            kafkaProduction.send(workTopic, String.valueOf(doWorkSendKafkaBO.getUserId()), JSONObject.parseObject(doWorkSendKafkaBOStr));
            if (Objects.equals(workInfo.getListenStatus(), 1)) {
                // 听课打卡
                doWorkSendKafkaBO.setTag(WorkKafkaMessageEnum.AUTO_LISTENED.getCode());
                doWorkSendKafkaBO.setListenStatus(true);
                doWorkSendKafkaBOStr = JSONObject.toJSONString(doWorkSendKafkaBO);
                log.info("syncDoWork listenedUp,userId_{}, courseId_{}, companyId_{}, courseTimeTableId_{}, kafka消息内容:{}",
                        dto.getUserId(), dto.getCourseId(), dto.getCompanyId(), dto.getCourseTimeTableId(), doWorkSendKafkaBOStr);
                kafkaProduction.send(workTopic, String.valueOf(doWorkSendKafkaBO.getUserId()), JSONObject.parseObject(doWorkSendKafkaBOStr));
            }
            long l4 = currentTimeMillis();
            log.info("耗时统计 l2-l1:[" + (l2 - l1) + "]  l3-l2[" + (l3 - l2) + "] l4-l3 [" + (l4 - l3));
            generateRank(doWorkSendKafkaBO);
            // 发送消息，用来在企业空间展示
            workSendMessageForCompanyZone(workInfo);
        } catch (InterruptedException e) {
            log.error("syncDoWork 报错,userId_{}, courseId_{}, companyId_{}, courseTimeTableId_{},error:{}",
                    dto.getUserId(), dto.getCourseId(), dto.getCompanyId(), dto.getCourseTimeTableId(), e.getMessage(), e);
            throw new BusinessException("提交作业失败，请重新提交");
        } finally {
            lock.release();
        }
    }

    private void workSendMessageForCompanyZone(WorkInfo workInfo) {
        CompletableFuture.runAsync(() -> {
            CompanyUserDynamicsBO companyUserDynamicsBO = new CompanyUserDynamicsBO();
            companyUserDynamicsBO.setUserId(workInfo.getUserId());
            companyUserDynamicsBO.setUserName(workInfo.getUserName());
            companyUserDynamicsBO.setContent("提交了《" + workInfo.getResourceTitle() + "》");
            companyUserDynamicsBO.setType(4);
            kafkaProduction.send(companyUserDynamicsTopic, companyUserDynamicsBO);
        }, quicklyExecutor);
    }

    private void listenSendMessageForCompanyZone(WorkInfo workInfo, CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO) {
        CompletableFuture.runAsync(() -> {
            CompanyUserDynamicsBO companyUserDynamicsBO = new CompanyUserDynamicsBO();
            companyUserDynamicsBO.setUserId(workInfo.getUserId());
            companyUserDynamicsBO.setUserName(workInfo.getUserName());
            companyUserDynamicsBO.setContent("听了《" + courseTimetableUnlockDetailVO.getResourceTitle() + "》");
            companyUserDynamicsBO.setType(1);
            kafkaProduction.send(companyUserDynamicsTopic, companyUserDynamicsBO);
        }, quicklyExecutor);
    }

    private void calculateWorkModel(DoWorkMsgDTO dto, WorkInfo workInfo, AtomicReference<Integer> score, List<WorkModelDetailVO> workInfoDetailList, Map<Integer, WorkModelDetailVO> workModelDetailVOMap) {
        for (DoWorkDetailDTO workDetailDTO : dto.getWorkDetails()) {
            if (Objects.isNull(workDetailDTO) || Objects.isNull(workDetailDTO.getId())) {
                continue;
            }
            WorkModelDetailVO workModelDetailVO = workModelDetailVOMap.get(workDetailDTO.getId());
            if (Objects.isNull(workModelDetailVO)) {
                continue;
            }
            WorkModelDetailVO workInfoDetail = new WorkModelDetailVO();
            BeanUtils.copyProperties(workModelDetailVO, workInfoDetail);
            workInfoDetailList.add(workInfoDetail);
            workInfoDetail.setContent(workDetailDTO.getContent());
            workInfoDetail.setScore(0);
            if (LISTEN.getCode().equals(workDetailDTO.getType())) {
                if (Objects.equals(workInfo.getListenStatus(), 1)) {
                    workInfoDetail.setContent("1");
                    workInfoDetail.setScore(workModelDetailVO.getScore());
                } else {
                    // 没有进行过听课加分,且现在是手动加分
                    if ("1".equals(workDetailDTO.getContent())) {
                        workInfoDetail.setContent("1");
                        workInfoDetail.setScore(workModelDetailVO.getScore());
                        score.set(score.get() + workModelDetailVO.getScore());
                        workInfo.setListenStatus(1);
                    }
                }
            } else if (SHARE.getCode().equals(workDetailDTO.getType())) {
                if (Objects.equals(workInfo.getShareStatus(), 1)) {
                    workInfoDetail.setContent("1");
                    workInfoDetail.setScore(workModelDetailVO.getScore());
                }
            } else if (WorkDetailTypeEnum.CLOCK.getCode().equals(workDetailDTO.getType())) {
                workInfoDetail.setShowStatus(YesOrNoEnum.NO.getCode());
                if ("1".equals(workDetailDTO.getContent())) {
                    workInfoDetail.setScore(workModelDetailVO.getScore());
                    score.set(score.get() + workInfoDetail.getScore());
                }
            } else if (WorkDetailTypeEnum.TEXT.getCode().equals(workDetailDTO.getType()) || DUSING.getCode().equals(workModelDetailVO.getType())) {
                if (StringUtils.isNotBlank(workDetailDTO.getContent())) {
                    if (workInfoDetail.getContent().length() >= workModelDetailVO.getMinLength()) {
                        workInfoDetail.setScore(workModelDetailVO.getScore());
                        score.set(score.get() + workInfoDetail.getScore());
                    }
                    if (StringUtils.isNotBlank(workDetailDTO.getContent()) && YesOrNoEnum.YES.getCode().equals(workModelDetailVO.getShowStatus())) {
                        workInfoDetail.setShowStatus(YesOrNoEnum.YES.getCode());
                        workInfo.setContentShowStatus(YesOrNoEnum.YES.getCode());
                        if (StringUtils.isBlank(workInfoDetail.getShowTitle())) {
                            workInfoDetail.setShowTitle(workInfoDetail.getTitle());
                        }
                    } else {
                        workInfoDetail.setShowStatus(YesOrNoEnum.NO.getCode());
                    }
                } else {
                    workInfoDetail.setShowStatus(YesOrNoEnum.NO.getCode());
                }
            } else if (AUDIO_TEXT.getCode().equals(workDetailDTO.getType())) {
                workInfoDetail.setAudioList(workDetailDTO.getAudioList());
                if (StringUtils.isNotBlank(workDetailDTO.getContent()) || CollUtil.isNotEmpty(workDetailDTO.getAudioList())) {
                    if ((StringUtils.isNotBlank(workDetailDTO.getContent()) && workDetailDTO.getContent().length() >= workModelDetailVO.getMinLength())
                            || CollUtil.isNotEmpty(workDetailDTO.getAudioList())) {
                        workInfoDetail.setScore(workModelDetailVO.getScore());
                        score.set(score.get() + workInfoDetail.getScore());
                    }
                    if ((StringUtils.isNotBlank(workDetailDTO.getContent()) || CollUtil.isNotEmpty(workDetailDTO.getAudioList()))
                            && YesOrNoEnum.YES.getCode().equals(workModelDetailVO.getShowStatus())) {
                        workInfoDetail.setShowStatus(YesOrNoEnum.YES.getCode());
                        workInfo.setContentShowStatus(YesOrNoEnum.YES.getCode());
                        workInfoDetail.setShowTitle(StrUtil.isNotBlank(workModelDetailVO.getShowTitle()) ? workModelDetailVO.getShowTitle() : workModelDetailVO.getTitle());
                    } else {
                        workInfoDetail.setShowStatus(YesOrNoEnum.NO.getCode());
                    }
                } else {
                    workInfoDetail.setShowStatus(YesOrNoEnum.NO.getCode());
                }
            } else if (workDetailDTO.getType().equals(WorkDetailTypeEnum.NUMBER.getCode()) || WorkDetailTypeEnum.NPS.getCode().equals(workDetailDTO.getType())) {
                workInfoDetail.setScore(workModelDetailVO.getScore() * Integer.parseInt(StringUtils.isNotBlank(workDetailDTO.getContent()) && Pattern.matches("\\d+", workDetailDTO.getContent()) ? workDetailDTO.getContent() : "0"));
                workInfoDetail.setContent(StringUtils.isNotBlank(workDetailDTO.getContent()) ? workDetailDTO.getContent() : "0");
                score.set(score.get() + workInfoDetail.getScore());
                workInfoDetail.setShowStatus(YesOrNoEnum.NO.getCode());
            } else if (WorkDetailTypeEnum.CHOICE.getCode().equals(workDetailDTO.getType()) || MULTIPLE_CHOICE.getCode().equals(workDetailDTO.getType())) {
                if (StringUtils.isNotBlank(workDetailDTO.getContent())) {
                    workInfoDetail.setScore(workModelDetailVO.getScore());
                    score.set(score.get() + workInfoDetail.getScore());
                }
                workInfoDetail.setShowStatus(YesOrNoEnum.NO.getCode());
            } else if (CLOCK_NOUN_SCORE.getCode().equals(workDetailDTO.getType()) || NOUN_SCORE.getCode().equals(workDetailDTO.getType()) || EVALUATE.getCode().equals(workModelDetailVO.getType())) {
                workInfoDetail.setShowStatus(YesOrNoEnum.NO.getCode());
            } else if (DYNAMIC_MULTIPLE_CHOICE.getCode().equals(workDetailDTO.getType())) {
                workInfoDetail.setShowStatus(YesOrNoEnum.NO.getCode());
                workInfoDetail.setAppOptionList(workDetailDTO.getAppOptionList());
                if (CollUtil.isNotEmpty(workDetailDTO.getAppOptionList())) {
                    boolean hasScore = false;
                    for (AppOptionVO appOptionVO : workDetailDTO.getAppOptionList()) {
                        if (Objects.equals(appOptionVO.getChecked(), 1)) {
                            hasScore = true;
                            break;
                        }
                    }
                    if (hasScore) {
                        workInfoDetail.setScore(workModelDetailVO.getScore());
                        score.set(score.get() + workInfoDetail.getScore());
                    }
                }
            } else if (DYNAMIC_SINGLE_CHOICE.getCode().equals(workDetailDTO.getType())) {
                workInfoDetail.setShowStatus(YesOrNoEnum.NO.getCode());
                workInfoDetail.setAppOptionList(workDetailDTO.getAppOptionList());
                if (CollUtil.isNotEmpty(workDetailDTO.getAppOptionList())) {
                    for (AppOptionVO appOptionVO : workDetailDTO.getAppOptionList()) {
                        if (Objects.equals(appOptionVO.getChecked(), 1)) {
                            workInfoDetail.setScore(workModelDetailVO.getScore());
                            score.set(score.get() + workInfoDetail.getScore());
                            break;
                        }
                    }
                }
            } else if (READ.getCode().equals(workDetailDTO.getType())) {
                if (workDetailDTO.getReadRecordId() != null && workDetailDTO.getReadRecordId() == 0) {
                    workDetailDTO.setReadRecordId(null);
                }
                if ("1".equals(workDetailDTO.getContent())) {
                    workInfoDetail.setContent("1");
                    workInfoDetail.setScore(workModelDetailVO.getScore());
                    if (workDetailDTO.getReadRecordId() == null) {
                        score.set(score.get() + workInfoDetail.getScore());
                    }
                    workInfoDetail.setReadRecordId(workDetailDTO.getReadRecordId());
                }
                if (YesOrNoEnum.YES.getCode().equals(workModelDetailVO.getShowStatus())) {
                    workInfoDetail.setShowStatus(YesOrNoEnum.YES.getCode());
                    workInfo.setContentShowStatus(YesOrNoEnum.YES.getCode());
                } else {
                    workInfoDetail.setShowStatus(YesOrNoEnum.NO.getCode());
                }
            } else {
                if ("1".equals(workDetailDTO.getContent())) {
                    workInfoDetail.setContent("1");
                    workInfoDetail.setScore(workModelDetailVO.getScore());
                    score.set(score.get() + workInfoDetail.getScore());
                } else {
                    workDetailDTO.setContent("0");
                }
                workInfoDetail.setShowStatus(YesOrNoEnum.NO.getCode());
            }
        }
    }

    public DoWorkSendKafkaBO workSendKafkaBoBuild(Integer companyId, Integer classesId, Integer teamId, Integer userId, Integer courseId, Integer courseTimetableId, Boolean listenStatus, Boolean workStatus) {
        return DoWorkSendKafkaBO.builder()
                .teamId(teamId)
                .classesId(classesId)
                .listenStatus(listenStatus)
                .workStatus(workStatus)
                .userId(userId)
                .companyId(companyId)
                .courseTimeTableId(courseTimetableId)
                .ts(currentTimeMillis() / 1000)
                .courseId(courseId)
                .build();
    }

    public ClassesTeamDetailVO getClassesTeamInfoByUser(Integer companyId, Integer courseId, Integer userId) {
        ClassesTeamDetailVO classesTeamInfoByUser = classesComponent.listenGetClassesTeamInfoByUser(companyId, courseId, userId);
        if (Objects.isNull(classesTeamInfoByUser)) {
            classesTeamInfoByUser = ClassesTeamDetailVO.builder()
                    .classesId(0)
                    .classesNo("0")
                    .teamId(0)
                    .build();
        }
        return classesTeamInfoByUser;
    }

    @Override
    public PageInfoBT<MyWorkVO> myWork(MyWorkDTO myWorkDto, HeaderUserInfo headerUserInfo) {
        IPage<WorkInfoPojo> workInfoPage = workInfoPojoMongoService.querySimplePage(new WorkInfoPojoQueryDTO()
                .setPageNum(myWorkDto.getPageNum()).setPageSize(myWorkDto.getPageSize())
                .setUserId(headerUserInfo.getId()).setCourseId(myWorkDto.getCourseId())
                .setCompanyId(headerUserInfo.getCurrentCompanyId()));
        if (CollUtil.isEmpty(workInfoPage.getRecords())) {
            return PageInfoBT.noData();
        }
        return PageInfoBT.fromPage(workInfoPage.convert(workInfo -> {
            MyWorkVO myWorkVO = new MyWorkVO();
            myWorkVO.setScore(workInfo.getScore());
            myWorkVO.setDoWorkTime(workInfo.getWorkTime());
            List<MyWorkDetailVO> myWorkDetailVOS = new ArrayList<>();
            if (StringUtils.isNotBlank(workInfo.getContent())) {
                myWorkDetailVOS = workContentParse(workInfo.getContent()).stream().map(workModelDetailVO ->
                        MyWorkDetailVO.builder()
                                .content(workModelDetailVO.getContent())
                                .workId(workInfo.getWorkId())
                                .description(workModelDetailVO.getDescription())
                                .prompt(workModelDetailVO.getPrompt())
                                .title(workModelDetailVO.getTitle())
                                .type(workModelDetailVO.getType())
                                .score(workModelDetailVO.getScore())
                                .words(workModelDetailVO.getWords())
                                .answer(workModelDetailVO.getAnswer())
                                .shareImageUrl(workModelDetailVO.getShareImageUrl())
                                .shareStatus(workModelDetailVO.getShareStatus())
                                .build()).collect(Collectors.toList());
            }
            myWorkVO.setWorkDetails(myWorkDetailVOS);
            return myWorkVO;
        }));


    }

    @Override
    public MyScoreVO myScore(MyScoreDTO myScoreDTO, HeaderUserInfo headerUserInfo) {
        QueryWrapper<UserStudyInfo> userStudyInfoQueryWrapper = new QueryWrapper<>();
        userStudyInfoQueryWrapper
                .lambda()
                .eq(UserStudyInfo::getUserId, headerUserInfo.getId())
                .eq(UserStudyInfo::getCourseId, myScoreDTO.getCourseId())
                .last("limit 1");
        UserStudyInfo userStudyInfo = userStudyInfoMapper.selectOne(userStudyInfoQueryWrapper);
        return MyScoreVO
                .builder()
                .score(Objects.nonNull(userStudyInfo) && Objects.nonNull(userStudyInfo.getScore()) ? userStudyInfo.getScore() : 0)
                .username(headerUserInfo.getUserFullName())
                .build();
    }

    @Override
    public ClassStatisticsVO classStatistics(ClassStatisticsDTO dto, HeaderUserInfo headerUserInfo) {
        ClassStatisticsVO classStatisticsVO = new ClassStatisticsVO();
        CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(dto.getCourseId(), null);
        if (Objects.isNull(courseTimetableUnlockDetailVO)) {
            log.info("classStatistics courseTimetableUnlockDetailVO is null.");
            return this.companyClassStatisticsBuild(courseTimetableUnlockDetailVO, classStatisticsVO);
        }
        UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(dto.getCourseId(), headerUserInfo.getId());
        if (userRightsCourse == null) {
            log.info("没有课程权限");
            throw new BusinessException("暂无权限");
        } else {
            headerUserInfo.setCurrentCompanyId(userRightsCourse.getCompanyId());
        }
        ClassStatisticsVO historyClassStatistics = getHistoryClassStatistics(dto, headerUserInfo, courseTimetableUnlockDetailVO);
        ClassStatisticsVO classStatisticsCurrent = classStudyStatisticsMapper.classStatistics(dto.getClassesId(), dto.getClassesNo(), dto.getCourseId(), headerUserInfo.getId(), courseTimetableUnlockDetailVO.getId(), headerUserInfo.getCurrentCompanyId());
        if (Objects.nonNull(classStatisticsCurrent)) {
            BeanUtil.copyProperties(classStatisticsCurrent, classStatisticsVO);
            classStatisticsVO.setHistoryWorkRate(historyClassStatistics.getHistoryWorkRate());
            classStatisticsVO.setHistoryListenRate(historyClassStatistics.getHistoryListenRate());
        }
        return this.companyClassStatisticsBuild(courseTimetableUnlockDetailVO, classStatisticsVO);
    }

    private ClassStatisticsVO getHistoryClassStatistics(ClassStatisticsDTO dto, HeaderUserInfo headerUserInfo, CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO) {
        String redisKey = RedisConstants.CLASS_STATISTICS_NEW + courseTimetableUnlockDetailVO.getId() + ":" + headerUserInfo.getCurrentCompanyId() + ":" + dto.getClassesId();
        String classStatisticsStr = redisUtil.get(redisKey, String.class);
        log.info("classStatistics get key:[{}],classStatisticsStr:{}", redisKey, classStatisticsStr);
        ClassStatisticsVO historyClassStatistics;
        if (StrUtil.isBlank(classStatisticsStr)) {
            historyClassStatistics = classStudyStatisticsMapper.classStatisticsHistory(dto.getClassesId(), dto.getClassesNo(), dto.getCourseId(), headerUserInfo.getId(), Objects.nonNull(courseTimetableUnlockDetailVO.getId()) ? courseTimetableUnlockDetailVO.getId() : null, headerUserInfo.getCurrentCompanyId());
            classStatisticsStr = JSON.toJSONString(historyClassStatistics);
            log.info("classStatistics set key:[{}],classStatisticsStr:{}", redisKey, classStatisticsStr);
            redisUtil.set(redisKey, classStatisticsStr, 24, TimeUnit.HOURS);
        } else {
            historyClassStatistics = JSON.parseObject(classStatisticsStr, ClassStatisticsVO.class);
        }
        return historyClassStatistics;
    }

    public ClassStatisticsVO companyClassStatisticsBuild(CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO, ClassStatisticsVO classStatisticsVO) {
        ClassStatisticsVO vo = ClassStatisticsVO
                .builder()
                .courseWareName(Objects.nonNull(courseTimetableUnlockDetailVO) ? courseTimetableUnlockDetailVO.getResourceTitle() : "")
                .workStartTime(Objects.nonNull(courseTimetableUnlockDetailVO) ? courseTimetableUnlockDetailVO.getWorkBeginTime() : null)
                .workEndTime(Objects.nonNull(courseTimetableUnlockDetailVO) ? courseTimetableUnlockDetailVO.getWorkEndTime() : null)
                .historyListenRate(Objects.nonNull(classStatisticsVO) && StringUtils.isNotBlank(classStatisticsVO.getHistoryListenRate()) ? classStatisticsVO.getHistoryListenRate() : "0.00")
                .historyWorkRate(Objects.nonNull(classStatisticsVO) && StringUtils.isNotBlank(classStatisticsVO.getHistoryWorkRate()) ? classStatisticsVO.getHistoryWorkRate() : "0.00")
                .listenRate(Objects.nonNull(classStatisticsVO) && StringUtils.isNotBlank(classStatisticsVO.getListenRate()) ? classStatisticsVO.getListenRate() : "0.00")
                .workRate(Objects.nonNull(classStatisticsVO) && StringUtils.isNotBlank(classStatisticsVO.getWorkRate()) ? classStatisticsVO.getWorkRate() : "0.00")
                .unsubUserName(StringUtils.isNotBlank(classStatisticsVO.getUnsubUserNames()) ? Arrays.asList(classStatisticsVO.getUnsubUserNames().split(",")) : new ArrayList<>())
                .build();
        log.info("companyClassStatisticsBuild vo:{}", JSON.toJSONString(vo));
        return vo;
    }

    @Override
    public List<WorkingByDateVO> classWorkingByDate(WorkingByDateDTO dto, HeaderUserInfo headerUserInfo) {
        dto.setPageNum(1);
        dto.setPageSize(30000);
        PageInfoBT<WorkingByDateVO> page = classWorkingByDatePage(dto, headerUserInfo);
        if (CollUtil.isEmpty(page.getRecords())) {
            return new ArrayList<>();
        }
        return page.getRecords();
    }

    @Override
    public List<TeamStatisticsVO> teamStatistics(TeamStatisticsDTO dto, HeaderUserInfo headerUserInfo) {
        UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(dto.getCourseId(), headerUserInfo.getId());
        headerUserInfo.setCurrentCompanyId(userRightsCourse.getCompanyId());
        List<CourseTimetableHistoryVO> courseHistoryTimetables = courseComponent.getCourseHistoryTimetable(dto.getCourseId());
        if (courseHistoryTimetables.isEmpty()) {
            return new ArrayList<>();
        }
        CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(dto.getCourseId(), null);

        if (Objects.nonNull(courseTimetableUnlockDetailVO)) {
            courseHistoryTimetables = courseHistoryTimetables.stream().filter(courseTimetableHistoryVO -> !courseTimetableHistoryVO.getId().equals(courseTimetableUnlockDetailVO.getId())).collect(Collectors.toList());
        }

        if (Objects.nonNull(dto.getTeamId())) {
            Map<Integer, TeamStatisticsVO> teamStatisticsVOMap = workInfoMapper.teamStatistics(dto, Objects.nonNull(courseTimetableUnlockDetailVO) && courseTimetableUnlockDetailVO.getWorkEndTime().isAfter(LocalDateTime.now()) ? courseTimetableUnlockDetailVO.getId() : 0, headerUserInfo.getCurrentCompanyId())
                    .stream().collect(Collectors.toMap(TeamStatisticsVO::getCourseTimeTableId, teamStatisticsVO -> teamStatisticsVO));
            return courseHistoryTimetables.stream().map(courseHistoryTimetable -> {
                TeamStatisticsVO teamStatistics = teamStatisticsVOMap.get(courseHistoryTimetable.getId());
                if (Objects.nonNull(teamStatistics)) {
                    return teamStatistics;
                } else {
                    return TeamStatisticsVO.builder()
                            .belongDate(courseHistoryTimetable.getCourseBeginTime().toLocalDate())
                            .classesId(dto.getClassesId())
                            .workNum(0)
                            .listenNum(0)
                            .score(0)
                            .build();
                }
            }).collect(Collectors.toList());
        } else {
            Map<Integer, TeamStatisticsVO> teamStatisticsVOMap = workInfoMapper.classesStatistics(dto, Objects.nonNull(courseTimetableUnlockDetailVO) && courseTimetableUnlockDetailVO.getWorkEndTime().isAfter(LocalDateTime.now()) ? courseTimetableUnlockDetailVO.getId() : 0, headerUserInfo.getCurrentCompanyId()).stream().collect(Collectors.toMap(TeamStatisticsVO::getCourseTimeTableId, teamStatisticsVO -> teamStatisticsVO));
            return courseHistoryTimetables.stream().map(courseHistoryTimetable -> {
                TeamStatisticsVO teamStatistics = teamStatisticsVOMap.get(courseHistoryTimetable.getId());
                if (Objects.nonNull(teamStatistics)) {
                    return teamStatistics;
                } else {
                    return TeamStatisticsVO.builder()
                            .belongDate(courseHistoryTimetable.getCourseBeginTime().toLocalDate())
                            .classesId(dto.getClassesId())
                            .workNum(0)
                            .listenNum(0)
                            .score(0)
                            .build();
                }
            }).collect(Collectors.toList());
        }
    }

    @Override
    public UserStudyStatisticsVO userStudyStatistics(UserStudyStatisticsDTO userStudyStatisticsDTO, HeaderUserInfo headerUserInfo) throws ExecutionException, InterruptedException {
        long l1 = System.currentTimeMillis();
        UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(userStudyStatisticsDTO.getCourseId(), userStudyStatisticsDTO.getUserId());
        if (userRightsCourse == null) {
            log.info("没有课程权限");
            throw new BusinessException("暂无权限");
        } else {
            headerUserInfo.setCurrentCompanyId(userRightsCourse.getCompanyId());
        }
        UserInfoCompanySearchVO userInfoCompanySearchVO = userComponent.userByUserIdCompanyId(headerUserInfo.getCurrentCompanyId(), userStudyStatisticsDTO.getUserId());
        long l2 = System.currentTimeMillis();
        if (Objects.isNull(userInfoCompanySearchVO)) {
            return null;
        }
        CompletableFuture<ClassesTeamDetailVO> classesTeamInfoByUserCF = CompletableFuture.supplyAsync(() ->
                classesComponent.getClassesTeamInfoByUser(headerUserInfo.getCurrentCompanyId(), userStudyStatisticsDTO.getCourseId(), userStudyStatisticsDTO.getUserId()), quicklyExecutor);
        UserStudyInfo userStudyInfo = userStudyInfoMapper.selectOne(new LambdaQueryWrapper<UserStudyInfo>()
                .eq(UserStudyInfo::getCourseId, userStudyStatisticsDTO.getCourseId())
                .eq(UserStudyInfo::getCompanyId, headerUserInfo.getCurrentCompanyId())
                .eq(UserStudyInfo::getUserId, userStudyStatisticsDTO.getUserId())
                .last("limit 1"));
        CompletableFuture<List<UserStudyStatisticsDetailVO>> listCF = CompletableFuture.supplyAsync(() ->
                workInfoPojoMongoService.userStudyStatistics(userStudyStatisticsDTO.getUserId(), userStudyStatisticsDTO.getCourseId(), headerUserInfo.getCurrentCompanyId()), quicklyExecutor);
        CompletableFuture<List<CourseTimetableHistoryVO>> courseHistoryTimetablesCF = CompletableFuture.supplyAsync(() -> courseComponent.getCourseHistoryTimetable(userStudyStatisticsDTO.getCourseId()), quicklyExecutor);
        long l3 = System.currentTimeMillis();
        CompletableFuture.allOf(classesTeamInfoByUserCF, listCF, courseHistoryTimetablesCF);
        long l4 = System.currentTimeMillis();
        ClassesTeamDetailVO classesTeamInfoByUser = classesTeamInfoByUserCF.get();
        List<UserStudyStatisticsDetailVO> list = listCF.get();
        List<CourseTimetableHistoryVO> courseHistoryTimetables = courseHistoryTimetablesCF.get();
        long l5 = System.currentTimeMillis();
        Map<Integer, UserStudyStatisticsDetailVO> userStudyStatisticsDetailVOMap = list.stream().collect(Collectors.toMap(UserStudyStatisticsDetailVO::getCourseTimetableId, userStudyStatisticsDetailVO -> userStudyStatisticsDetailVO));
        log.info("userStudyStatistics 耗时统计:l2 - l1:{}, l3 - l2:{}, l4 - l3:{}, l5 - l4:{}", l2 - l1, l3 - l2, l4 - l3, l5 - l4);
        return UserStudyStatisticsVO.builder()
                .avatar(userInfoCompanySearchVO.getAvatar())
                .score(Objects.nonNull(userStudyInfo) ? userStudyInfo.getScore() : 0)
                .userPhone(userInfoCompanySearchVO.getPhone())
                .userId(userStudyStatisticsDTO.getUserId())
                .username(StringUtils.isNotBlank(userInfoCompanySearchVO.getRealName()) ? userInfoCompanySearchVO.getRealName() : userInfoCompanySearchVO.getUserFullName())
                .teamName(Objects.nonNull(classesTeamInfoByUser) ? classesTeamInfoByUser.getTeamName() : "")
                .userStudyDetails(courseHistoryTimetables.size() > 0 ? courseHistoryTimetables.stream().map(courseTimetable -> {
                    UserStudyStatisticsDetailVO userStudyStatisticsDetailVO = userStudyStatisticsDetailVOMap.get(courseTimetable.getId());
                    if (Objects.nonNull(userStudyStatisticsDetailVO)) {
                        return userStudyStatisticsDetailVO;
                    } else {
                        return UserStudyStatisticsDetailVO
                                .builder()
                                .workScore(0)
                                .belongDate(courseTimetable.getCourseBeginTime().toLocalDate())
                                .listenStatus(false)
                                .workStatus(false)
                                .build();
                    }
                }).collect(Collectors.toList()) : new ArrayList<>())
                .build();
    }

    @Override
    public WorkStatusInfoVO workStatusInfo(WorkStatusInfoDTO workStatusInfoDTO) {
        WorkInfoPojoQueryDTO workInfoPojoQueryDTO = new WorkInfoPojoQueryDTO().setUserId(workStatusInfoDTO.getUserId())
                .setCourseTimetableId(workStatusInfoDTO.getCourseTimeTableId());
        if (Objects.nonNull(workStatusInfoDTO.getCompanyId())) {
            workInfoPojoQueryDTO.setCompanyId(workInfoPojoQueryDTO.getCompanyId());
        } else {
            workInfoPojoQueryDTO.setCompanyId(-1);
        }
        WorkInfoPojo workInfo = workInfoPojoMongoService.querySimpleOne(workInfoPojoQueryDTO);
        if (Objects.nonNull(workInfo)) {
            if (WorkStatusEnum.OK.getCode().equals(workInfo.getWorkStatus())) {
                return WorkStatusInfoVO.builder()
                        .workStatus(true)
                        .score(workInfo.getScore())
                        .build();
            } else {
                return WorkStatusInfoVO.builder()
                        .workStatus(false)
                        .score(workInfo.getScore())
                        .build();
            }
        } else {
            return WorkStatusInfoVO.builder()
                    .workStatus(false)
                    .score(0)
                    .build();
        }
    }

    @Override
    public PageInfoBT<WorkListVO> workList(UserWorkListDTO dto) {
        List<CourseTimetable> courseTimetableList = new ArrayList<>();
        if (StringUtils.isNotBlank(dto.getResourceTitle())) {
            courseTimetableList = courseComponent.getCourseTimetableListByName(dto.getCourseId(), dto.getResourceTitle());
        }
        List<ClassTeacherVO> classTeacherList = new ArrayList<>();
        if (StringUtils.isNotBlank(dto.getClassesTeacherName())) {
            classTeacherList = classesComponent.getClassTeachers(dto.getClassesTeacherName(), Integer.valueOf(dto.getCourseId()), Integer.valueOf(dto.getCompanyId()));
            if (CollUtil.isEmpty(classTeacherList)) {
                return PageInfoBT.from(0, new ArrayList<>());
            }
        }
        IPage<WorkInfoPojo> workInfoPage = workInfoPojoMongoService.saasWorkList(dto, courseTimetableList, classTeacherList);
        if (workInfoPage.getRecords().size() < 1) {
            return PageInfoBT.from(0, new ArrayList<>());
        }
        List<String> classTeacherPhones = new ArrayList<>();
        List<Integer> userIds = new ArrayList<>();
        for (WorkInfoPojo workInfo : workInfoPage.getRecords()) {
            userIds.add(workInfo.getUserId());
            if (StringUtils.isNotBlank(workInfo.getClassesTeacherPhone())) {
                classTeacherPhones.add(workInfo.getClassesTeacherPhone());
            }
        }
        List<Integer> classTeacherIds = new ArrayList<>();
        Map<String, UserInfoSearchSimpleVO> userInfoSearchSimpleVOMap = new HashMap<>();
        if (classTeacherPhones.size() > 0) {
            UserInfoListSearchByPhoneDTO userInfoListSearchByPhoneDTO = new UserInfoListSearchByPhoneDTO();
            userInfoListSearchByPhoneDTO.setPhones(classTeacherPhones);
            classTeacherIds = userComponent.userListByPhone(userInfoListSearchByPhoneDTO).stream().map(UserInfoVO::getId).collect(Collectors.toList());
            if (classTeacherIds.size() > 0) {
                userInfoSearchSimpleVOMap = userComponent.userSimpleListByUserIds(classTeacherIds, StringUtils.isNotBlank(dto.getCompanyId()) ? Integer.valueOf(dto.getCompanyId()) : null).stream().collect(Collectors.toMap(UserInfoSearchSimpleVO::getPhone, userInfoSearchSimpleVO -> userInfoSearchSimpleVO));
            }
        }
        if (userIds.size() > 0) {
            Map<String, UserInfoSearchSimpleVO> userInfoMap = userComponent.userSimpleListByUserIds(classTeacherIds, StringUtils.isNotBlank(dto.getCompanyId()) ? Integer.valueOf(dto.getCompanyId()) : null).stream().collect(Collectors.toMap(UserInfoSearchSimpleVO::getPhone, userInfoSearchSimpleVO -> userInfoSearchSimpleVO));
            userInfoSearchSimpleVOMap.putAll(userInfoMap);
        }
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        Map<Integer, CourseTimetableHistoryVO> courseTimetableHistoryVOMap = courseComponent.getCourseHistoryTimetable(Integer.valueOf(dto.getCourseId())).stream().collect(Collectors.toMap(CourseTimetableHistoryVO::getId, courseTimetableHistoryVO -> courseTimetableHistoryVO));
        Map<String, UserInfoSearchSimpleVO> finalUserInfoSearchSimpleVOMap = userInfoSearchSimpleVOMap;
        return PageInfoBT.fromPage(workInfoPage.convert(workInfo -> {
            WorkListVO workListVO = new WorkListVO();
            BeanUtils.copyProperties(workInfo, workListVO);
            workListVO.setUserName(workInfo.getUserName());
            if (StringUtils.isNotBlank(workInfo.getContent())) {
                List<WorkModelDetailVO> workInfoDetails = workContentParse(workInfo.getContent());
                for (WorkModelDetailVO workInfoDetail : workInfoDetails) {
                    if (workInfoDetail.getType().equals(TEXT.getCode())
                            || workInfoDetail.getType().equals(DUSING.getCode())) {
                        if (StringUtils.isBlank(workListVO.getWorkContentOne())) {
                            workListVO.setWorkContentOne(workInfoDetail.getContent());
                        } else if (StringUtils.isBlank(workListVO.getWorkContentTwo())) {
                            workListVO.setWorkContentTwo(workInfoDetail.getContent());
                        }
                    }
                }
            }
            if (Objects.nonNull(finalUserInfoSearchSimpleVOMap.get(workInfo.getUserId().toString()))) {
                workListVO.setDept(finalUserInfoSearchSimpleVOMap.get(workInfo.getClassesTeacherPhone()).getDept());
            }
            if (StringUtils.isNotBlank(workInfo.getClassesTeacherPhone()) && Objects.nonNull(finalUserInfoSearchSimpleVOMap.get(workInfo.getClassesTeacherPhone()))) {
                workListVO.setClassesTeacherName(finalUserInfoSearchSimpleVOMap.get(workInfo.getClassesTeacherPhone()).getUserFullName());
            }
            if (Objects.nonNull(workInfo.getCourseBeginTime())) {
                workListVO.setBelongDate(workInfo.getCourseBeginTime().toLocalDate());
            }
            if (Objects.nonNull(courseTimetableHistoryVOMap.get(workInfo.getCourseTimetableId()))) {
                workListVO.setResourceTitle(courseTimetableHistoryVOMap.get(workInfo.getCourseTimetableId()).getResourceTitle());
            }
            workListVO.setSubWorkTime(Objects.nonNull(workInfo.getWorkTime()) ? workInfo.getWorkTime().format(fmt) : "");
            return workListVO;
        }));
    }

    @Override
    public WorkInfoVO workInfo(Integer id) {
        WorkInfoPojo workInfo = workInfoPojoMongoService.getById(id);
        if (Objects.isNull(workInfo)) {
            throw new BusinessException("作业信息不存在");
        }
        CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(workInfo.getCourseId(), workInfo.getCourseTimetableId());
        ClassesTeamDetailVO classesTeamInfoByUser = classesComponent.getClassesTeamInfoByUser(workInfo.getCompanyId(), workInfo.getCourseId(), workInfo.getUserId());
        List<WorkModelDetailVO> workModelDetailVOS = workContentParse(workInfo.getContent());
        List<MyWorkDetailVO> workDetailList = workModelDetailVOS.stream().map(workModelDetailVO -> {
            MyWorkDetailVO myWorkDetailVO = new MyWorkDetailVO();
            BeanUtils.copyProperties(workModelDetailVO, myWorkDetailVO);
            return myWorkDetailVO;
        }).collect(Collectors.toList());
        Map<Integer, List<AppWorkTeacherReplyVO>> workReplyMap = workTeacherReplyService.list(new LambdaQueryWrapper<WorkTeacherReply>()
                .eq(WorkTeacherReply::getWorkId, id)).stream().map(teacherReply -> {
                    AppWorkTeacherReplyVO appWorkTeacherReplyVO = new AppWorkTeacherReplyVO();
                    BeanUtils.copyProperties(teacherReply, appWorkTeacherReplyVO);
                    appWorkTeacherReplyVO.setTeacherReply(teacherReply.getTeacherContent());
                    appWorkTeacherReplyVO.setPostId(teacherReply.getWorkId());
                    appWorkTeacherReplyVO.setTeacherReplyType(teacherReply.getTeacherReplyType());
                    appWorkTeacherReplyVO.setTeacherName(teacherReply.getUserName());
                    return appWorkTeacherReplyVO;
                }
        ).collect(Collectors.groupingBy(AppWorkTeacherReplyVO::getReplyUserType));
        return WorkInfoVO.builder()
                .score(workInfo.getScore())
                .belongDate(workInfo.getCourseBeginTime().toLocalDate())
                .workDetails(workDetailList)
                .classesNo(workInfo.getClassesNo())
                .classesTeacherPhone(workInfo.getClassesTeacherPhone())
                .courseName(workInfo.getCourseName())
                .userName(workInfo.getUserName())
                .userPhone(workInfo.getUserPhone())
                .workTime(workInfo.getWorkTime())
                .teacherReplyTime(workInfo.getTeacherReplyTime())
                .resourceTitle(Objects.nonNull(courseTimetableUnlockDetailVO) ? courseTimetableUnlockDetailVO.getResourceTitle() : "")
                .classesTeacherName(Objects.nonNull(classesTeamInfoByUser) ? classesTeamInfoByUser.getClassesOwnerName() : "")
                .recommendTime(workInfo.getRecommendTime())
                .recommendSource(workInfo.getRecommendSource())
                .recommendTeacherStatus(workInfo.getRecommendTeacherStatus())
                .recommendTeacherTime(workInfo.getRecommendTeacherTime())
                .teacherReplyStatus(workInfo.getTeacherReplyStatus())
                .teacherReplyTime(workInfo.getTeacherReplyTime())
                .teacherReplyList(Objects.nonNull(workReplyMap) && Objects.nonNull(workReplyMap.get(ReplyUserTypeEnums.TEACHER.getCode())) ? workReplyMap.get(ReplyUserTypeEnums.TEACHER.getCode()) : new ArrayList<>())
                .companyReplyList(Objects.nonNull(workReplyMap) && Objects.nonNull(workReplyMap.get(ReplyUserTypeEnums.COMPANY_USER.getCode())) ? workReplyMap.get(ReplyUserTypeEnums.COMPANY_USER.getCode()) : new ArrayList<>())
                .build();
    }

    @Override
    public List<WorkListVO> workListExport(UserWorkListDTO userWorkListDTO) {
        userWorkListDTO.setPageNum(1);
        userWorkListDTO.setPageSize(10_0000);
        PageInfoBT<WorkListVO> pageInfoBT = this.workList(userWorkListDTO);
        if (CollUtil.isEmpty(pageInfoBT.getRecords())) {
            return new ArrayList<>();
        }
        return pageInfoBT.getRecords();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean listenAddScore(DoWorkSendKafkaBO bo) {
        log.info("listenAddScore 开始 bo:{}", JSON.toJSONString(bo));
        //记录听课完成项块start
        try{
            InnoCourseGoodsDetailVO innoCourseGoodsDetailVO = courseComponent.innoCourseInfo();
            if (bo.getCourseId().equals(innoCourseGoodsDetailVO.getCourseId())){
                doneAction(bo);
            }
        }catch (Exception e){
            log.error("unlockByUidAndRid 发生异常：{}", e);
        }
        //记录听课完成块end
        boolean sendListen = false;
        // 企业+个人版 课程表
        CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(bo.getCourseId(), bo.getCourseTimeTableId(), bo.getUserId());
        log.info("listenAddScore courseTimetableUnlockDetailVO:{}", JSON.toJSONString(courseTimetableUnlockDetailVO));
        if (Objects.isNull(courseTimetableUnlockDetailVO)) {
            log.info("listenAddScore 课程表信息未查询到-退出听课加分");
            return false;
        }
        if (Objects.isNull(courseTimetableUnlockDetailVO.getWorkBeginTime()) || Objects.isNull(courseTimetableUnlockDetailVO.getWorkEndTime())) {
            log.info("listenAddScore 作业开始结束时间未设置-退出听课加分");
            return false;
        }
        CourseConfig courseConfig = courseComponent.courseConfigDetail(bo.getCompanyId(), bo.getCourseId());
        log.info("listenAddScore courseConfig:{}", JSON.toJSONString(courseConfig));
        if (Objects.isNull(courseConfig)) {
            log.info("listenAddScore courseConfig未查询到-退出听课加分");
            return false;
        }
        UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(bo.getCourseId(), bo.getUserId());
        bo.setCompanyId(userRightsCourse.getCompanyId());
        if (YesOrNoEnum.YES.getCode().equals(courseConfig.getMakeUpWork()) && LocalDateTime.now().isAfter(courseTimetableUnlockDetailVO.getWorkEndTime())) {
            LocalDateTime listenTime = LocalDateTime.ofEpochSecond(bo.getTs(), 0, ZoneOffset.ofHours(8));
            if (!(listenTime.isAfter(courseTimetableUnlockDetailVO.getWorkBeginTime()) && listenTime.isBefore(courseTimetableUnlockDetailVO.getWorkEndTime().plusDays(courseConfig.getMakeUpWorkBeginWeek())))) {
                log.info("listenAddScore 补交作业 听课时间:{},开始时间:{},结束时间:{},不在时间范围内-退出听课加分", listenTime, courseTimetableUnlockDetailVO.getWorkBeginTime(), courseTimetableUnlockDetailVO.getWorkEndTime().plusDays(courseConfig.getMakeUpWorkBeginWeek()));
                return false;
            }
            log.info("listenAddScore 补交作业 听课时间:{},开始时间:{},结束时间:{},在时间范围内-进行加分操作", listenTime, courseTimetableUnlockDetailVO.getWorkBeginTime(), courseTimetableUnlockDetailVO.getWorkEndTime().plusDays(courseConfig.getMakeUpWorkBeginWeek()));
        } else {
            LocalDateTime listenTime = LocalDateTime.ofEpochSecond(bo.getTs(), 0, ZoneOffset.ofHours(8));
            if (!(listenTime.isAfter(courseTimetableUnlockDetailVO.getWorkBeginTime()) && listenTime.isBefore(courseTimetableUnlockDetailVO.getWorkEndTime()))) {
                log.info("listenAddScore 听课时间:{},开始时间:{},结束时间:{},不在时间范围内-退出听课加分", listenTime, courseTimetableUnlockDetailVO.getWorkBeginTime(), courseTimetableUnlockDetailVO.getWorkEndTime());
                return false;
            }
            log.info("listenAddScore 听课时间:{},开始时间:{},结束时间:{},在作业提交时间范围内-进行加分操作", listenTime, courseTimetableUnlockDetailVO.getWorkBeginTime(), courseTimetableUnlockDetailVO.getWorkEndTime());
        }
        Jedis jedis = JedisPoolImpl.getResource();
        JedisDistributedLock lock = new JedisDistributedLock(jedis, WorkKafkaMessageEnum.AUTO_LISTENED.getCode() + bo.getCompanyId() + "-" + bo.getCourseTimeTableId() + "-" + bo.getUserId(), 10000, 5000);//acquireTimeoutMillis:超过该时间没有获取到锁，直接返回false; expiryTimeMillis:锁过期时间)
        try {
            if (!lock.acquireEp()) {
                log.info("listenAddScore 加锁失败,userId_{}, courseId_{}, companyId_{}, courseTimeTableId_{}", bo.getUserId(), bo.getCourseId(), bo.getCompanyId(), bo.getCourseTimeTableId());
                throw new BusinessException("听课打卡失败，请稍后重试");
            }
            UserStudyInfo userStudyInfo = this.getUserStudyInfo(bo.getUserId(), bo.getCourseId(), bo.getCompanyId());
            WorkInfo workInfo = workInfoMapper.queryOneWork(bo.getUserId(), bo.getCourseId(), bo.getCompanyId(), courseTimetableUnlockDetailVO.getId());
            AtomicReference<Integer> score = new AtomicReference<>(0);
            // 作业项数据
            List<WorkModelDetailVO> workModelDetailVOS;
            if (Objects.nonNull(workInfo)) {
                workInfo.setResourceTitle(courseTimetableUnlockDetailVO.getResourceTitle());
                workInfo.setWorkModelFlag(courseTimetableUnlockDetailVO.getWorkModelFlag());
                boolean isOldFlag = getIsOldFlag(courseTimetableUnlockDetailVO.getWorkModelFlag(), bo.getUserId(), bo.getCourseId());
                workInfo.setUserFlag(isOldFlag ? 1 : 0);
                WorkUserModel workUserModel = getWorkUserModel(courseTimetableUnlockDetailVO.getWorkModelFlag(), bo.getUserId(), bo.getCourseId());
                // 如果已经有分或者已写过作业,就不能修改模板等级了
                workInfo.setLevel((Objects.equals(workInfo.getWorkStatus(), 1) || workInfo.getScore() > 0) && StrUtil.isNotBlank(workInfo.getLevel())
                        ? workInfo.getLevel() : Objects.nonNull(workUserModel) && StringUtils.isNotBlank(workUserModel.getLevel()) ? workUserModel.getLevel() : "");
                Integer workModelId = getSimpleWorkModelIdByCourseTimetableUnlockDetailVO(courseTimetableUnlockDetailVO, isOldFlag, workUserModel, workInfo);
                workInfo.setWorkModelId(workModelId);
                WorkModelDetail workModelDetail = workModelDetailMapper.selectOne(new LambdaQueryWrapper<WorkModelDetail>()
                        .eq(WorkModelDetail::getType, LISTEN.getCode())
                        .eq(WorkModelDetail::getWorkModelId, workInfo.getWorkModelId()));
                if (Objects.isNull(workModelDetail)) {
                    log.info("listenAddScore 该作业没有听课加分作业项----退出听课加分,userId_{}, courseId_{}, companyId_{}, courseTimeTableId_{}", bo.getUserId(), bo.getCourseId(), bo.getCompanyId(), bo.getCourseTimeTableId());
                    return true;
                }
                if (WorkModelFlagEnum.UNIFY.getCode().equals(workInfo.getWorkModelFlag())) {
                    workInfo.setLevel(courseTimetableUnlockDetailVO.getPlanSchemeOption());
                }
                if (workInfo.getWorkStatus().equals(1)) {
                    bo.setWorkStatus(true);
                }
                if (Objects.equals(workInfo.getListenStatus(), 1)) {
                    if (!workInfo.getAutomatic().equals(1)) {
                        workInfo.setAutomatic(1);
                        workInfoMapper.updateById(workInfo);
                        workInfoPojoMongoService.saveOrUpdate(workInfo);
                        log.info("listenAddScore 作业已加过听课分,但是没有自动打卡,现在自动打卡,userId_{}, courseId_{}, companyId_{}, courseTimeTableId_{}", bo.getUserId(), bo.getCourseId(), bo.getCompanyId(), bo.getCourseTimeTableId());
                    }
                    log.info("listenAddScore 作业已加过听课分 return,userId_{}, courseId_{}, companyId_{}, courseTimeTableId_{}", bo.getUserId(), bo.getCourseId(), bo.getCompanyId(), bo.getCourseTimeTableId());
                    return true;
                }
                if (StringUtils.isNotBlank(workInfo.getContent())) {
                    workModelDetailVOS = workContentParse(workInfo.getContent());
                } else {
                    workModelDetailVOS = queryWorkModelDetailList(workInfo.getWorkModelId(), bo.getCourseId(), courseTimetableUnlockDetailVO);
                }
                sendListen = listen(bo, userStudyInfo, workInfo, score, workModelDetail, workModelDetailVOS);
            } else {
                log.info("listenAddScore 未写作业---进行听课加分操作,userId_{}, courseId_{}, companyId_{}, courseTimeTableId_{}", bo.getUserId(), bo.getCourseId(), bo.getCompanyId(), bo.getCourseTimeTableId());
                workInfo = this.workInfoBuild(courseTimetableUnlockDetailVO, bo.getUserId(), bo.getCourseId(), bo.getCompanyId());
                workInfo.setResourceTitle(courseTimetableUnlockDetailVO.getResourceTitle());
                boolean isOldFlag = getIsOldFlag(courseTimetableUnlockDetailVO.getWorkModelFlag(), bo.getUserId(), bo.getCourseId());
                workInfo.setUserFlag(isOldFlag ? 1 : 0);
                WorkUserModel workUserModel = getWorkUserModel(courseTimetableUnlockDetailVO.getWorkModelFlag(), bo.getUserId(), bo.getCourseId());
                // 如果已经有分或者已写过作业,就不能修改模板等级了
                workInfo.setLevel((Objects.equals(workInfo.getWorkStatus(), 1) || workInfo.getScore() > 0) && StrUtil.isNotBlank(workInfo.getLevel())
                        ? workInfo.getLevel() : Objects.nonNull(workUserModel) && StringUtils.isNotBlank(workUserModel.getLevel()) ? workUserModel.getLevel() : "");
                Integer workModelId = getSimpleWorkModelIdByCourseTimetableUnlockDetailVO(courseTimetableUnlockDetailVO, isOldFlag, workUserModel, workInfo);
                workInfo.setWorkModelId(workModelId);
                workModelDetailVOS = queryWorkModelDetailList(workInfo.getWorkModelId(), bo.getCourseId(), courseTimetableUnlockDetailVO);
                if (YesOrNoEnum.YES.getCode().equals(courseConfig.getMakeUpWork()) && LocalDateTime.now().isAfter(courseTimetableUnlockDetailVO.getWorkEndTime())) {
                    workInfo.setClassesNo(0);
                    workInfo.setClassesName("");
                    workInfo.setTeamId(0);
                    workInfo.setTeamName("");
                }
                if (WorkModelFlagEnum.UNIFY.getCode().equals(workInfo.getWorkModelFlag())) {
                    workInfo.setLevel(courseTimetableUnlockDetailVO.getPlanSchemeOption());
                }
                WorkModelDetail workModelDetail = workModelDetailMapper.selectOne(new LambdaQueryWrapper<WorkModelDetail>()
                        .eq(WorkModelDetail::getType, LISTEN.getCode())
                        .eq(WorkModelDetail::getWorkModelId, workInfo.getWorkModelId()));
                if (Objects.isNull(workModelDetail)) {
                    log.info("listenAddScore 该作业没有听课加分作业项----退出听课加分,userId_{}, courseId_{}, companyId_{}, courseTimeTableId_{}", bo.getUserId(), bo.getCourseId(), bo.getCompanyId(), bo.getCourseTimeTableId());
                    return true;
                }
                log.info("listenAddScore 作业未加过听课分----进行听课加分操作 begin,userId_{}, courseId_{}, companyId_{}, courseTimeTableId_{}", bo.getUserId(), bo.getCourseId(), bo.getCompanyId(), bo.getCourseTimeTableId());
                sendListen = listen(bo, userStudyInfo, workInfo, score, workModelDetail, workModelDetailVOS);
            }
            if (sendListen) {
                bo.setTag(WorkKafkaMessageEnum.AUTO_LISTENED.getCode());
                String boStr = JSONObject.toJSONString(bo);
                log.info("listenAddScore sendListen, 发送消息 bo :{}", boStr);
                kafkaProduction.send(workTopic, String.valueOf(bo.getUserId()), JSONObject.parseObject(boStr));
            }
            // 如果作业项只有听课项，且没有写过作业，则自动提交作业
            if (!Objects.equals(workInfo.getWorkStatus(), 1)
                    && Objects.isNull(workModelDetailVOS.stream().filter(workInfoDetail -> !LISTEN.getCode().equals(workInfoDetail.getType())).findFirst().orElse(null))) {
                DoWorkMsgDTO doWorkMsgDTO = BeanUtil.copyProperties(bo, DoWorkMsgDTO.class);
                doWorkMsgDTO.setWorkDetails(BeanUtil.copyToList(workModelDetailVOS, DoWorkDetailDTO.class));
                log.info("listenAddScore syncDoWork 如果作业项只有听课项，则自动提交作业");
                syncDoWork(doWorkMsgDTO);
            }
            generateRank(bo);
            listenSendMessageForCompanyZone(workInfo, courseTimetableUnlockDetailVO);
        } catch (InterruptedException e) {
            log.info("syncDoWork 听课打卡失败，请稍后重试,userId_{}, courseId_{}, companyId_{}, courseTimeTableId_{}, error:{}",
                    bo.getUserId(), bo.getCourseId(), bo.getCompanyId(), bo.getCourseTimeTableId(), e.getMessage(), e);
            throw new BusinessException("听课打卡失败，请稍后重试");
        } finally {
            lock.release();
        }
        return true;
    }

    private boolean listen(DoWorkSendKafkaBO bo, UserStudyInfo userStudyInfo, WorkInfo workInfo, AtomicReference<Integer> score, WorkModelDetail workModelDetail, List<WorkModelDetailVO> workModelDetailVOS) {
        boolean sendListen = false;
        WorkModelDetailVO workModelDetailVO = workModelDetailVOS.stream().filter(workInfoDetail -> LISTEN.getCode().equals(workInfoDetail.getType()))
                .findFirst().orElse(null);
        if (Objects.isNull(workModelDetailVO)) {
            log.info("listenAddScore 已保存的作业项没有筛选到听课作业项，退出听课加分");
            return sendListen;
        }
        workModelDetailVO.setContent("1");
        workModelDetailVO.setScore(workModelDetail.getScore());
        score.set(score.get() + workModelDetail.getScore());
        sendListen = true;
        workInfo.setScore(workInfo.getScore() + score.get());
        workInfo.setContent(JSONObject.toJSONString(workModelDetailVOS));
        workInfo.setAutomatic(1);
        workInfo.setListenStatus(1);
        workInfo.setUpdateTime(LocalDateTime.now());
        workInfoMapper.saveOrUpdate(workInfo);
        workInfoPojoMongoService.saveOrUpdate(workInfo);
        userStudyInfo.setScore(userStudyInfo.getScore() + score.get());
        userStudyInfoMapper.updateById(userStudyInfo);
        bo.setScore(workInfo.getScore());
        bo.setListenStatus(true);
        log.info("listenAddScore 作业未加过听课分----进行听课加分操作,userId_{}, courseId_{}, companyId_{}, courseTimeTableId_{}", bo.getUserId(), bo.getCourseId(), bo.getCompanyId(), bo.getCourseTimeTableId());
        return sendListen;
    }

    @Override
    public LearningSituationVO learningSituation(HeaderUserInfo headerUserInfo) {
        LearningSituationVO learningSituationVO = new LearningSituationVO();
        UserInfoCompanySearchVO userInfoCompanySearchVO = userComponent.userByUserIdCompanyId(headerUserInfo.getCurrentCompanyId(), headerUserInfo.getId());
        if (Objects.nonNull(userInfoCompanySearchVO) && Objects.nonNull(userInfoCompanySearchVO.getIntoCompanyTime())) {
            learningSituationVO.setLearnDays(Duration.between(userInfoCompanySearchVO.getIntoCompanyTime(), LocalDateTime.now()).toDays());
        } else {
            learningSituationVO.setLearnDays((long) 0);
        }
        Integer userCourseTotal = orderComponent.getCountByCourseAndCompany(headerUserInfo.getId(), headerUserInfo.getCurrentCompanyId());
        learningSituationVO.setCourseNum(Objects.nonNull(userCourseTotal) ? userCourseTotal : 0);
        QueryWrapper<UserStudyInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("sum(score) as  learnScore ")
                .lambda()
                .eq(UserStudyInfo::getCompanyId, headerUserInfo.getCurrentCompanyId())
                .eq(UserStudyInfo::getUserId, headerUserInfo.getId());
        UserStudyInfo userStudyInfo = userStudyInfoMapper.selectOne(queryWrapper);
        if (Objects.nonNull(userStudyInfo)) {
            learningSituationVO.setLearnScore(Objects.nonNull(userStudyInfo.getLearnScore()) ? userStudyInfo.getLearnScore() : 0);
        } else {
            learningSituationVO.setLearnScore(0);
        }
        return learningSituationVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recommendAndCancelWork(Integer id, HeaderUserInfo headerUserInfo) {
        WorkInfoPojo workInfo = workInfoPojoMongoService.getById(id);
        if (Objects.isNull(workInfo.getRecommendStatus()) || workInfo.getRecommendStatus().equals(0)) {
            workInfo.setRecommendStatus(1);
            if (StringUtils.isBlank(headerUserInfo.getAppOs())) {
                workInfo.setRecommendSource(RecommendSourceEnum.MANAGE_PLATFORM.getCode());
            } else {
                workInfo.setRecommendSource(RecommendSourceEnum.SAAS.getCode());
            }
        } else {
            workInfo.setRecommendStatus(0);
            workInfo.setRecommendTeacherStatus(0);
        }
        workInfo.setRecommendTime(LocalDateTime.now());
        WorkInfo mySqlWorkInfo = new WorkInfo();
        BeanUtil.copyProperties(workInfo,mySqlWorkInfo);
        mySqlWorkInfo.setId(workInfo.getWorkId());
        workInfoMapper.updateById(mySqlWorkInfo);
        workInfoPojoMongoService.saveOrUpdate(workInfo);
        prefectureComponent.workContentAsyncPrefecture(workInfo, PrefectureContentAsyncTypeEnum.RECOMMEND_WORK.getCode());
    }


    @Override
    public WorkCardInfoVO todayWork(WorkCardInfoDTO dto, HeaderUserInfo headerUserInfo) {
        if (Objects.nonNull(dto.getCourseTimeTableId()) && Objects.equals(dto.getCourseTimeTableId(), 0)) {
            dto.setCourseTimeTableId(null);
        }
        long l1 = currentTimeMillis();
        log.info("todayWork 用户信息:{}",JSONObject.toJSONString(headerUserInfo));
        UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(dto.getCourseId(), headerUserInfo.getId());
        headerUserInfo.setCurrentCompanyId(userRightsCourse.getCompanyId());
        CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(dto.getCourseId(), dto.getCourseTimeTableId(), headerUserInfo.getId());
        log.info("todayWork courseTimetableUnlockDetailVO:{}", JSON.toJSONString(courseTimetableUnlockDetailVO));
        if (Objects.isNull(courseTimetableUnlockDetailVO)) {
            log.info("todayWork 课程表信息未查询到-退出");
            return null;
        }
        CourseConfig courseConfig = courseComponent.courseConfigDetail(headerUserInfo.getCurrentCompanyId(), dto.getCourseId());
        log.info("todayWork courseConfig:{}", JSON.toJSONString(courseConfig));
        // 补交作业
        if (Objects.nonNull(courseConfig) && Objects.nonNull(dto.getCourseTimeTableId()) && YesOrNoEnum.YES.getCode().equals(courseConfig.getMakeUpWork()) && LocalDateTime.now().isAfter(courseTimetableUnlockDetailVO.getWorkEndTime())) {
            if (!(LocalDateTime.now().isAfter(courseTimetableUnlockDetailVO.getWorkBeginTime()) && LocalDateTime.now().isBefore(courseTimetableUnlockDetailVO.getWorkEndTime().plusDays(courseConfig.getMakeUpWorkBeginWeek())))) {
                log.info("todayWork 当前时间:" + LocalDateTime.now() + ",提交作业开始时间:" + courseTimetableUnlockDetailVO.getWorkBeginTime() + ",提交作业结束时间:" + courseTimetableUnlockDetailVO.getWorkBeginTime().plusDays(-courseConfig.getMakeUpWorkBeginWeek()) + ",不在作业提交时间范围内-退出.");
                return null;
            }
        } else {
            // 正常交作业
            if (!(LocalDateTime.now().isAfter(courseTimetableUnlockDetailVO.getWorkBeginTime()) && LocalDateTime.now().isBefore(courseTimetableUnlockDetailVO.getWorkEndTime()))) {
                log.info("todayWork 当前时间:" + LocalDateTime.now() + ",提交作业开始时间:" + courseTimetableUnlockDetailVO.getWorkBeginTime() + ",提交作业结束时间:" + courseTimetableUnlockDetailVO.getWorkEndTime() + ",不在作业提交时间范围内-退出.");
                return null;
            }
        }
        // 作业查询
        WorkInfoPojo workInfo = workInfoPojoMongoService.querySimpleOne(new WorkInfoPojoQueryDTO().setUserId(headerUserInfo.getId())
                .setCourseId(dto.getCourseId()).setCompanyId(headerUserInfo.getCurrentCompanyId()).setCourseTimetableId(courseTimetableUnlockDetailVO.getId()));
        boolean isOldFlag = getIsOldFlag(courseTimetableUnlockDetailVO.getWorkModelFlag(), headerUserInfo.getId(), dto.getCourseId());
        WorkUserModel workUserModel = getWorkUserModel(courseTimetableUnlockDetailVO.getWorkModelFlag(), headerUserInfo.getId(), dto.getCourseId());
        Integer workModelId = getSimpleWorkModelIdByCourseTimetableUnlockDetailVO(courseTimetableUnlockDetailVO, isOldFlag, workUserModel, workInfo);
        // 作业项
        List<WorkModelDetailVO> workModelDetailList = todayWorkModelDetailList(dto, courseTimetableUnlockDetailVO, courseConfig, workInfo, workModelId, workUserModel);
        long l2 = currentTimeMillis();
        // 作业分数
        UserStudyInfo userStudyInfo = userStudyInfoMapper.selectOne(new LambdaQueryWrapper<UserStudyInfo>()
                .eq(UserStudyInfo::getCourseId, dto.getCourseId())
                .eq(UserStudyInfo::getCompanyId, headerUserInfo.getCurrentCompanyId())
                .eq(UserStudyInfo::getUserId, headerUserInfo.getId()));
        WorkCardInfoVO vo = WorkCardInfoVO
                .builder()
                .workStatus(Objects.nonNull(workInfo) && Objects.equals(workInfo.getWorkStatus(), 1) ? 1 : 0)
                .workModelId(workModelId)
                .courseId(dto.getCourseId())
                .workStartTime(courseTimetableUnlockDetailVO.getWorkBeginTime())
                .workEndTime(courseTimetableUnlockDetailVO.getWorkEndTime())
                .workTitle(courseTimetableUnlockDetailVO.getResourceTitle())
                .score(Objects.nonNull(userStudyInfo) ? Objects.nonNull(workInfo) ? userStudyInfo.getScore() - workInfo.getScore() : userStudyInfo.getScore() : 0)
                .courseTimeTableId(courseTimetableUnlockDetailVO.getId())
                .workModelDetails(workModelDetailList)
                .classesId(Objects.nonNull(workInfo) ? workInfo.getClassesId() : 0)
                .build();
        // 作业上方的文案
        todayWorkSetTopTip(dto.getCourseTimeTableId(), courseTimetableUnlockDetailVO, courseConfig, vo);
        // 附加信息 延伸学习
        todayWorkSetAdditionalInfo(courseTimetableUnlockDetailVO, courseConfig, workInfo, vo);
        long l3 = currentTimeMillis();
        log.info("todayWork 耗时统计 l2-l1:[" + (l2 - l1) + "],l3-l2:[" + (l3 - l2) + "]");
        return vo;
    }

    private List<WorkModelDetailVO> todayWorkModelDetailList(WorkCardInfoDTO dto, CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO, CourseConfig courseConfig, WorkInfoPojo workInfo, Integer workModelId, WorkUserModel workUserModel) {
        List<WorkModelDetailVO> workModelDetailList;
        if (Objects.nonNull(workInfo) && StringUtils.isNotBlank(workInfo.getContent())) {
            // 从 work_info 表里取出来的作业项,也就是之前保存过
            workModelDetailList = workContentParse(workInfo.getContent());
            if (Objects.equals(workInfo.getWorkStatus(), 1)) {
                return workModelDetailList;
            }
            if (WorkModelFlagEnum.LEVEL_WORK_TEMPLATE.getCode().equals(courseTimetableUnlockDetailVO.getWorkModelFlag())) {
                if (Objects.nonNull(workUserModel) && !Objects.equals(workUserModel.getLevel(), workInfo.getLevel())) {
                    // 模板作业项
                    List<WorkModelDetailVO> workModelDetailVOS = queryWorkModelDetailList(workModelId, workInfo.getCourseId(), courseTimetableUnlockDetailVO);
                    // 下面的代码是这样的业务:作业模板支持用户自己切换,切换完了还要把分享加分和听课加分带到新的作业模板里. 追加了读书项
                    for (WorkModelDetailVO workModelDetailVO : workModelDetailVOS) {
                        if (LISTEN.getCode().equals(workModelDetailVO.getType())) {
                            if (Objects.equals(workInfo.getListenStatus(), 1)) {
                                workModelDetailVO.setContent("1");
                            }
                        } else if (SHARE.getCode().equals(workModelDetailVO.getType())) {
                            if (Objects.equals(workInfo.getShareStatus(), 1)) {
                                workModelDetailVO.setContent("1");
                            }
                        } else if (READ.getCode().equals(workModelDetailVO.getType())) {
                            workModelDetailList.stream().filter(existWorkModelDetailVO -> Objects.equals(existWorkModelDetailVO.getType(), READ.getCode()))
                                    .findAny().ifPresent(existWorkModelDetailVO -> {
                                        if ("1".equals(existWorkModelDetailVO.getContent())) {
                                            workModelDetailVO.setContent("1");
                                            workModelDetailVO.setEbookId(existWorkModelDetailVO.getEbookId());
                                            workModelDetailVO.setEbookName(existWorkModelDetailVO.getEbookName());
                                            workModelDetailVO.setChapterList(existWorkModelDetailVO.getChapterList());
                                            workModelDetailVO.setReadRecordId(existWorkModelDetailVO.getReadRecordId());
                                        }
                                    });
                        }
                    }
                    workModelDetailList = workModelDetailVOS;
                }
            }
        } else {
            // 模板作业项
            workModelDetailList = queryWorkModelDetailList(workModelId, dto.getCourseId(), courseTimetableUnlockDetailVO);
            workModelDetailList.forEach(workModelDetailVO -> {
                if (Objects.nonNull(workModelDetailVO) && LISTEN.getCode().equals(workModelDetailVO.getType()) && Objects.nonNull(courseConfig)) {
                    workModelDetailVO.setManualStatus(courseConfig.getManualClockSwitch());
                }
            });
        }
        return workModelDetailList;
    }

    private void todayWorkSetAdditionalInfo(CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO, CourseConfig courseConfig, WorkInfoPojo workInfo, WorkCardInfoVO vo) {
        AdditionalInfoVO additionalInfoVO = new AdditionalInfoVO();
        if (Objects.nonNull(workInfo) && StringUtils.isNotBlank(workInfo.getAdditional())) {
            additionalInfoVO = additionalParse(workInfo.getAdditional());
        }
        if (Objects.nonNull(workInfo) && StringUtils.isNotBlank(workInfo.getAdditional()) && Objects.equals(additionalInfoVO.getStatus(), 1)) {
            if (Objects.nonNull(courseConfig) && YesOrNoEnum.YES.getCode().equals(courseConfig.getExtendStudySwitch())) {
                additionalInfoVO.setAdditionalScore(courseConfig.getExtendStudyScore());
            }
            vo.setAdditionalInfo(additionalInfoVO);
        } else {
            if (Objects.nonNull(courseConfig) && YesOrNoEnum.YES.getCode().equals(courseConfig.getExtendStudySwitch())) {
                additionalInfoVO = oldServerComponent.additionalInfo(courseConfig.getCourseId(), courseTimetableUnlockDetailVO.getWorkBeginTime().toLocalDate());
                if (Objects.nonNull(additionalInfoVO)) {
                    additionalInfoVO.setAdditionalScore(courseConfig.getExtendStudyScore());
                    vo.setAdditionalInfo(additionalInfoVO);
                }
            }
        }
    }

    private void todayWorkSetTopTip(Integer courseTimeTableId, CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO, CourseConfig courseConfig, WorkCardInfoVO vo) {
        if (Objects.nonNull(courseConfig) && Objects.nonNull(courseTimeTableId) && YesOrNoEnum.YES.getCode().equals(courseConfig.getMakeUpWork()) && LocalDateTime.now().isAfter(courseTimetableUnlockDetailVO.getWorkEndTime())) {
            LocalDate endWeekDay = courseTimetableUnlockDetailVO.getWorkEndTime().plusDays(courseConfig.getMakeUpWorkBeginWeek()).toLocalDate();
            LocalDateTime endTime = LocalDateTime.of(endWeekDay, courseTimetableUnlockDetailVO.getWorkEndTime().toLocalTime());
            vo.setTopTip("请提交《" + courseTimetableUnlockDetailVO.getResourceTitle() + "》作业\n" +
                    (endTime.getMonthValue() < 10 ? "0" + endTime.getMonthValue() : endTime.getMonthValue()) + "月" +
                    (endTime.getDayOfMonth() < 10 ? "0" + endTime.getDayOfMonth() : endTime.getDayOfMonth()) + "日" +
                    endTime.getHour() + ":" +
                    (endTime.getMinute() < 10 ? "0" + endTime.getMinute() : endTime.getMinute()) + "之后将无法补交，提交后不可修改。");
        } else {
            vo.setTopTip("请提交《" + courseTimetableUnlockDetailVO.getResourceTitle() + "》作业\n" +
                    "记得在" +
                    (courseTimetableUnlockDetailVO.getWorkEndTime().getMonthValue() < 10 ? "0" + courseTimetableUnlockDetailVO.getWorkEndTime().getMonthValue() : courseTimetableUnlockDetailVO.getWorkEndTime().getMonthValue()) + "月" +
                    (courseTimetableUnlockDetailVO.getWorkEndTime().getDayOfMonth() < 10 ? "0" + courseTimetableUnlockDetailVO.getWorkEndTime().getDayOfMonth() : courseTimetableUnlockDetailVO.getWorkEndTime().getDayOfMonth()) + "日" +
                    courseTimetableUnlockDetailVO.getWorkEndTime().getHour() + ":" +
                    (courseTimetableUnlockDetailVO.getWorkEndTime().getMinute() < 10 ? "0" + courseTimetableUnlockDetailVO.getWorkEndTime().getMinute() : courseTimetableUnlockDetailVO.getWorkEndTime().getMinute()) + "之前完成哦~");
        }
    }

    // TODO: 2024/1/5 要改造的
    private Integer getSimpleWorkModelIdByCourseTimetableUnlockDetailVO(CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO, boolean oldUserFlag, WorkUserModel workUserModel, WorkInfo workInfo) {
        if (WorkModelFlagEnum.UNIFY.getCode().equals(courseTimetableUnlockDetailVO.getWorkModelFlag())) {
            return courseTimetableUnlockDetailVO.getWorkModelId();
        } else if (WorkModelFlagEnum.DIFF_OLD_NEW.getCode().equals(courseTimetableUnlockDetailVO.getWorkModelFlag())) {
            if (oldUserFlag) {
                return courseTimetableUnlockDetailVO.getOldUserWorkModelId();
            } else {
                return courseTimetableUnlockDetailVO.getWorkModelId();
            }
        } else if (WorkModelFlagEnum.LEVEL_WORK_TEMPLATE.getCode().equals(courseTimetableUnlockDetailVO.getWorkModelFlag())) {
            // 如果已经写过作业,或者加过分,就不更改了
            if (Objects.nonNull(workInfo) && (Objects.equals(workInfo.getWorkStatus(), 1) || workInfo.getScore() > 0)) {
                return workInfo.getWorkModelId();
            }
            String level = Objects.nonNull(workUserModel) && StringUtils.isNotBlank(workUserModel.getLevel()) ? workUserModel.getLevel() : "a";
            if ("a".equals(level)) {
                return courseTimetableUnlockDetailVO.getAlevelWorkModelId();
            } else if ("b".equals(level)) {
                return courseTimetableUnlockDetailVO.getBlevelWorkModelId();
            } else if ("c".equals(level)) {
                return courseTimetableUnlockDetailVO.getClevelWorkModelId();
            }
        }
        log.error("getSimpleWorkModelIdByCourseTimetableUnlockDetailVO 暂未查询到作业 workModelId 信息:courseTimetableUnlockDetailVO:{}", JSON.toJSONString(courseTimetableUnlockDetailVO));
        throw new BusinessException("暂未查询到作业信息,请稍后重试");
    }

    private Integer getSimpleWorkModelIdByCourseTimetableUnlockDetailVO(CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO, boolean oldUserFlag, WorkUserModel workUserModel, WorkInfoPojo workInfo) {
        if (WorkModelFlagEnum.UNIFY.getCode().equals(courseTimetableUnlockDetailVO.getWorkModelFlag())) {
            return courseTimetableUnlockDetailVO.getWorkModelId();
        } else if (WorkModelFlagEnum.DIFF_OLD_NEW.getCode().equals(courseTimetableUnlockDetailVO.getWorkModelFlag())) {
            if (oldUserFlag) {
                return courseTimetableUnlockDetailVO.getOldUserWorkModelId();
            } else {
                return courseTimetableUnlockDetailVO.getWorkModelId();
            }
        } else if (WorkModelFlagEnum.LEVEL_WORK_TEMPLATE.getCode().equals(courseTimetableUnlockDetailVO.getWorkModelFlag())) {
            // 如果已经写过作业,或者加过分,就不更改了
            if (Objects.nonNull(workInfo) && (Objects.equals(workInfo.getWorkStatus(), 1) || workInfo.getScore() > 0)) {
                return workInfo.getWorkModelId();
            }
            String level = Objects.nonNull(workUserModel) && StringUtils.isNotBlank(workUserModel.getLevel()) ? workUserModel.getLevel() : "a";
            if ("a".equals(level)) {
                return courseTimetableUnlockDetailVO.getAlevelWorkModelId();
            } else if ("b".equals(level)) {
                return courseTimetableUnlockDetailVO.getBlevelWorkModelId();
            } else if ("c".equals(level)) {
                return courseTimetableUnlockDetailVO.getClevelWorkModelId();
            }
        }
        log.error("getSimpleWorkModelIdByCourseTimetableUnlockDetailVO 暂未查询到作业 workModelId 信息:courseTimetableUnlockDetailVO:{}", JSON.toJSONString(courseTimetableUnlockDetailVO));
        throw new BusinessException("暂未查询到作业信息,请稍后重试");
    }

    private boolean getIsOldFlag(Integer workModelFlag, Integer userId, Integer courseId) {
        if (!WorkModelFlagEnum.DIFF_OLD_NEW.getCode().equals(workModelFlag)) {
         return false;
        }
        return workInfoMapper.checkOldUser(courseId, userId) > 0;
    }

    private WorkUserModel getWorkUserModel(Integer workModelFlag, Integer userId, Integer courseId) {
        if (!WorkModelFlagEnum.LEVEL_WORK_TEMPLATE.getCode().equals(workModelFlag)) {
            return null;
        }
        return workUserModelService.getByUserIdAndCourseId(userId, courseId);
    }

    @Override
    public PageInfoBT<WorkCardInfoVO> previousWorkList(PreviousWorkDTO dto, HeaderUserInfo headerUserInfo) {
        UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(dto.getCourseId(), headerUserInfo.getId());
        headerUserInfo.setCurrentCompanyId(userRightsCourse.getCompanyId());
        long l1 = System.currentTimeMillis();
        CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(dto.getCourseId(), null);
        long l2 = System.currentTimeMillis();
        IPage<WorkInfoPojo> workInfoPage = workInfoPojoMongoService.previousWorkList(dto, headerUserInfo, courseTimetableUnlockDetailVO);
        if (CollUtil.isEmpty(workInfoPage.getRecords())) {
            return PageInfoBT.noData();
        }
        long l3 = System.currentTimeMillis();
        List<WorkCardInfoVO> voList = workInfoPage.getRecords().stream().map(workInfoPojo -> {
            return WorkCardInfoVO.builder()
                    .id(workInfoPojo.getId())
                    .workStatus(workInfoPojo.getWorkStatus())
                    .courseId(workInfoPojo.getCourseId())
                    .workModelId(workInfoPojo.getWorkModelId())
                    .workStartTime(workInfoPojo.getCourseBeginTime())
                    .score(workInfoPojo.getScore())
                    .courseTimeTableId(workInfoPojo.getCourseTimetableId())
                    .additionalInfo(Objects.nonNull(workInfoPojo.getAdditional()) && StringUtils.isNotBlank(workInfoPojo.getAdditional()) ? additionalParse(workInfoPojo.getAdditional()) : null)
                    .workModelDetails(workContentParse(workInfoPojo.getContent()))
                    .workTitle(workInfoPojo.getResourceTitle())
                    .classesId(workInfoPojo.getClassesId())
                    .build();
        }).collect(Collectors.toList());
        PageInfoBT<WorkCardInfoVO> pageInfo = new PageInfoBT<>();
        pageInfo.setCurrent((long) dto.getPageNum());
        pageInfo.setSize((long) dto.getPageSize());
        pageInfo.setTotal(workInfoPage.getTotal());
        pageInfo.setRecords(voList);
        log.info("previousWorkList 耗时:l1-l2:{},l2-l3:{}", l2 - l1, l3 - l2);
        return pageInfo;
    }

    /**
     * @param dto,headerUserInfo
     * @Return List<AppWorkInfoListVO>
     * <AUTHOR>
     * @Date 2022年5月17日17:08:33
     * @Description App老师回复心得列表
     */
    @Override
    public PageInfoBT<AppWorkInfoListVO> teacherReplyHeartPerceptionList(AppWorkReplySearchDTO dto, HeaderUserInfo headerUserInfo) {
        UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(dto.getCourseId(), headerUserInfo.getId());
        headerUserInfo.setCurrentCompanyId(userRightsCourse.getCompanyId());
        CourseInfo courseInfo = courseComponent.courseInfoById(dto.getCourseId());
        List<WorkTeacherReply> workTeacherReplyList = workTeacherReplyService.queryByCourseId(dto.getCourseId());
        if (CollUtil.isEmpty(workTeacherReplyList)) {
            return PageInfoBT.noData();
        }
        List<Integer> workIdList = workTeacherReplyList.stream().map(WorkTeacherReply::getWorkId).collect(Collectors.toList());
        IPage<WorkInfoPojo> workInfoPage = workInfoPojoMongoService.selectTeacherReplyWorkPage(dto, workIdList, headerUserInfo);
        List<AppWorkInfoListVO> voList = workInfoPage.getRecords().stream().map(workInfo -> {
            // 转换老版本作业信息 解析作业内容
            return convertAppWorkInfoListVO(workInfo, null, false, headerUserInfo);
        }).collect(Collectors.toList());
        // 构建心得关联老师回复数据
        Map<Integer, List<AppWorkTeacherReplyVO>> replyMap = findTeacherReplyList(workTeacherReplyList, headerUserInfo);
        Map<Integer, Integer> oldUserMap = new HashMap<>();
        if (WorkModelFlagEnum.DIFF_OLD_NEW.getCode().equals(courseInfo.getWorkModelFlag())) {
            oldUserMap = workInfoMapper.checkOldUserByUserIds(dto.getCourseId(), voList.stream().map(AppWorkInfoListVO::getUserId).collect(Collectors.toList())).stream().collect(Collectors.toMap(Integer::intValue, userId -> userId));
        }
        Map<Integer, Integer> finalOldUserMap = oldUserMap;
        voList.forEach(workInfo -> {
            workInfo.setWorkModelFlag(courseInfo.getWorkModelFlag());
            if (WorkModelFlagEnum.DIFF_OLD_NEW.getCode().equals(courseInfo.getWorkModelFlag())) {
                if (Objects.nonNull(finalOldUserMap.get(workInfo.getUserId()))) {
                    workInfo.setUserFlag(UserFlagEnum.OLD_USER.getCode());
                }
            }
            if (ObjectUtil.isNotNull(replyMap)) {
                workInfo.setTeacherReplyList(replyMap.get(workInfo.getId()));
            }
        });
        PageInfoBT<AppWorkInfoListVO> pageInfo = new PageInfoBT<>();
        pageInfo.setCurrent((long) dto.getPageNum());
        pageInfo.setSize((long) dto.getPageSize());
        pageInfo.setTotal(workInfoPage.getTotal());
        pageInfo.setRecords(voList);
        return pageInfo;
    }

    /**
     * @param replyDTO,headerUserInfo
     * @Return List<AppWorkInfoListVO>
     * <AUTHOR>
     * @Date 2022年5月17日17:08:33
     * @Description App老师回复
     */
    @Override
    public List<AppWorkTeacherReplyVO> teacherReply(AppWorkReplyDTO replyDTO, HeaderUserInfo headerUserInfo) {
        List<AppWorkTeacherReplyVO> result;
        Integer courseType;
        // 获取被回复的作业信息
        WorkInfoPojo workInfo = workInfoPojoMongoService.getById(replyDTO.getId());
        headerUserInfo.setCurrentCompanyId(workInfo.getCompanyId());
        if (headerUserInfo.getCurrentCompanyId() > 0) {
            Map<Integer, String> userCompanyNameMap = userComponent.queryUserCompanyNameById(headerUserInfo.getCurrentCompanyId(), Collections.singletonList(headerUserInfo.getId()));
            headerUserInfo.setUserFullName(userCompanyNameMap.get(headerUserInfo.getId()));
        }
        // 保存老师回复内容
        WorkTeacherReply workTeacherReply = new WorkTeacherReply();
        BeanUtil.copyProperties(replyDTO, workTeacherReply);
        workTeacherReply.setWorkId(replyDTO.getId());
        workTeacherReply.setCourseId(workInfo.getCourseId());
        workTeacherReply.setUserName(headerUserInfo.getUserFullName());
        workTeacherReply.setTeacherUserId(headerUserInfo.getId());
        UserInfoCompanySearchVO userInfoCompanySearchVO = userComponent.userByUserIdCompanyId(headerUserInfo.getCurrentCompanyId(), headerUserInfo.getId());
        if (workTeacherInfoMapper.selectCount(new QueryWrapper<WorkTeacherInfo>().lambda()
                .eq(WorkTeacherInfo::getUserId, headerUserInfo.getId())
                .eq(WorkTeacherInfo::getCourseId, workInfo.getCourseId())) > 0) {
            workTeacherReply.setReplyUserType(ScreeningTypeEnums.TEACHER_REPLY.getCode());
            workInfo.setTeacherReplyStatus(YesOrNoEnum.YES.getCode());
            if (Objects.isNull(workInfo.getTeacherReplyTime())) {
                workInfo.setTeacherReplyTime(LocalDateTime.now());
            }
            courseType = PrefectureContentAsyncTypeEnum.TEACHER_REPLY.getCode();
        } else if (Objects.nonNull(userInfoCompanySearchVO) && userInfoCompanySearchVO.getSpecialUserFlag().equals(YesOrNoEnum.YES.getCode())) {
            workTeacherReply.setReplyUserType(ScreeningTypeEnums.COMPANY_REPLY.getCode());
            workInfo.setCompanyReplyStatus(YesOrNoEnum.YES.getCode());
            courseType = PrefectureContentAsyncTypeEnum.COMPANY_REPLY.getCode();
        } else {
            if (workInfo.getRecommendStatus().equals(YesOrNoEnum.NO.getCode())) {
                throw new BusinessException("该作业没有推荐不能回复");
            }
            if (workInfo.getRecommendTeacherStatus().equals(YesOrNoEnum.NO.getCode())) {
                throw new BusinessException("该作业没有推送不能回复");
            }
            if (!workInfo.getCompanyId().equals(-1)) {
                WorkTeacherInfo workTeacherInfo = workTeacherInfoMapper.selectOne(new QueryWrapper<WorkTeacherInfo>().lambda()
                        .eq(WorkTeacherInfo::getCourseId, workInfo.getCourseId()).last(" limit 1"));
                workTeacherReply.setReplyUserType(ScreeningTypeEnums.TEACHER_REPLY.getCode());
                workInfo.setTeacherReplyStatus(YesOrNoEnum.YES.getCode());
                if (Objects.isNull(workTeacherInfo)) {
                    throw new BusinessException("该课程没有配置老师，不可回复");
                }
                UserInfoSearchSimpleVO userInfoSearchSimpleVO = userComponent.userSimpleByUserId(workTeacherInfo.getUserId());
                if (Objects.isNull(userInfoSearchSimpleVO)) {
                    throw new BusinessException("老师信息配置异常");
                }
                workTeacherReply.setTeacherUserId(workTeacherInfo.getUserId());
                workTeacherReply.setUserName(userInfoSearchSimpleVO.getUserFullName());
            } else {
                workInfo.setTeacherReplyStatus(YesOrNoEnum.YES.getCode());
                workTeacherReply.setReplyUserType(ScreeningTypeEnums.TEACHER_REPLY.getCode());
                workTeacherReply.setTeacherUserId(headerUserInfo.getId());
                workTeacherReply.setUserName(headerUserInfo.getUserFullName());
            }
            courseType = PrefectureContentAsyncTypeEnum.TEACHER_REPLY.getCode();
        }
        if (Objects.isNull(workInfo.getCompanyReplyTime())) {
            workInfo.setCompanyReplyTime(LocalDateTime.now());
        }
        workTeacherReply.setTeacherReplyType(replyDTO.getTeacherReplyType());
        workTeacherReply.setTeacherContent(replyDTO.getTeacherReply());
        workTeacherReply.setTeacherReplyTime(LocalDateTime.now());
        workTeacherReply.setDeleteFlag(IsDeleteEnum.NO.getCode());
        workTeacherReply.setSourceType(SourceTypeEnum.APP.getCode());
        workTeacherReplyService.save(workTeacherReply);
        // 更新作业老师回复状态
        WorkInfo mySqlWorkInfo = new WorkInfo();
        BeanUtil.copyProperties(workInfo,mySqlWorkInfo);
        mySqlWorkInfo.setId(workInfo.getWorkId());
        workInfoMapper.updateById(mySqlWorkInfo);
        workInfoPojoMongoService.saveOrUpdate(workInfo);
        result = workTeacherReplyService.list(new LambdaQueryWrapper<WorkTeacherReply>()
                .eq(WorkTeacherReply::getReplyUserType, workTeacherReply.getReplyUserType())
                .eq(WorkTeacherReply::getWorkId, replyDTO.getId())).stream().map(teacherReply -> {
            AppWorkTeacherReplyVO appWorkTeacherReplyVO = new AppWorkTeacherReplyVO();
            BeanUtils.copyProperties(teacherReply, appWorkTeacherReplyVO);
            appWorkTeacherReplyVO.setTeacherReply(teacherReply.getTeacherContent());
            appWorkTeacherReplyVO.setPostId(teacherReply.getWorkId());
            appWorkTeacherReplyVO.setTeacherName(teacherReply.getUserName());
            appWorkTeacherReplyVO.setIsMyReply(teacherReply.getTeacherUserId().equals(headerUserInfo.getId()));
            return appWorkTeacherReplyVO;
        }).collect(Collectors.toList());
        // 推送消息通知
        commonAsyncService.teacherReplayPushMsg(headerUserInfo, workInfo, workTeacherReply);
        prefectureComponent.workContentAsyncPrefecture(workInfo, courseType);
        return result;
    }

    /**
     * @param delDTO,headerUserInfo
     * @Return List<AppWorkInfoListVO>
     * <AUTHOR>
     * @Date 2022年5月17日17:08:33
     * @Description 老师删除作业回复
     */
    @Override
    public void teacherDeleteReply(AppWorkReplyDelDTO delDTO, HeaderUserInfo headerUserInfo) {
        WorkTeacherReply workTeacherReply = workTeacherReplyMapper.selectById(delDTO.getId());
        if (workTeacherReply == null) {
            throw new BusinessException("回复不存在");
        }
        if (IsDeleteEnum.YES.getCode().equals(workTeacherReply.getDeleteFlag())) {
            log.info("teacherDeleteReply 回复已被删除");
            return;
        }
        Integer contentType = null;
        workTeacherReplyMapper.deleteById(delDTO.getId());
        WorkInfoPojo workInfo = workInfoPojoMongoService.getById(workTeacherReply.getWorkId());
        if (ReplyUserTypeEnums.TEACHER.getCode().equals(workTeacherReply.getReplyUserType())) {
            contentType = PrefectureContentAsyncTypeEnum.TEACHER_REPLY.getCode();
            if (!workTeacherReplyService.workHasReply(workTeacherReply.getWorkId(), workTeacherReply.getReplyUserType())) {
                workInfo.setTeacherReplyStatus(0);
                workInfo.setTeacherReplyTime(null);
            }
        } else if (workTeacherReply.getReplyUserType().equals(ReplyUserTypeEnums.COMPANY_USER.getCode())) {
            contentType = PrefectureContentAsyncTypeEnum.COMPANY_REPLY.getCode();
            if (!workTeacherReplyService.workHasReply(workTeacherReply.getWorkId(), workTeacherReply.getReplyUserType())) {
                workInfo.setCompanyReplyStatus(0);
                workInfo.setCompanyReplyTime(null);
            }
        }
        workInfo.setUpdateTime(LocalDateTime.now());
        WorkInfo mySqlWorkInfo = new WorkInfo();
        BeanUtil.copyProperties(workInfo,mySqlWorkInfo);
        mySqlWorkInfo.setId(workInfo.getWorkId());
        workInfoMapper.updateById(mySqlWorkInfo);
        workInfoPojoMongoService.saveOrUpdate(workInfo);
        prefectureComponent.workContentAsyncPrefecture(workInfo, contentType);
    }

    @Override
    public AdditionalInfoVO additionalInfo(Integer courseId) {
        CourseConfig courseConfig = courseComponent.courseConfigDetail(-1, courseId);
        if (Objects.isNull(courseConfig)) {
            return null;
        }
        log.info("courseConfig" + JSON.toJSONString(courseConfig));
        CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(courseId, null);
        if (Objects.isNull(courseTimetableUnlockDetailVO)) {
            return null;
        }
        log.info("courseTimetableUnlockDetailVO" + JSON.toJSONString(courseTimetableUnlockDetailVO));
        AdditionalInfoVO additionalInfoVO = oldServerComponent.additionalInfo(courseId, courseTimetableUnlockDetailVO.getWorkBeginTime().toLocalDate());
        if (Objects.isNull(additionalInfoVO)) {
            return null;
        }
        additionalInfoVO.setAdditionalScore(courseConfig.getExtendStudyScore());
        return additionalInfoVO;
    }

    @Override
    public Boolean addAdditionalScore(Integer courseId, Integer courseTimetableId, HeaderUserInfo headerUserInfo) {
        DoWorkMsgDTO doWorkMsgDTO = new DoWorkMsgDTO();
        doWorkMsgDTO.setCourseId(courseId);
        doWorkMsgDTO.setCompanyId(headerUserInfo.getCurrentCompanyId());
        doWorkMsgDTO.setUserId(headerUserInfo.getId());
        doWorkMsgDTO.setCourseTimeTableId(courseTimetableId);
        doWorkMsgDTO.setTag(WorkKafkaMessageEnum.ADDITIONAL.getCode());
        this.syncAddAdditionalScore(doWorkMsgDTO);
        return true;
    }

    @Override
    public void syncAddAdditionalScore(DoWorkMsgDTO dto) {
        log.info("syncAddAdditionalScore dto:{}", JSON.toJSONString(dto));
        CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(dto.getCourseId(), dto.getCourseTimeTableId(), dto.getUserId());
        CourseConfig courseConfig = courseComponent.courseConfigDetail(dto.getCompanyId(), dto.getCourseId());
        UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(dto.getCourseId(), dto.getUserId());
        dto.setCompanyId(userRightsCourse.getCompanyId());

        //校验写作业时间
        this.checkWorkTime(courseTimetableUnlockDetailVO,courseConfig);

        Jedis jedis = JedisPoolImpl.getResource();
        JedisDistributedLock lock = new JedisDistributedLock(jedis, WorkKafkaMessageEnum.DO_WORK.getCode() + dto.getCompanyId() + "-" + dto.getCourseTimeTableId() + "-" + dto.getUserId(), 10000, 5000);//acquireTimeoutMillis:超过该时间没有获取到锁，直接返回false; expiryTimeMillis:锁过期时间)
        try {
            if (lock.acquireEp()) {
                WorkInfo workInfo = workInfoMapper.queryOneWork(dto.getUserId(), dto.getCourseId(), dto.getCompanyId(), courseTimetableUnlockDetailVO.getId());
                if (Objects.isNull(workInfo)) {
                    log.info("syncAddAdditionalScore 作业未查询到");
                    return;
                }
                AdditionalInfoVO additionalInfoVO = oldServerComponent.additionalInfo(courseConfig.getCourseId(), courseTimetableUnlockDetailVO.getWorkBeginTime().toLocalDate());
                if (Objects.isNull(additionalInfoVO)) {
                    log.info("syncAddAdditionalScore additionalInfoVO 未查询到");
                    return;
                }
                if (StringUtils.isNotBlank(workInfo.getAdditional()) && additionalParse(workInfo.getAdditional()).getStatus().equals(YesOrNoEnum.YES.getCode())) {
                    log.info("syncAddAdditionalScore 分享加分已加过分");
                    return;
                }
                log.info("syncAddAdditionalScore 开始分享加分");
                additionalInfoVO.setAdditionalScore(courseConfig.getExtendStudyScore());
                additionalInfoVO.setStatus(YesOrNoEnum.YES.getCode());
                workInfo.setScore(workInfo.getScore() + courseConfig.getExtendStudyScore());
                workInfo.setAdditional(JSONObject.toJSONString(additionalInfoVO));
                UserStudyInfo userStudyInfo = userStudyInfoMapper.selectOne(new QueryWrapper<UserStudyInfo>()
                        .lambda()
                        .eq(UserStudyInfo::getCourseId, dto.getCourseId())
                        .eq(UserStudyInfo::getCompanyId, dto.getCompanyId())
                        .eq(UserStudyInfo::getUserId, dto.getUserId()));
                userStudyInfo.setScore(userStudyInfo.getScore() + courseConfig.getExtendStudyScore());
                userStudyInfoMapper.updateById(userStudyInfo);
                workInfoMapper.saveOrUpdate(workInfo);
                workInfoPojoMongoService.saveOrUpdate(workInfo);
                DoWorkSendKafkaBO doWorkSendKafkaBO = this.workSendKafkaBoBuild(
                        dto.getCompanyId(),
                        workInfo.getClassesId(),
                        workInfo.getTeamId(),
                        workInfo.getUserId(),
                        workInfo.getCourseId(),
                        workInfo.getCourseTimetableId(),
                        YesOrNoEnum.YES.getCode().equals(workInfo.getListenStatus()),
                        YesOrNoEnum.YES.getCode().equals(workInfo.getWorkStatus()));
                doWorkSendKafkaBO.setScore(workInfo.getScore());
                doWorkSendKafkaBO.setTag(WorkKafkaMessageEnum.SHAR.getCode());
                kafkaProduction.send(workTopic, String.valueOf(doWorkSendKafkaBO.getUserId()), JSONObject.parseObject(JSONObject.toJSONString(doWorkSendKafkaBO)));
                generateRank(DoWorkSendKafkaBO.builder()
                        .courseTimeTableId(dto.getCourseTimeTableId())
                        .userId(dto.getUserId())
                        .companyId(dto.getCompanyId())
                        .courseId(dto.getCourseId())
                        .build());
            } else {
                log.info("syncAddAdditionalScore 加锁失败");
                throw new BusinessException("延伸作业分享异常,请重新分享");
            }
        } catch (InterruptedException e) {
            log.error("syncAddAdditionalScore error:{}", e.getMessage(), e);
            throw new BusinessException("延伸作业分享异常,请重新分享");
        } finally {
            lock.release();
        }
    }

    /**
     * @param dto,headerUserInfo
     * @Return List<AppWorkInfoListVO>
     * <AUTHOR>
     * @Date 2022年5月17日17:08:33
     * @Description App推荐作业列表
     */
    @Override
    public PageInfoBT<AppWorkInfoListVO> recommendWorkList(AppWorkRecommendSearchDTO dto, HeaderUserInfo headerUserInfo) {
        CourseInfo courseInfo = courseComponent.courseInfoById(dto.getCourseId());
        IPage<WorkInfoPojo> workInfoPage = workInfoPojoMongoService.recommendWorkList(dto);
        if (CollUtil.isEmpty(workInfoPage.getRecords())) {
            return PageInfoBT.noData();
        }
        List<AppWorkInfoListVO> voList = workInfoPage.getRecords().stream().map(workInfo -> {
            // 转换老版本作业信息 解析作业内容
            return convertAppWorkInfoListVO(workInfo, null, false, headerUserInfo);
        }).collect(Collectors.toList());
        // 批量查询学员学分
        List<Integer> userIds = voList.stream().map(AppWorkInfoListVO::getUserId).collect(Collectors.toList());
        List<UserStudyInfo> userStudyInfoList = userStudyInfoMapper.selectList(new LambdaQueryWrapper<UserStudyInfo>()
                .eq(UserStudyInfo::getCourseId, dto.getCourseId())
                .in(UserStudyInfo::getUserId, userIds));
        Map<Integer, Integer> userStudyScoreMap = userStudyInfoList.stream().collect(Collectors.toMap(UserStudyInfo::getUserId, UserStudyInfo::getScore, (v1, v2) -> v1));
        // 构建心得关联老师回复数据
        List<Integer> workIds = voList.stream().map(AppWorkInfoListVO::getId).collect(Collectors.toList());
        Map<Integer, List<AppWorkTeacherReplyVO>> replyMap = findTeacherReplyListByWorkIds(workIds, headerUserInfo.getId(), null);
        Map<Integer, Integer> oldUserMap = new HashMap<>();
        if (WorkModelFlagEnum.DIFF_OLD_NEW.getCode().equals(courseInfo.getWorkModelFlag())) {
            oldUserMap = workInfoMapper.checkOldUserByUserIds(dto.getCourseId(), userIds).stream().collect(Collectors.toMap(Integer::intValue, userId -> userId));
        }
        Map<Integer, Integer> finalOldUserMap = oldUserMap;
        voList.forEach(workInfo -> {
            workInfo.setUserFlag(UserFlagEnum.NEW_USER.getCode());
            workInfo.setWorkModelFlag(courseInfo.getWorkModelFlag());
            if (WorkModelFlagEnum.DIFF_OLD_NEW.getCode().equals(courseInfo.getWorkModelFlag())) {
                if (Objects.nonNull(finalOldUserMap.get(workInfo.getUserId()))) {
                    workInfo.setUserFlag(UserFlagEnum.OLD_USER.getCode());
                }
            }
            workInfo.setWorkModelFlag(courseInfo.getWorkModelFlag());
            workInfo.setClassTeacherName(StringUtils.isNotBlank(workInfo.getClassTeacherName()) ?
                    workInfo.getClassTeacherName() : "---");
            workInfo.setTeacherReplyList(replyMap.get(workInfo.getId()));
            // 获取总学分
            workInfo.setScoreTotal(userStudyScoreMap.getOrDefault(workInfo.getUserId(), 0));
        });
        PageInfoBT<AppWorkInfoListVO> pageInfo = new PageInfoBT<>();
        pageInfo.setCurrent((long) dto.getPageNum());
        pageInfo.setSize((long) dto.getPageSize());
        pageInfo.setTotal(workInfoPage.getTotal());
        pageInfo.setRecords(voList);
        return pageInfo;
    }

    private Map<Integer, ClassMemberUserInfoVO> classesMemberMap(Integer courseId, List<Integer> classesNos, Integer userRole) {
        Map<Integer, ClassMemberUserInfoVO> classMemberMap = new HashMap<>();
        ClassMemberUserInfoDTO queryClassMemberDTO = new ClassMemberUserInfoDTO();
        queryClassMemberDTO.setClassGroupIds(classesNos);
        queryClassMemberDTO.setCourseId(courseId);
        queryClassMemberDTO.setUserRole(userRole);
        List<ClassMemberUserInfoVO> classMemberUserInfoVOList = userComponent.classMemberUserByEntity(queryClassMemberDTO);
        if (CollectionUtil.isNotEmpty(classMemberUserInfoVOList)) {
            if (Objects.equals(UserRoleEnums.TEACHER.getValue(), userRole)) {
                classMemberMap = classMemberUserInfoVOList.stream().collect(Collectors.toMap(ClassMemberUserInfoVO::getUserId, memberUserInfo -> memberUserInfo));
            } else if (Objects.equals(UserRoleEnums.CLASS_TEACHER.getValue(), userRole)) {
                classMemberMap = classMemberUserInfoVOList.stream().collect(Collectors.toMap(ClassMemberUserInfoVO::getClassGroupId, memberUserInfo -> memberUserInfo));
            }
        }
        return classMemberMap;
    }

    @Override
    public ClassGroupRoleVO recommendWork(Integer workId, HeaderUserInfo headerUserInfo) {
        WorkInfoPojo workInfo = workInfoPojoMongoService.getById(workId);
        if (Objects.isNull(workInfo)) {
            throw new BusinessException("作业信息不存在");
        }
        ClassGroupRoleVO classGroupRoleVO = new ClassGroupRoleVO();
        headerUserInfo.setCurrentCompanyId(workInfo.getCompanyId());
        if (Objects.nonNull(workInfo.getCompanyId()) && workInfo.getCompanyId().equals(-1)) {
            CourseConfig courseConfig = courseComponent.courseConfigDetail(-1, workInfo.getCourseId());
            if (Objects.isNull(courseConfig)) {
                log.info("recommendWork courseConfig is null, don't recommend work.");
                throw new BusinessException("不支持推荐作业");
            }
            if (courseConfig.getShowWorkSwitch().equals(YesOrNoEnum.NO.getCode())) {
                log.info("recommendWork showWorkSwitch is no, don't recommend work.");
                throw new BusinessException("不支持推荐作业");
            }
            if (Objects.isNull(courseConfig.getRecommendWorkType())) {
                log.info("recommendWork recommendWorkType is null, don't recommend work.");
                throw new BusinessException("不支持推荐作业");
            }
            if (RecommendWorkTypeEnum.NUMBER.getCode().equals(courseConfig.getRecommendWorkType())) {
                if (Objects.isNull(courseConfig.getRecommendWorkTotalLimit())) {
                    log.info("recommendWork recommendWorkTotalLimit is null, don't recommend work.");
                    throw new BusinessException("不支持推荐作业");
                }
                if (courseConfig.getRecommendWorkTotalLimit() < 1) {
                    log.info("recommendWork recommendWorkTotalLimit < 1, don't recommend work.");
                    throw new BusinessException("今日推荐作业数量已达到上限");
                }
            } else if (RecommendWorkTypeEnum.PERSON_NUM.getCode().equals(courseConfig.getRecommendWorkType())) {
                Integer personNum = classesComponent.memberCountByClassesId(workInfo.getClassesId());
                if (Objects.nonNull(courseConfig.getRecommendWorkUserLimit())) {
                    courseConfig.setRecommendWorkTotalLimit(personNum % courseConfig.getRecommendWorkUserLimit());
                } else {
                    log.info("recommendWork recommendWorkUserLimit is null, don't recommend work.");
                    throw new BusinessException("不支持推荐作业");
                }
            } else if (RecommendWorkTypeEnum.UNLIMITED.getCode().equals(courseConfig.getRecommendWorkType())) {
                log.info("recommendWork recommendWorkType is unlimited, recommend work.");
            } else {
                log.info("recommendWork recommendWorkType is error, don't recommend work.");
                throw new BusinessException("不支持推荐作业");
            }
            CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(workInfo.getCourseId(), null);
            if (Objects.isNull(courseTimetableUnlockDetailVO)) {
                log.info("recommendWork courseTimetableUnlockDetailVO is null, don't recommend work.");
                throw new BusinessException("推荐作业时间已过");
            }
            if (!(LocalDateTime.now().isAfter(courseTimetableUnlockDetailVO.getWorkBeginTime()) && LocalDateTime.now().isBefore(courseTimetableUnlockDetailVO.getWorkEndTime()))) {
                log.info("recommendWork don't in work time, don't recommend work.");
                throw new BusinessException("推荐作业时间已过");
            }
            long recommendNum = workInfoPojoMongoService.todayRecommendCount(workInfo.getCompanyId(), workInfo.getClassesId());
            if (RecommendWorkTypeEnum.NUMBER.getCode().equals(courseConfig.getRecommendWorkType())) {
                if (recommendNum >= courseConfig.getRecommendWorkTotalLimit()) {
                    throw new BusinessException("今日推荐作业数量已达到上限");
                }
                classGroupRoleVO.setRecommendNumber(courseConfig.getRecommendWorkTotalLimit());
            } else if (RecommendWorkTypeEnum.UNLIMITED.getCode().equals(courseConfig.getRecommendWorkType())) {
                classGroupRoleVO.setRecommendNumber(999999);
            } else {
                ClassStudyStatistics classStudyStatistics = classStudyStatisticsMapper.selectOne(new QueryWrapper<ClassStudyStatistics>().lambda()
                        .eq(ClassStudyStatistics::getClassesId, workInfo.getClassesId())
                        .eq(ClassStudyStatistics::getCompanyId, workInfo.getCompanyId())
                        .eq(ClassStudyStatistics::getCourseTimetableId, workInfo.getCourseTimetableId()));
                if (Objects.isNull(classStudyStatistics)) {
                    throw new BusinessException("推荐作业时间已过");
                }
                if ((classStudyStatistics.getClassMemberTotal() / courseConfig.getRecommendWorkUserLimit()) - recommendNum < 1) {
                    throw new BusinessException("今日推荐作业数量已达到上限");
                }
                classGroupRoleVO.setRecommendNumber(classStudyStatistics.getClassMemberTotal() / courseConfig.getRecommendWorkUserLimit());
            }
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
            classGroupRoleVO.setDoneDate(courseTimetableUnlockDetailVO.getWorkBeginTime().toLocalDate().format(formatter));
        }
        if (StringUtils.isNotBlank(headerUserInfo.getAppOs()) && (headerUserInfo.getAppOs().equals("ios") || headerUserInfo.getAppOs().equals("android"))) {
            workInfo.setRecommendSource(RecommendSourceEnum.CLASS_TEACHER.getCode());
        } else if (StringUtils.isNotBlank(headerUserInfo.getAppOs()) && headerUserInfo.getAppOs().equals("web")) {
            workInfo.setRecommendSource(RecommendSourceEnum.SAAS.getCode());
        } else {
            workInfo.setRecommendSource(RecommendSourceEnum.MANAGE_PLATFORM.getCode());
        }
        workInfo.setRecommendStatus(YesOrNoEnum.YES.getCode());
        workInfo.setRecommendTime(LocalDateTime.now());
        WorkInfo mySqlWorkInfo = new WorkInfo();
        BeanUtil.copyProperties(workInfo, mySqlWorkInfo);
        mySqlWorkInfo.setId(workInfo.getWorkId());
        workInfoMapper.updateById(mySqlWorkInfo);
        workInfoPojoMongoService.saveOrUpdate(workInfo);
        // 推送消息通知
        if (!Objects.equals(workInfo.getUserId(), headerUserInfo.getId()) && headerUserInfo.getCurrentCompanyId() == -1) {
            commonAsyncService.recommendWorkPushMsg(headerUserInfo, workInfo);
        }
        prefectureComponent.workContentAsyncPrefecture(workInfo, PrefectureContentAsyncTypeEnum.RECOMMEND_WORK.getCode());
        return classGroupRoleVO;
    }

    @Override
    public ClassHworkStatisticsVO classStatisticsByPerson(ClassStatisticsByPersonDTO dto, HeaderUserInfo headerUserInfo) {
        CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO;
        String planSchemeOption = dto.getPlanSchemeOption();
        if ("-1".equals(planSchemeOption)) {
            dto.setPlanSchemeOption("");
        }
        if (StrUtil.isBlank(dto.getPlanSchemeOption())) {
            courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(dto.getCourseId(), null, headerUserInfo.getId());
            if (Objects.nonNull(courseTimetableUnlockDetailVO) && "-1".equals(planSchemeOption)) {
                dto.setPlanSchemeOption(courseTimetableUnlockDetailVO.getPlanSchemeOption());
            }
        } else {
            courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(dto.getCourseId(), null, dto.getPlanSchemeOption());
        }
        if ("-1".equals(dto.getPlanSchemeOption())) {
            dto.setPlanSchemeOption(null);
        }
        UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(dto.getCourseId(), headerUserInfo.getId());
        headerUserInfo.setCurrentCompanyId(userRightsCourse.getCompanyId());
        List<MemberListByCourseIdClassesNoTeamIdVO> memberList = classesComponent.memberListByCourseIdClassesNoTeamId(userRightsCourse.getCompanyId(), dto.getCourseId(), String.valueOf(dto.getClassesNo()), null);
        if (CollUtil.isEmpty(memberList)) {
            log.info("classStatisticsByPerson memberList is empty.");
            return this.classStatisticsBuild(null, new ArrayList<>(), courseTimetableUnlockDetailVO, null, headerUserInfo);
        }
        ClassesTeamDetailVO classesTeamInfoByUser = classesComponent.listenGetClassesTeamInfoByUser(headerUserInfo.getCurrentCompanyId(), dto.getCourseId(), headerUserInfo.getId());
        if (Objects.nonNull(classesTeamInfoByUser)) {
            dto.setClassesId(classesTeamInfoByUser.getClassesId());
        }
        Map<Integer, MemberListByCourseIdClassesNoTeamIdVO> userMap = memberList.stream().collect(Collectors.toMap(MemberListByCourseIdClassesNoTeamIdVO::getUserId, i -> i));
        if (Objects.isNull(courseTimetableUnlockDetailVO)) {
            log.info("classStatisticsByPerson courseTimetableUnlockDetailVO is null.");
            return this.classStatisticsBuild(null, new ArrayList<>(), courseTimetableUnlockDetailVO, userMap, headerUserInfo);
        }
        if (Objects.isNull(courseTimetableUnlockDetailVO.getWorkBeginTime()) || Objects.isNull(courseTimetableUnlockDetailVO.getWorkEndTime())) {
            log.info("classStatisticsByPerson courseTimetableUnlockDetailVO workBeginTime or workEndTime is null.");
            return this.classStatisticsBuild(null, new ArrayList<>(), courseTimetableUnlockDetailVO, userMap, headerUserInfo);
        }
        ClassStatisticsVO classStatisticsVO = classStudyStatisticsMapper.classStatisticsV2(dto.getClassesId(), dto.getClassesNo(), dto.getCourseId(), headerUserInfo.getId(), courseTimetableUnlockDetailVO.getId(), headerUserInfo.getCurrentCompanyId());
        if (Objects.isNull(classStatisticsVO)) {
            log.info("classStatisticsByPerson classStatisticsVO is null.");
            return this.classStatisticsBuild(classStatisticsVO, new ArrayList<>(), courseTimetableUnlockDetailVO, userMap, headerUserInfo);
        }
        // 历史学习情况
        ClassStatisticsVO historyClassStatisticsVO = getHistoryClassStatisticsVO(dto, headerUserInfo, courseTimetableUnlockDetailVO);
        classStatisticsVO.setHistoryWorkRate(historyClassStatisticsVO.getHistoryWorkRate());
        classStatisticsVO.setHistoryListenRate(historyClassStatisticsVO.getHistoryListenRate());

        List<Integer> allUserIdList = memberList.stream().map(MemberListByCourseIdClassesNoTeamIdVO::getUserId).collect(Collectors.toList());
        // 班级下小组下所有人的作业信息
        List<WorkInfoPojo> workInfos = workInfoPojoMongoService.querySimpleList(new WorkInfoPojoQueryDTO().setCompanyId(headerUserInfo.getCurrentCompanyId())
                .setCourseTimetableId(courseTimetableUnlockDetailVO.getId()).setUserIdList(allUserIdList));
        // 写过作业的学员 如果选择了方案，则只统计该方案下的写过作业的
        List<WorkInfoPojo> workUserList = workInfos.stream().filter(i -> Objects.equals(i.getWorkStatus(), 1))
                .filter(i -> StrUtil.isNotBlank(dto.getPlanSchemeOption()) && dto.getPlanSchemeOption().equals(i.getLevel()))
                .collect(Collectors.toList());
        Set<Integer> removeUserIdSet = new HashSet<>();
        for (WorkInfoPojo workInfo : workInfos.stream().filter(i -> Objects.equals(i.getWorkStatus(), 1)).collect(Collectors.toList())) {
            removeUserIdSet.add(workInfo.getUserId());
        }
        // 没有写作业的人员
        List<Integer> notWorkUserIdList = allUserIdList.stream().filter(m -> !removeUserIdSet.contains(m)).collect(Collectors.toList());
        // 如果选择了作业方案
        if (StrUtil.isNotBlank(dto.getPlanSchemeOption())) {
            // 选择了学习方案的人员
            Map<Integer, String> userPlanMap = courseComponent.batchGetUserPlanSchemeOption(courseTimetableUnlockDetailVO.getId(), allUserIdList);
            // 选择了前端选择的方案的人员
            userPlanMap.entrySet().removeIf(entry -> !dto.getPlanSchemeOption().equals(entry.getValue()));
            Set<Integer> chooseUserIdSet = new HashSet<>(userPlanMap.keySet());
            notWorkUserIdList = notWorkUserIdList.stream().filter(chooseUserIdSet::contains).collect(Collectors.toList());
            // 听过课的学员
            List<WorkInfoPojo> listenUserList = workInfos.stream().filter(i -> Objects.equals(i.getListenStatus(), 1))
                    .filter(i -> StrUtil.isNotBlank(dto.getPlanSchemeOption()) && dto.getPlanSchemeOption().equals(i.getLevel()))
                    .filter(i -> chooseUserIdSet.contains(i.getUserId()))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(chooseUserIdSet)) {
                classStatisticsVO.setWorkRate(String.format("%.2f", (double) workUserList.size() / chooseUserIdSet.size() * 100));
                classStatisticsVO.setListenRate(String.format("%.2f", (double) listenUserList.size() / chooseUserIdSet.size() * 100));
            } else {
                classStatisticsVO.setWorkRate("0.00");
                classStatisticsVO.setListenRate("0.00");
            }
        }

        // 接下来拼装没有写作业的人员的信息
        List<Integer> finalNotWorkUserIdList = notWorkUserIdList;
        List<MemberListByCourseIdClassesNoTeamIdVO> notWorkUserList = memberList.stream().filter(i -> finalNotWorkUserIdList.contains(i.getUserId())).collect(Collectors.toList());

        if (WorkModelFlagEnum.DIFF_OLD_NEW.getCode().equals(courseTimetableUnlockDetailVO.getWorkModelFlag())) {
            Map<Integer, Integer> oldUserMap = workInfoMapper.checkOldUserByUserIds(dto.getCourseId(), notWorkUserIdList).stream().collect(Collectors.toMap(Integer::intValue, userId -> userId));
            for (MemberListByCourseIdClassesNoTeamIdVO userInfo : notWorkUserList) {
                userInfo.setUserFlag(false);
                if (!Objects.nonNull(oldUserMap.get(userInfo.getUserId()))) {
                    userInfo.setUserFlag(true);
                }
            }
        }
        if (WorkModelFlagEnum.LEVEL_WORK_TEMPLATE.getCode().equals(courseTimetableUnlockDetailVO.getWorkModelFlag())) {
            // 查询作业归属等级
            List<WorkUserModel> workUserModelList = workUserModelService.getByUserIdListAndCourseId(notWorkUserIdList, dto.getCourseId());
            Map<Integer, String> workUserModelMap = workUserModelList.stream().collect(Collectors.toMap(WorkUserModel::getUserId, WorkUserModel::getLevel));
            notWorkUserList.forEach(userInfo -> Optional.ofNullable(workUserModelMap.get(userInfo.getUserId())).ifPresent(userInfo::setLevel));
        }
        ClassHworkStatisticsVO classHworkStatisticsVO = this.classStatisticsBuild(classStatisticsVO, notWorkUserList, courseTimetableUnlockDetailVO, userMap, headerUserInfo);
        if (StrUtil.isNotBlank(courseTimetableUnlockDetailVO.getPlanSchemeOption()) && StrUtil.isBlank(dto.getPlanSchemeOption())) {
            // 只要空格前面的
            classHworkStatisticsVO.setResTitle(StrUtil.subBefore(courseTimetableUnlockDetailVO.getResourceTitle(), " ", true));
        }
        classHworkStatisticsVO.setPlanSchemeOption(courseTimetableUnlockDetailVO.getPlanSchemeOption());
        classHworkStatisticsVO.setCourseTimetableId(courseTimetableUnlockDetailVO.getId());
        return classHworkStatisticsVO;

    }

    private ClassStatisticsVO getHistoryClassStatisticsVO(ClassStatisticsByPersonDTO dto, HeaderUserInfo headerUserInfo, CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO) {
        String redisKey = RedisConstants.CLASS_STATISTICS_NEW + courseTimetableUnlockDetailVO.getId() + ":" + headerUserInfo.getCurrentCompanyId() + ":" + dto.getClassesId();
        String classStatisticsStr = redisUtil.get(redisKey, String.class);
        log.info("classStatisticsByPerson redisKey:{} value:{}", redisKey, classStatisticsStr);
        ClassStatisticsVO historyClassStatisticsVO;
        if (StrUtil.isBlank(classStatisticsStr)) {
            historyClassStatisticsVO = classStudyStatisticsMapper.classStatisticsHistoryV2(dto.getClassesId(), dto.getClassesNo(), dto.getCourseId(), headerUserInfo.getId(), courseTimetableUnlockDetailVO.getId(), headerUserInfo.getCurrentCompanyId());
            String historyStr = JSON.toJSONString(historyClassStatisticsVO);
            log.info("classStatisticsByPerson set historyStr:{}", historyStr);
            redisUtil.set(redisKey, historyStr, 24, TimeUnit.HOURS);
        } else {
            historyClassStatisticsVO = JSON.parseObject(classStatisticsStr, ClassStatisticsVO.class);
        }
        return historyClassStatisticsVO;
    }

    public ClassHworkStatisticsVO classStatisticsBuild(ClassStatisticsVO classStatisticsVO, List<MemberListByCourseIdClassesNoTeamIdVO> userInfos, CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO, Map<Integer, MemberListByCourseIdClassesNoTeamIdVO> userMap, HeaderUserInfo headerUserInfo) {
        ClassHworkStatisticsVO vo = ClassHworkStatisticsVO
                .builder()
                .historySubRate(Objects.nonNull(classStatisticsVO) && StringUtils.isNotBlank(classStatisticsVO.getHistoryWorkRate()) ? classStatisticsVO.getHistoryWorkRate() : "0.00")
                .todaySubRate(Objects.nonNull(classStatisticsVO) && StringUtils.isNotBlank(classStatisticsVO.getWorkRate()) ? classStatisticsVO.getWorkRate() : "0.00")
                .classNoSubTotal(Objects.nonNull(classStatisticsVO) && Objects.nonNull(classStatisticsVO.getClassNoSubTotal()) ? classStatisticsVO.getClassNoSubTotal() : Objects.nonNull(userInfos) ? userInfos.size() : 0)
                .noSubTotal(Objects.nonNull(classStatisticsVO) && Objects.nonNull(classStatisticsVO.getClassMemberTotal()) ? classStatisticsVO.getClassMemberTotal() - classStatisticsVO.getSubTotal() : Objects.nonNull(userInfos) ? userInfos.size() : 0)
                .unsubUserName(new ArrayList<>())
                .noSubUserInfos(userInfos)
                .todayListenRate(Objects.nonNull(classStatisticsVO) && StringUtils.isNotBlank(classStatisticsVO.getListenRate()) ? classStatisticsVO.getListenRate() : "0.00")
                .historyListenRate(Objects.nonNull(classStatisticsVO) && StringUtils.isNotBlank(classStatisticsVO.getHistoryListenRate()) ? classStatisticsVO.getHistoryListenRate() : "0.00")
                .classNoListenTotal(Objects.nonNull(classStatisticsVO) && Objects.nonNull(classStatisticsVO.getClassNoListenTotal()) ? classStatisticsVO.getClassNoListenTotal() : Objects.nonNull(userInfos) ? userInfos.size() : 0)
                .listenTotal(Objects.nonNull(classStatisticsVO) && Objects.nonNull(classStatisticsVO.getListenTotal()) ? classStatisticsVO.getListenTotal() : 0)
                .userRole(Objects.nonNull(userMap) && Objects.nonNull(userMap.get(headerUserInfo.getId())) ? userMap.get(headerUserInfo.getId()).getUserRole() : 4)
                .chartsList(new ArrayList<>())
                .doneDate(Objects.nonNull(courseTimetableUnlockDetailVO) && Objects.nonNull(courseTimetableUnlockDetailVO.getWorkBeginTime()) ? courseTimetableUnlockDetailVO.getWorkBeginTime().toLocalDate() : null)
                .endDate(Objects.nonNull(courseTimetableUnlockDetailVO) && Objects.nonNull(courseTimetableUnlockDetailVO.getWorkEndTime()) ? courseTimetableUnlockDetailVO.getWorkEndTime().toLocalDate() : null)
                .endTime(LocalDateTime.now())
                .doWorkStartTime(Objects.nonNull(courseTimetableUnlockDetailVO) && Objects.nonNull(courseTimetableUnlockDetailVO.getWorkBeginTime()) ? courseTimetableUnlockDetailVO.getWorkBeginTime() : null)
                .doWorkEndTime(Objects.nonNull(courseTimetableUnlockDetailVO) && Objects.nonNull(courseTimetableUnlockDetailVO.getWorkEndTime()) ? courseTimetableUnlockDetailVO.getWorkEndTime() : null)
                .resTitle(Objects.nonNull(courseTimetableUnlockDetailVO) && StringUtils.isNotBlank(courseTimetableUnlockDetailVO.getResourceTitle()) ? courseTimetableUnlockDetailVO.getResourceTitle() : "")
                .remindStatus(true)
                .planFlag(Objects.nonNull(courseTimetableUnlockDetailVO) ? courseTimetableUnlockDetailVO.getPlanFlag() : 0)
                .build();
        log.info("classStatisticsBuild:{}", JSON.toJSONString(vo));
        return vo;
    }

    @Override
    public PageInfoBT<ToDayWorkVO> todayWorking(WorkingByDateDTO dto, HeaderUserInfo headerUserInfo) {
        UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(dto.getCourseId(), headerUserInfo.getId());
        headerUserInfo.setCurrentCompanyId(userRightsCourse.getCompanyId());
        CourseInfo courseInfo = courseComponent.courseInfoById(dto.getCourseId());
        MySimpleClassesDetailVO mySimpleClassesDetailVO = classesComponent.innerMySimpleClassesDetail(dto.getCourseId(), headerUserInfo.getId());
        if (Objects.isNull(mySimpleClassesDetailVO) || Objects.isNull(mySimpleClassesDetailVO.getClassesNo())) {
            return PageInfoBT.from(0, new ArrayList<>());
        }
        if (Objects.equals(dto.getTeamId(), 9)) {
            dto.setTeamId(null);
        }
        CourseTimetable courseTimetable;
        if (Objects.isNull(dto.getCourseTimeTableId())) {
            if (Objects.isNull(dto.getBelongDate())) {
                CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(dto.getCourseId(), null);
                if (Objects.nonNull(courseTimetableUnlockDetailVO)) {
                    courseTimetable = BeanUtil.copyProperties(courseTimetableUnlockDetailVO, CourseTimetable.class);
                } else {
                    return PageInfoBT.from(0, new ArrayList<>());
                }
            } else {
                QueryByCourseIdAndDateDTO queryByCourseIdAndDateDTO = new QueryByCourseIdAndDateDTO();
                queryByCourseIdAndDateDTO.setCourseId(dto.getCourseId());
                queryByCourseIdAndDateDTO.setBelongDate(dto.getBelongDate());
                courseTimetable = courseComponent.queryByCourseIdAndDate(queryByCourseIdAndDateDTO);
            }
        } else {
            courseTimetable = courseComponent.getCourseTimetableDetail(dto.getCourseTimeTableId());
        }
        List<WorkInfoPojo> workInfoList = workInfoPojoMongoService.querySimpleList(new WorkInfoPojoQueryDTO().setCompanyId(headerUserInfo.getCurrentCompanyId()).setClassesId(mySimpleClassesDetailVO.getClassesId())
                .setTeamId(dto.getTeamId()).setCourseId(dto.getCourseId()).setCourseTimetableId(courseTimetable.getId()));
        if (CollUtil.isEmpty(workInfoList)) {
            return PageInfoBT.noData();
        }
        List<Integer> userIdList = workInfoList.stream().map(WorkInfoPojo::getUserId).collect(Collectors.toList());
        Map<Integer, String> userNameMap = getUserNameMap(userRightsCourse.getCompanyId(), userIdList);
        Map<Integer, UserStudyInfo> userStudyInfoMap = userStudyInfoMapper.workDate(dto, headerUserInfo, userIdList)
                .stream().collect(Collectors.toMap(UserStudyInfo::getUserId, Function.identity()));
        Map<Integer, Integer> oldUserMap = new HashMap<>();
        if (WorkModelFlagEnum.DIFF_OLD_NEW.getCode().equals(courseInfo.getWorkModelFlag())) {
            oldUserMap = workInfoMapper.checkOldUserByUserIds(dto.getCourseId(), userIdList).stream().collect(Collectors.toMap(Integer::intValue, userId -> userId));
        }
        Map<Integer, Integer> finalOldUserMap = oldUserMap;
        PageInfoBT<ToDayWorkVO> page = PageInfoBT.from(1, workInfoList.stream().map(workInfoPojo -> {
            UserFlagInfoVO userInfoVO = new UserFlagInfoVO();
            userInfoVO.setUserId(workInfoPojo.getUserId());
            userInfoVO.setUsername(workInfoPojo.getUserName());
            userInfoVO.setUserFlag(false);
            if (WorkModelFlagEnum.DIFF_OLD_NEW.getCode().equals(courseInfo.getWorkModelFlag())) {
                if (!Objects.nonNull(finalOldUserMap.get(userInfoVO.getUserId()))) {
                    userInfoVO.setUserFlag(true);
                }
            }
            UserStudyInfo userStudyInfo = userStudyInfoMap.get(userInfoVO.getUserId());
            ToDayWorkVO todayWorkVO = ToDayWorkVO.builder()
                    .doWorkStatus(workInfoPojo.getWorkStatus())
                    .listenStatus(workInfoPojo.getListenStatus())
                    .username(userInfoVO.getUsername())
                    .score(workInfoPojo.getScore())
                    .userId(userInfoVO.getUserId())
                    .userFlag(userInfoVO.getUserFlag())
                    .workModelFlag(workInfoPojo.getWorkModelFlag())
                    .level(workInfoPojo.getLevel())
                    .workTime(workInfoPojo.getWorkTime())
                    .build();
            String userName = userNameMap.get(userInfoVO.getUserId());
            if (StrUtil.isNotBlank(userName)) {
                todayWorkVO.setUsername(userName);
            }
            if (StrUtil.isBlank(todayWorkVO.getLevel())) {
                todayWorkVO.setLevel(userInfoVO.getLevel());
            }
            if (userStudyInfo != null) {
                todayWorkVO.setTotalScore(userStudyInfo.getScore());
            }
            if (Objects.isNull(todayWorkVO.getTotalScore())) {
                todayWorkVO.setTotalScore(0);
            }
            return todayWorkVO;
        }).collect(Collectors.toList()));
        // 如果今天没写作业,作业模板等级就从 WorkUserModel 表里取
        if (WorkModelFlagEnum.LEVEL_WORK_TEMPLATE.getCode().equals(courseInfo.getWorkModelFlag())) {
            Map<Integer, String> levelMap = workUserModelService.getByUserIdListAndCourseId(userIdList, dto.getCourseId())
                    .stream().collect(Collectors.toMap(WorkUserModel::getUserId, WorkUserModel::getLevel));
            page.getRecords().forEach(user -> {
                if (StrUtil.isBlank(user.getLevel())) {
                    user.setLevel(levelMap.get(user.getUserId()));
                }
            });
        }
        // 如果今天没写作业，作业方案
        if (WorkModelFlagEnum.UNIFY.getCode().equals(courseInfo.getWorkModelFlag()) && Objects.equals(courseTimetable.getPlanFlag(), 1)) {
            Map<Integer, String> map = courseComponent.batchGetUserPlanSchemeOption(courseTimetable.getId(), userIdList);
            page.getRecords().forEach(user -> {
                if (StrUtil.isBlank(user.getLevel())) {
                    user.setLevel(map.get(user.getUserId()));
                }
            });
        }
        if (StrUtil.isNotBlank(dto.getPlanSchemeOption())) {
            Map<Integer, String> userPlanMap = courseComponent.batchGetUserPlanSchemeOption(dto.getCourseTimeTableId(), userIdList);
            userPlanMap.entrySet().removeIf(i -> !i.getValue().equals(dto.getPlanSchemeOption()));
            page.getRecords().removeIf(i -> Objects.isNull(userPlanMap.get(i.getUserId())));
        }
        return page;
    }

    private Map<Integer, String> getUserNameMap(Integer companyId, List<Integer> userIdList) {
        if (Objects.isNull(companyId)) {
            return new HashMap<>();
        }
        if (Objects.equals(companyId, -1)) {
            return userComponent.userFullNameByIdList(userIdList);
        } else {
            return userComponent.queryUserCompanyNameById(companyId, userIdList);
        }
    }

    @Override
    public PageInfoBT<HistoryWorkingVO> historyWorking(HistoryWorkingDTO dto, HeaderUserInfo headerUserInfo) {
        log.info("historyWorking,userId:{}, 入参:{}", headerUserInfo.getId(), JSON.toJSONString(dto));
        UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(dto.getCourseId(), headerUserInfo.getId());
        headerUserInfo.setCurrentCompanyId(userRightsCourse.getCompanyId());
        CourseClassTeamDTO courseClassTeamDTO = new CourseClassTeamDTO();
        courseClassTeamDTO.setCourseId(dto.getCourseId());
        courseClassTeamDTO.setSortMode(2);
        PageInfoBT<CourseTimetable> courseHistoryTimetables = courseComponent.getCourseHistoryTimetablePage(courseClassTeamDTO, dto.getPageSize(), dto.getPageNum());
        if (courseHistoryTimetables.getRecords().size() < 1) {
            return PageInfoBT.from(0, new ArrayList<>());
        }
        List<CourseTimetable> courseTimetableHistoryVOS = courseHistoryTimetables.getRecords();
        ClassesTeamDetailVO classesTeamInfoByUser = classesComponent.getClassesTeamInfoByUser(headerUserInfo.getCurrentCompanyId(), dto.getCourseId(), headerUserInfo.getId());
        if (Objects.nonNull(classesTeamInfoByUser)) {
            dto.setClassesId(classesTeamInfoByUser.getClassesId());
        }
        CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(dto.getCourseId(), null);
        if (Objects.nonNull(courseTimetableUnlockDetailVO)) {
            courseTimetableHistoryVOS = courseTimetableHistoryVOS.stream()
                    .filter(courseTimetableHistoryVO -> !courseTimetableHistoryVO.getId().equals(courseTimetableUnlockDetailVO.getId()))
                    .collect(Collectors.toList());
        }
        // teamId 等于 9 的时候，代表全部成员。。。
        if (Objects.nonNull(dto.getTeamId()) && !dto.getTeamId().equals(9)) {
            Map<Integer, TeamStatisticsVO> teamStatisticsVOMap = workInfoMapper.teamStatisticsV2(dto, Objects.nonNull(courseTimetableUnlockDetailVO) && courseTimetableUnlockDetailVO.getWorkEndTime().isAfter(LocalDateTime.now()) ? courseTimetableUnlockDetailVO.getId() : 0, headerUserInfo.getCurrentCompanyId()).stream().collect(Collectors.toMap(TeamStatisticsVO::getCourseTimeTableId, teamStatisticsVO -> teamStatisticsVO));
            List<HistoryWorkingVO> historyWorkingVOS = courseTimetableHistoryVOS.stream().map(courseHistoryTimetable -> {
                TeamStatisticsVO teamStatistics = teamStatisticsVOMap.get(courseHistoryTimetable.getId());
                if (Objects.nonNull(teamStatistics)) {
                    return HistoryWorkingVO
                            .builder()
                            .doWorkNum(teamStatistics.getWorkNum())
                            .listenNum(teamStatistics.getListenNum())
                            .belongDate(teamStatistics.getBelongDate())
                            .score(teamStatistics.getScore())
                            .courseTimeTableId(courseHistoryTimetable.getId())
                            .courseTimeTableName(courseHistoryTimetable.getResourceTitle())
                            .courseId(dto.getCourseId())
                            .planFlag(courseHistoryTimetable.getPlanFlag())
                            .build();
                } else {
                    return HistoryWorkingVO
                            .builder()
                            .doWorkNum(0)
                            .listenNum(0)
                            .score(0)
                            .belongDate(courseHistoryTimetable.getWorkBeginTime().toLocalDate())
                            .courseTimeTableId(courseHistoryTimetable.getId())
                            .courseTimeTableName(courseHistoryTimetable.getResourceTitle())
                            .planFlag(courseHistoryTimetable.getPlanFlag())
                            .courseId(dto.getCourseId())
                            .build();
                }
            }).collect(Collectors.toList());
            PageInfoBT pageInfoBT = new PageInfoBT<>();
            pageInfoBT.setRecords(historyWorkingVOS);
            pageInfoBT.setCurrent(courseHistoryTimetables.getCurrent());
            pageInfoBT.setSize(courseHistoryTimetables.getSize());
            pageInfoBT.setTime(courseHistoryTimetables.getTime());
            pageInfoBT.setTotal(courseHistoryTimetables.getTotal());
            return pageInfoBT;
        } else {
            Map<Integer, TeamStatisticsVO> teamStatisticsVOMap = workInfoMapper.classesStatisticsV2(dto, Objects.nonNull(courseTimetableUnlockDetailVO) && courseTimetableUnlockDetailVO.getWorkEndTime().isAfter(LocalDateTime.now()) ? courseTimetableUnlockDetailVO.getId() : 0, headerUserInfo.getCurrentCompanyId()).stream().collect(Collectors.toMap(TeamStatisticsVO::getCourseTimeTableId, teamStatisticsVO -> teamStatisticsVO));
            List<HistoryWorkingVO> historyWorkingVOS = courseTimetableHistoryVOS.stream().map(courseHistoryTimetable -> {
                TeamStatisticsVO teamStatistics = teamStatisticsVOMap.get(courseHistoryTimetable.getId());
                if (Objects.nonNull(teamStatistics)) {
                    return HistoryWorkingVO
                            .builder()
                            .doWorkNum(teamStatistics.getWorkNum())
                            .listenNum(teamStatistics.getListenNum())
                            .belongDate(teamStatistics.getBelongDate())
                            .score(teamStatistics.getScore())
                            .courseTimeTableId(courseHistoryTimetable.getId())
                            .courseTimeTableName(courseHistoryTimetable.getResourceTitle())
                            .planFlag(courseHistoryTimetable.getPlanFlag())
                            .courseId(dto.getCourseId())
                            .build();
                } else {
                    return HistoryWorkingVO
                            .builder()
                            .doWorkNum(0)
                            .listenNum(0)
                            .score(0)
                            .belongDate(courseHistoryTimetable.getWorkBeginTime().toLocalDate())
                            .courseTimeTableId(courseHistoryTimetable.getId())
                            .courseTimeTableName(courseHistoryTimetable.getResourceTitle())
                            .planFlag(courseHistoryTimetable.getPlanFlag())
                            .courseId(dto.getCourseId())
                            .build();
                }
            }).collect(Collectors.toList());

            PageInfoBT pageInfoBT = new PageInfoBT<>();
            pageInfoBT.setRecords(historyWorkingVOS);
            pageInfoBT.setCurrent(courseHistoryTimetables.getCurrent());
            pageInfoBT.setSize(courseHistoryTimetables.getSize());
            pageInfoBT.setTime(courseHistoryTimetables.getTime());
            pageInfoBT.setTotal(courseHistoryTimetables.getTotal());
            return pageInfoBT;
        }
    }

    @Override
    public List<MyWorkDateVO> memberWorking(MyWorkDTO dto, HeaderUserInfo headerUserInfo) {
        long l1 = System.currentTimeMillis();
        List<CourseTimetableHistoryVO> courseHistoryTimetables = courseComponent.getCourseHistoryTimetable(dto.getCourseId());
        if (CollUtil.isEmpty(courseHistoryTimetables)) {
            return new ArrayList<>();
        }
        UserFlagInfoVO userInfoVO = new UserFlagInfoVO();
        userInfoVO.setUserFlag(false);
        long l2 = System.currentTimeMillis();
        CourseInfo courseInfo = courseComponent.courseInfoById(dto.getCourseId());
        long l3 = System.currentTimeMillis();
        if (courseInfo.getWorkModelFlag().equals(WorkModelFlagEnum.DIFF_OLD_NEW.getCode())) {
            if (workInfoMapper.checkOldUser(dto.getCourseId(), dto.getUserId()) > 0) {
                userInfoVO.setUserFlag(true);
            }
        }
        long l4 = System.currentTimeMillis();
        UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(dto.getCourseId(), dto.getUserId());
        List<WorkInfoPojo> workInfoPojoList = workInfoPojoMongoService.querySimpleList(new WorkInfoPojoQueryDTO().setCourseId(dto.getCourseId())
                .setUserId(dto.getUserId()).setCompanyId(userRightsCourse.getCompanyId()));
        long l5 = System.currentTimeMillis();
        Map<Integer, WorkInfoPojo> workInfoMap = workInfoPojoList.stream().collect(Collectors.toMap(WorkInfoPojo::getCourseTimetableId, workInfo -> workInfo));
        List<PerSonMyWorkVO> myWorkVOS = new ArrayList<>();
        String jsonObject = JSON.toJSONString(courseHistoryTimetables);
        List<CourseTimetableHistoryVO> courseTimetableHistoryVOS = JSONObject.parseArray(jsonObject, CourseTimetableHistoryVO.class);
        courseTimetableHistoryVOS.forEach(courseTimetableHistoryVO -> {
            if (Objects.nonNull(workInfoMap.get(courseTimetableHistoryVO.getId()))) {
                WorkInfoPojo workInfo = workInfoMap.get(courseTimetableHistoryVO.getId());
                myWorkVOS.add(PerSonMyWorkVO
                        .builder()
                        .userFlag(userInfoVO.getUserFlag())
                        .courseTimetableId(courseTimetableHistoryVO.getId())
                        .doWorkStatus(workInfo.getWorkStatus())
                        .listenStatus(workInfo.getListenStatus())
                        .score(workInfo.getScore())
                        .belongDate(workInfo.getCourseBeginTime().toLocalDate())
                        .year(workInfo.getCourseBeginTime().getYear())
                        .workModelFlag(workInfo.getWorkModelFlag())
                        .level(workInfo.getLevel())
                        .build());
            } else {
                myWorkVOS.add(PerSonMyWorkVO
                        .builder()
                        .courseTimetableId(courseTimetableHistoryVO.getId())
                        .doWorkStatus(0)
                        .listenStatus(0)
                        .score(0)
                        .userFlag(userInfoVO.getUserFlag())
                        .belongDate(courseTimetableHistoryVO.getWorkBeginTime().toLocalDate())
                        .year(courseTimetableHistoryVO.getWorkBeginTime().getYear())
                        .workModelFlag(courseInfo.getWorkModelFlag())
                        .build());
            }
        });
        if (myWorkVOS.isEmpty()) {
            return new ArrayList<>();
        }
        Map<Integer, String> userOptionMap = courseComponent.batchGetTimetablePlanScheme(dto.getCourseId(), dto.getUserId(), myWorkVOS.stream().map(PerSonMyWorkVO::getCourseTimetableId).collect(Collectors.toList()));
        myWorkVOS.forEach(i -> {
            if (StrUtil.isBlank(i.getLevel())) {
                i.setLevel(userOptionMap.get(i.getCourseTimetableId()));
            }
        });
        // 单独给当天的课件做处理
        CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(dto.getCourseId(), null, dto.getUserId());
        if (Objects.nonNull(courseTimetableUnlockDetailVO)) {
            myWorkVOS.stream().filter(i -> Objects.equals(i.getCourseTimetableId(), courseTimetableUnlockDetailVO.getId()))
                    .findAny().ifPresent(i -> i.setLevel(courseTimetableUnlockDetailVO.getPlanSchemeOption()));
        }
        Set<Integer> years = new HashSet<>();
        for (PerSonMyWorkVO myWorkVO : myWorkVOS) {
            myWorkVO.setMonthWithDay((myWorkVO.getBelongDate().getMonthValue() < 10 ? "0" + myWorkVO.getBelongDate().getMonthValue() : myWorkVO.getBelongDate().getMonthValue() + "") + "月" + (myWorkVO.getBelongDate().getDayOfMonth() < 10 ? "0" + myWorkVO.getBelongDate().getDayOfMonth() : myWorkVO.getBelongDate().getDayOfMonth() + "") + "日");
            years.add(myWorkVO.getBelongDate().getYear());
        }
        Map<Integer, List<PerSonMyWorkVO>> myWorkMap = myWorkVOS.stream().collect(Collectors.groupingBy(PerSonMyWorkVO::getYear));
        log.info("memberWorking 耗时统计:l2 - l1:{}, l3 - l2:{}, l4 - l3:{}, l5 - l4:{}", l2 - l1, l3 - l2, l4 - l3, l5 - l4);
        return years.stream().sorted(Comparator.reverseOrder()).map(year -> {
            MyWorkDateVO myWorkDateVO = new MyWorkDateVO();
            myWorkDateVO.setYear(year);
            myWorkDateVO.setWorkList(myWorkMap.get(year));
            return myWorkDateVO;
        }).collect(Collectors.toList());
    }

    @Override
    public ClassHworkStatisticsVO getWorkGroupStatistics(ClassStatisticsDTO classStatisticsDTO, HeaderUserInfo headerUserInfo) {
        List<MemberListByCourseIdClassesNoTeamIdVO> userInfos = new ArrayList<>();
        ClassStatisticsVO classStatisticsVO = new ClassStatisticsVO();
        CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(classStatisticsDTO.getCourseId(), null);
        if (Objects.isNull(courseTimetableUnlockDetailVO)) {
            return this.classHworkStatisticsBuild(classStatisticsDTO, headerUserInfo.getId(), classStatisticsVO, userInfos, courseTimetableUnlockDetailVO);
        }
        String redisKey = RedisConstants.CLASS_STATISTICS + "teacher" + courseTimetableUnlockDetailVO.getId();
        String classStatisticsStr = redisUtil.get(redisKey, String.class);
        if (StringUtils.isNotBlank(classStatisticsStr)) {
            ClassStatisticsVO historyClassStatistics = JSON.parseObject(classStatisticsStr, ClassStatisticsVO.class);
            ClassStatisticsVO currentClassStatistics = classStudyStatisticsMapper.classStatistics(null, classStatisticsDTO.getClassesNo(), classStatisticsDTO.getCourseId(), headerUserInfo.getId(), courseTimetableUnlockDetailVO.getId(), headerUserInfo.getCurrentCompanyId());
            if (Objects.isNull(currentClassStatistics)) {
                classStatisticsVO.setHistoryWorkRate(historyClassStatistics.getHistoryWorkRate());
                classStatisticsVO.setHistoryListenRate(historyClassStatistics.getHistoryListenRate());
                return this.classHworkStatisticsBuild(classStatisticsDTO, headerUserInfo.getId(), classStatisticsVO, userInfos, courseTimetableUnlockDetailVO);
            } else {
                BeanUtils.copyProperties(currentClassStatistics, classStatisticsVO);
                classStatisticsVO.setHistoryWorkRate(historyClassStatistics.getHistoryWorkRate());
                classStatisticsVO.setHistoryListenRate(historyClassStatistics.getHistoryListenRate());
                return this.classHworkStatisticsBuild(classStatisticsDTO, headerUserInfo.getId(), classStatisticsVO, userInfos, courseTimetableUnlockDetailVO);
            }
        } else {
            ClassStatisticsVO currentClassStatistics = classStudyStatisticsMapper.classStatistics(null, classStatisticsDTO.getClassesNo(), classStatisticsDTO.getCourseId(), headerUserInfo.getId(), courseTimetableUnlockDetailVO.getId(), headerUserInfo.getCurrentCompanyId());
            ClassStatisticsVO historyClassStatistics = classStudyStatisticsMapper.classStatisticsHistory(null, classStatisticsDTO.getClassesNo(), classStatisticsDTO.getCourseId(), headerUserInfo.getId(), courseTimetableUnlockDetailVO.getId(), headerUserInfo.getCurrentCompanyId());
            if (Objects.nonNull(historyClassStatistics)) {
                classStatisticsVO.setHistoryWorkRate(historyClassStatistics.getHistoryWorkRate());
                classStatisticsVO.setHistoryListenRate(historyClassStatistics.getHistoryListenRate());
                redisTemplate.opsForValue().set(redisKey, JSON.toJSONString(classStatisticsVO), Duration.between(LocalDateTime.now(), LocalDateTime.of(LocalDate.now(), LocalTime.of(23, 59, 59))).getSeconds());
                redisTemplate.expireAt(redisKey, Date.from(LocalDateTime.of(LocalDate.now(), LocalTime.of(23, 59, 59)).atZone(ZoneId.systemDefault()).toInstant()));
            }
            if (Objects.isNull(currentClassStatistics)) {
                return this.classHworkStatisticsBuild(classStatisticsDTO, headerUserInfo.getId(), classStatisticsVO, userInfos, courseTimetableUnlockDetailVO);
            } else {
                BeanUtils.copyProperties(currentClassStatistics, classStatisticsVO);
                classStatisticsVO.setHistoryWorkRate(historyClassStatistics.getHistoryWorkRate());
                classStatisticsVO.setHistoryListenRate(historyClassStatistics.getHistoryListenRate());
                return this.classHworkStatisticsBuild(classStatisticsDTO, headerUserInfo.getId(), classStatisticsVO, userInfos, courseTimetableUnlockDetailVO);
            }
        }
    }

    public ClassHworkStatisticsVO classHworkStatisticsBuild(ClassStatisticsDTO classStatisticsDTO, Integer userId, ClassStatisticsVO classStatisticsVO, List<MemberListByCourseIdClassesNoTeamIdVO> userInfos, CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO) {
        // 是否是老师
        MySimpleClassesDetailVO mySimpleClassesDetailVO = classesComponent.innerMySimpleClassesDetail(classStatisticsDTO.getCourseId(), userId);
        return ClassHworkStatisticsVO
                .builder()
                .userRole(mySimpleClassesDetailVO != null ? mySimpleClassesDetailVO.getUserRole() : 4)
                .historySubRate(Objects.nonNull(classStatisticsVO) && StringUtils.isNotBlank(classStatisticsVO.getHistoryWorkRate()) ? classStatisticsVO.getHistoryWorkRate() : "0.00")
                .todaySubRate(Objects.nonNull(classStatisticsVO) && StringUtils.isNotBlank(classStatisticsVO.getWorkRate()) ? classStatisticsVO.getWorkRate() : "0.00")
                .classNoSubTotal(Objects.nonNull(classStatisticsVO) && Objects.nonNull(classStatisticsVO.getClassNoSubTotal()) ? classStatisticsVO.getClassNoSubTotal() : 0)
                .noSubTotal(Objects.nonNull(classStatisticsVO) && Objects.nonNull(classStatisticsVO.getClassMemberTotal()) && Objects.nonNull(classStatisticsVO.getSubTotal()) ? classStatisticsVO.getClassMemberTotal() - classStatisticsVO.getSubTotal() : 0)
                .unsubUserName(new ArrayList<>())
                .noSubUserInfos(userInfos)
                .todayListenRate(Objects.nonNull(classStatisticsVO) && StringUtils.isNotBlank(classStatisticsVO.getListenRate()) ? classStatisticsVO.getListenRate() : "0.00")
                .historyListenRate(Objects.nonNull(classStatisticsVO) && StringUtils.isNotBlank(classStatisticsVO.getHistoryListenRate()) ? classStatisticsVO.getHistoryListenRate() : "0.00")
                .classNoListenTotal(Objects.nonNull(classStatisticsVO) && Objects.nonNull(classStatisticsVO.getClassNoListenTotal()) ? classStatisticsVO.getClassNoListenTotal() : 0)
                .listenTotal(Objects.nonNull(classStatisticsVO) && Objects.nonNull(classStatisticsVO.getListenTotal()) ? classStatisticsVO.getListenTotal() : 0)
                .chartsList(new ArrayList<>())
                .doneDate(Objects.nonNull(courseTimetableUnlockDetailVO) && Objects.nonNull(courseTimetableUnlockDetailVO.getWorkBeginTime()) ? courseTimetableUnlockDetailVO.getWorkBeginTime().toLocalDate() : null)
                .endDate(Objects.nonNull(courseTimetableUnlockDetailVO) && Objects.nonNull(courseTimetableUnlockDetailVO.getWorkEndTime()) ? courseTimetableUnlockDetailVO.getWorkEndTime().toLocalDate() : null)
                .endTime(LocalDateTime.now())
                .doWorkStartTime(Objects.nonNull(courseTimetableUnlockDetailVO) && Objects.nonNull(courseTimetableUnlockDetailVO.getWorkBeginTime()) ? courseTimetableUnlockDetailVO.getWorkBeginTime() : null)
                .doWorkEndTime(Objects.nonNull(courseTimetableUnlockDetailVO) && Objects.nonNull(courseTimetableUnlockDetailVO.getWorkEndTime()) ? courseTimetableUnlockDetailVO.getWorkEndTime() : null)
                .resTitle(Objects.nonNull(courseTimetableUnlockDetailVO) && StringUtils.isNotBlank(courseTimetableUnlockDetailVO.getResourceTitle()) ? courseTimetableUnlockDetailVO.getResourceTitle() : "")
                .remindStatus(true)
                .build();
    }

    @Override
    public List<ClassRankVO> classRanking(Integer courseId, Integer type, HeaderUserInfo headerUserInfo) {
        UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(courseId, headerUserInfo.getId());
        headerUserInfo.setCurrentCompanyId(userRightsCourse.getCompanyId());
        CourseClassTeamDTO courseClassTeamDTO = new CourseClassTeamDTO();
        courseClassTeamDTO.setCourseId(courseId);
        courseClassTeamDTO.setSortMode(2);
        PageInfoBT<CourseTimetable> courseHistoryTimetable = courseComponent.getCourseHistoryTimetablePage(courseClassTeamDTO, 1, 1);
        if (courseHistoryTimetable.getRecords().size() < 1) {
            return new ArrayList<>();
        }
        String jsonObject = JSON.toJSONString(courseHistoryTimetable.getRecords());
        List<CourseTimetableHistoryVO> courseTimetableHistoryVOS = JSONObject.parseArray(jsonObject, CourseTimetableHistoryVO.class);
        MySimpleClassesDetailVO mySimpleClassesDetailVO = classesComponent.innerMySimpleClassesDetail(courseId, headerUserInfo.getId());
        List<String> userPhoneList = new ArrayList<>();
        Map<Integer, List<ClassSubRateVO>> classSubRateVOMap = classSubRateVOMap(courseTimetableHistoryVOS, type, courseId, headerUserInfo.getCurrentCompanyId());
        List<ClassRankVO> rankVOList = courseTimetableHistoryVOS.stream().map(courseTimetableHistoryVO -> {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
            List<ClassSubRateVO> classSubRateVOList = classSubRateVOMap.get(courseTimetableHistoryVO.getId());
            return ClassRankVO.builder()
                    .date(courseTimetableHistoryVO.getWorkBeginTime().format(formatter))
                    .currentRank(getCurrentRank(headerUserInfo.getCurrentCompanyId(), courseId, mySimpleClassesDetailVO.getClassesNo(), courseTimetableHistoryVO.getId()))
                    .classRanking(classSubRateVOList)
                    .build();
        }).collect(Collectors.toList());
        rankVOList.forEach(classRankVO -> {
            if (CollUtil.isNotEmpty(classRankVO.getClassRanking())) {
                classRankVO.getClassRanking().forEach(classSubRateVO -> {
                    if (StringUtils.isNotBlank(classSubRateVO.getClassTeacherPhone())) {
                        if (Pattern.matches("^[1-9][0-9]*$", classSubRateVO.getClassTeacherPhone())) {
                            classSubRateVO.setClassTeacherPhone(AESUtil.decrypt(classSubRateVO.getClassTeacherPhone()));
                        }
                        userPhoneList.add(classSubRateVO.getClassTeacherPhone());
                    }
                });
            }
        });
        Map<String, UserInfoVO> userInfoVOMap = new HashMap<>();
        if (userPhoneList.size() > 0) {
            UserInfoEntityListSearchDTO userInfoEntityListSearchDTO = new UserInfoEntityListSearchDTO();
            userInfoEntityListSearchDTO.setPhones(userPhoneList);
            userInfoVOMap = userComponent.userByEntityList(userInfoEntityListSearchDTO)
                    .stream().collect(Collectors.toMap(UserInfoVO::getPhone, userInfoVO -> userInfoVO));
        }
        Map<String, UserInfoVO> finalUserInfoVOMap = userInfoVOMap;
        rankVOList.forEach(classRankVO -> {
            if (CollUtil.isNotEmpty(classRankVO.getClassRanking())) {
                classRankVO.getClassRanking().forEach(classSubRateVO -> {
                    if (StringUtils.isNotBlank(classSubRateVO.getClassTeacherPhone())) {
                        UserInfoVO userFlagInfoVO = finalUserInfoVOMap.get(classSubRateVO.getClassTeacherPhone());
                        if (Objects.nonNull(userFlagInfoVO)) {
                            classSubRateVO.setClassTeacherName(userFlagInfoVO.getUserFullName());
                        }
                    }
                });
            }
        });
        return rankVOList;
    }

    private Map<Integer, List<ClassSubRateVO>> classSubRateVOMap(List<CourseTimetableHistoryVO> courseTimetableHistoryVOS, Integer type, Integer courseId, Integer currentCompanyId) {
        if (CollUtil.isEmpty(courseTimetableHistoryVOS)) return new HashMap<>();
        List<Integer> idList = courseTimetableHistoryVOS.stream().map(CourseTimetableHistoryVO::getId).collect(Collectors.toList());
        return classStudyStatisticsMapper.classStudyTop(idList, type, courseId, currentCompanyId)
                .stream().collect(Collectors.groupingBy(ClassSubRateVO::getCourseTimetableId));
    }

    @Override
    public List<ClassMemberRankVO> classMemberRanking(Integer courseId, HeaderUserInfo headerUserInfo) {
        CourseClassTeamDTO courseClassTeamDTO = new CourseClassTeamDTO();
        courseClassTeamDTO.setCourseId(courseId);
        courseClassTeamDTO.setSortMode(2);
        PageInfoBT<CourseTimetable> courseHistoryTimetable = courseComponent.getCourseHistoryTimetablePage(courseClassTeamDTO, 1, 1);
        if (courseHistoryTimetable.getRecords().isEmpty()) {
            return new ArrayList<>();
        }
        CourseInfo courseInfo = courseComponent.courseInfoById(courseId);
        String jsonObject = JSON.toJSONString(courseHistoryTimetable.getRecords());
        List<CourseTimetableHistoryVO> courseTimetableHistoryVOS = JSONObject.parseArray(jsonObject, CourseTimetableHistoryVO.class);
        List<Integer> userIds = new ArrayList<>();
        List<ClassMemberRankVO> classMemberRankList = courseTimetableHistoryVOS.stream().map(courseTimetableHistoryVO -> {
            Set<ZSetOperations.TypedTuple<Object>> typedTuples = redisTemplate.opsForZSet().reverseRangeWithScores(RedisConstants.RANK + courseTimetableHistoryVO.getId(), 0, 9);
            if (typedTuples.size() < 10) {
                List<TodayStudyUserTopVO> todayStudyUserTopVOS = classStudyStatisticsMapper.userStudyTop(courseId, headerUserInfo.getCurrentCompanyId(), courseTimetableHistoryVO.getWorkBeginTime());
                Map<Integer, Integer> oldUserMap = new HashMap<>();
                if (WorkModelFlagEnum.DIFF_OLD_NEW.getCode().equals(courseInfo.getWorkModelFlag())) {
                    List<Integer> userIdList = todayStudyUserTopVOS.stream().map(TodayStudyUserTopVO::getUserId).collect(Collectors.toList());
                    if (CollUtil.isEmpty(userIdList)) {
                        oldUserMap = new HashMap<>();
                    } else {
                        oldUserMap = workInfoMapper.checkOldUserByUserIds(courseId, userIdList).stream().collect(Collectors.toMap(Integer::intValue, userId -> userId));
                    }
                }
                Map<Integer, Integer> finalOldUserMap = oldUserMap;
                todayStudyUserTopVOS.forEach(todayStudyUserTopVO -> {
                    todayStudyUserTopVO.setUserFlag(false);
                    if (courseInfo.getWorkModelFlag().equals(WorkModelFlagEnum.DIFF_OLD_NEW.getCode())) {
                        if (Objects.isNull(finalOldUserMap.get(todayStudyUserTopVO.getUserId()))) {
                            todayStudyUserTopVO.setUserFlag(true);
                        }
                    }
                    redisTemplate.opsForZSet().add(RedisConstants.RANK + courseTimetableHistoryVO.getId(), todayStudyUserTopVO.getUserId(), todayStudyUserTopVO.getScore());
                    redisTemplate.expireAt(RedisConstants.RANK + courseTimetableHistoryVO.getId(), Date.from(LocalDateTime.of(LocalDate.now(), LocalTime.of(23, 59, 59)).atZone(ZoneId.systemDefault()).toInstant()));
                });
                redisTemplate.expireAt(RedisConstants.RANK + courseTimetableHistoryVO.getId(), Date.from(LocalDateTime.of(LocalDate.now(), LocalTime.of(23, 59, 59)).atZone(ZoneId.systemDefault()).toInstant()));
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
                return ClassMemberRankVO.builder()
                        .date(courseTimetableHistoryVO.getWorkBeginTime().format(formatter))
                        .userTop(classStudyStatisticsMapper.userStudyTop(courseId, headerUserInfo.getCurrentCompanyId(), courseTimetableHistoryVO.getWorkBeginTime()))
                        .build();
            } else {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
                return ClassMemberRankVO.builder()
                        .date(courseTimetableHistoryVO.getWorkBeginTime().format(formatter))
                        .userTop(typedTuples.stream().map(tuple -> {
                            UserFlagInfoVO userInfoVO = new UserFlagInfoVO();
                            userInfoVO.setUserFlag(false);
                            if (courseInfo.getWorkModelFlag().equals(WorkModelFlagEnum.DIFF_OLD_NEW.getCode())) {
                                userInfoVO.setUserFlag(true);
                            }
                            return TodayStudyUserTopVO.builder()
                                    .userFlag(userInfoVO.getUserFlag())
                                    .score(tuple.getScore().intValue())
                                    .userId(Integer.valueOf(String.valueOf(tuple.getValue())))
                                    .build();
                        }).collect(Collectors.toList()))
                        .build();
            }
        }).collect(Collectors.toList());
        if (classMemberRankList.isEmpty()) {
            return new ArrayList<>();
        }
        classMemberRankList.forEach(classMemberRankVO -> {
            classMemberRankVO.getUserTop().forEach(userTop -> {
                userIds.add(userTop.getUserId());
            });
        });
        Map<Integer, UserFlagInfoVO> userInfoVOMap = classesComponent.queryByCourseIdUserIdList(courseId, userIds)
                .stream().map(item -> {
                    UserFlagInfoVO vo = new UserFlagInfoVO();
                    vo.setUserId(item.getUserId());
                    vo.setUsername(item.getUsername());
                    vo.setUserRole(item.getUserRole());
                    vo.setClassGroupId(Integer.valueOf(item.getClassesNo()));
                    vo.setClassTeacherName(item.getClassesOwnerName());
                    return vo;
                }).collect(Collectors.toList())
                .stream().collect(Collectors.toMap(UserFlagInfoVO::getUserId, userInfoVO -> userInfoVO));

        classMemberRankList.forEach(classMemberRankVO -> {
            classMemberRankVO.getUserTop().forEach(userTop -> {
                UserFlagInfoVO userFlagInfoVO = userInfoVOMap.get(userTop.getUserId());
                if (Objects.nonNull(userFlagInfoVO)) {
                    userTop.setUserName(userFlagInfoVO.getUsername());
                    userTop.setTeacherName(userFlagInfoVO.getClassTeacherName());
                    userTop.setClassGroupId(userFlagInfoVO.getClassGroupId());

                }
            });
        });
        return classMemberRankList;
    }

    @Override
    public GetRecommendVO getRecommendNum(GetRecommendNumDTO dto) {
        GetRecommendVO getRecommendVO = new GetRecommendVO();
        CourseConfig courseConfig = courseComponent.courseConfigDetail(-1, dto.getCourseId());
        if (Objects.isNull(courseConfig)) {
            getRecommendVO.setRecommendNumber(0);
            getRecommendVO.setRecommendedNumber(0);
            return getRecommendVO;
        }
        if (Objects.isNull(courseConfig.getShowWorkSwitch())) {
            getRecommendVO.setRecommendNumber(0);
            getRecommendVO.setRecommendedNumber(0);
            return getRecommendVO;
        }
        if (courseConfig.getShowWorkSwitch().equals(YesOrNoEnum.NO.getCode())) {
            getRecommendVO.setRecommendNumber(0);
            getRecommendVO.setRecommendedNumber(0);
            return getRecommendVO;
        }
        if (Objects.isNull(courseConfig.getRecommendWorkType())) {
            getRecommendVO.setRecommendNumber(0);
            getRecommendVO.setRecommendedNumber(0);
            return getRecommendVO;
        }
        if (courseConfig.getRecommendWorkType().equals(RecommendWorkTypeEnum.NUMBER.getCode()) && Objects.isNull(courseConfig.getRecommendWorkTotalLimit())) {
            getRecommendVO.setRecommendNumber(0);
            getRecommendVO.setRecommendedNumber(0);
            return getRecommendVO;
        }
        if (courseConfig.getRecommendWorkType().equals(RecommendWorkTypeEnum.PERSON_NUM.getCode()) && Objects.isNull(courseConfig.getRecommendWorkUserLimit())) {
            getRecommendVO.setRecommendNumber(999999);
            getRecommendVO.setRecommendedNumber(0);
            return getRecommendVO;
        }
        CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(dto.getCourseId(), null);
        if (Objects.isNull(courseTimetableUnlockDetailVO)) {
            getRecommendVO.setRecommendNumber(0);
            getRecommendVO.setRecommendedNumber(0);
            return getRecommendVO;
        }
        if (!(LocalDateTime.now().isAfter(courseTimetableUnlockDetailVO.getWorkBeginTime()) && LocalDateTime.now().isBefore(courseTimetableUnlockDetailVO.getWorkEndTime()))) {
            getRecommendVO.setRecommendNumber(0);
            getRecommendVO.setRecommendedNumber(0);
            return getRecommendVO;
        }
        long recommendNum = workInfoPojoMongoService.querySimpleCount(new WorkInfoPojoQueryDTO()
                .setCompanyId(-1).setClassesNo(dto.getClassesNo()).setRecommendStatus(1)
                .setCourseTimetableId(courseTimetableUnlockDetailVO.getId()));
        if (RecommendWorkTypeEnum.NUMBER.getCode().equals(courseConfig.getRecommendWorkType())) {
            if (recommendNum >= courseConfig.getRecommendWorkTotalLimit()) {
                getRecommendVO.setRecommendNumber(0);
                getRecommendVO.setRecommendedNumber(0);
                return getRecommendVO;
            }
            getRecommendVO.setRecommendNumber(courseConfig.getRecommendWorkTotalLimit());
        } else if (RecommendWorkTypeEnum.UNLIMITED.getCode().equals(courseConfig.getRecommendWorkType())) {
            getRecommendVO.setRecommendNumber(999999);
            getRecommendVO.setRecommendedNumber(0);
            return getRecommendVO;
        } else {
            ClassStudyStatistics classStudyStatistics = classStudyStatisticsMapper.selectOne(new QueryWrapper<ClassStudyStatistics>().lambda()
                    .eq(ClassStudyStatistics::getClassesNo, dto.getClassesNo())
                    .eq(ClassStudyStatistics::getCompanyId, -1)
                    .eq(ClassStudyStatistics::getCourseTimetableId, courseTimetableUnlockDetailVO.getId()));
            if (Objects.isNull(classStudyStatistics)) {
                getRecommendVO.setRecommendNumber(0);
                getRecommendVO.setRecommendedNumber(0);
                return getRecommendVO;
            }
            if ((classStudyStatistics.getClassMemberTotal() / courseConfig.getRecommendWorkUserLimit()) - recommendNum < 1) {
                getRecommendVO.setRecommendNumber(0);
                getRecommendVO.setRecommendedNumber(0);
                return getRecommendVO;
            }
            getRecommendVO.setRecommendNumber(classStudyStatistics.getClassMemberTotal() / courseConfig.getRecommendWorkUserLimit());
        }
        return getRecommendVO;
    }

    @Override
    public void oldWorkSync() {
      // 接口没调用，删除了先
    }

    @Override
    public UserStudyVO getUserStudy(UserStudyDTO dto) {
        UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(dto.getCourseId(), dto.getUserId());
        dto.setCompanyId(userRightsCourse.getCompanyId());
        UserStudyInfo userStudyInfo = userStudyInfoMapper.selectOne(new LambdaQueryWrapper<UserStudyInfo>()
                .eq(UserStudyInfo::getUserId, dto.getUserId())
                .eq(UserStudyInfo::getCompanyId, dto.getCompanyId())
                .eq(UserStudyInfo::getCourseId, dto.getCourseId()));
        UserStudyVO vo = workInfoPojoMongoService.getUserStudy(dto);
        vo.setScore(Objects.nonNull(userStudyInfo) ? userStudyInfo.getScore() : 0);
        return vo;
    }

    @Override
    public void repairScore(RepairScoreDTO repairScoreDTO) {
        log.info(JSON.toJSONString(repairScoreDTO));
        if (repairScoreDTO.getModelType() == 1) {
            repairListenScore(repairScoreDTO);
        } else if (repairScoreDTO.getModelType() == 2) {
            CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(repairScoreDTO.getCourseId(), repairScoreDTO.getCourseTimetableId());
            QueryWrapper<UserStudyInfo> userStudyInfoQueryWrapper = new QueryWrapper<>();
            userStudyInfoQueryWrapper
                    .lambda()
                    .eq(UserStudyInfo::getUserId, repairScoreDTO.getUserId())
                    .eq(UserStudyInfo::getCompanyId, repairScoreDTO.getCompanyId())
                    .eq(UserStudyInfo::getCourseId, repairScoreDTO.getCourseId());
            UserStudyInfo userStudyInfo = userStudyInfoMapper.selectOne(userStudyInfoQueryWrapper);
            if (Objects.isNull(userStudyInfo)) {
                userStudyInfo = UserStudyInfo.builder()
                        .courseId(repairScoreDTO.getCourseId())
                        .companyId(repairScoreDTO.getCompanyId())
                        .score(0)
                        .userId(repairScoreDTO.getUserId())
                        .build();
                userStudyInfoMapper.insert(userStudyInfo);
            }
            WorkInfoPojo workInfo = workInfoPojoMongoService.querySimpleOne(new WorkInfoPojoQueryDTO().setUserId(repairScoreDTO.getUserId())
                    .setCourseId(repairScoreDTO.getCourseId()).setCourseTimetableId(repairScoreDTO.getCourseTimetableId()));
            if (Objects.nonNull(workInfo)
                    && ((StringUtils.isNotBlank(workInfo.getAdditional()) && Objects.nonNull(additionalParse(workInfo.getAdditional()))
                    && additionalParse(workInfo.getAdditional()).getStatus().equals(0)) || StringUtils.isBlank(workInfo.getAdditional()))) {
                CourseConfig courseConfig = courseComponent.courseConfigDetail(-1, workInfo.getCourseId());
                if (Objects.nonNull(courseConfig) && YesOrNoEnum.YES.getCode().equals(courseConfig.getExtendStudySwitch())) {
                    AdditionalInfoVO additionalInfoVO = oldServerComponent.additionalInfo(workInfo.getCourseId(), courseTimetableUnlockDetailVO.getWorkBeginTime().toLocalDate());
                    if (Objects.nonNull(additionalInfoVO)) {
                        additionalInfoVO.setAdditionalScore(courseConfig.getExtendStudyScore());
                        additionalInfoVO.setStatus(1);
                        additionalInfoVO.setAdditionalScore(courseConfig.getExtendStudyScore());
                        workInfo.setAdditional(JSON.toJSONString(additionalInfoVO));
                        workInfo.setScore(workInfo.getScore() + courseConfig.getExtendStudyScore());
                        workInfo.setUpdateTime(LocalDateTime.now());
                        WorkInfo mySqlWorkInfo = new WorkInfo();
                        BeanUtil.copyProperties(workInfo, mySqlWorkInfo);
                        mySqlWorkInfo.setId(workInfo.getWorkId());
                        workInfoMapper.updateById(mySqlWorkInfo);
                        workInfoPojoMongoService.saveOrUpdate(workInfo);
                        userStudyInfo.setScore(userStudyInfo.getScore() + courseConfig.getExtendStudyScore());
                        userStudyInfoMapper.updateById(userStudyInfo);
                        generateRank(DoWorkSendKafkaBO.builder()
                                .courseTimeTableId(courseTimetableUnlockDetailVO.getId())
                                .userId(repairScoreDTO.getUserId())
                                .companyId(repairScoreDTO.getCompanyId())
                                .courseId(repairScoreDTO.getCourseId())
                                .build());
                    } else {
                        throw new BusinessException("没有附加作业配置");
                    }
                }
            } else if (Objects.nonNull(workInfo)
                    && StringUtils.isNotBlank(workInfo.getAdditional())
                    && Objects.nonNull(additionalParse(workInfo.getAdditional()))
                    && additionalParse(workInfo.getAdditional()).getStatus().equals(1)) {
                throw new BusinessException("附加作业已完成，无需补分");
            } else {
                new BusinessException("phone请先补听课分");
            }

        }


    }

    @Override
    public Boolean classesMemberDynamic(ClassesMemberDynamicKafkaBO classesMemberDynamicKafkaBO) {
        CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(classesMemberDynamicKafkaBO.getCourseId(), null);
        if (Objects.isNull(courseTimetableUnlockDetailVO)) {
            log.info("课程表信息不存在，不处理");
            return true;
        }
        if (!(LocalDateTime.now().isAfter(courseTimetableUnlockDetailVO.getWorkBeginTime()) && LocalDateTime.now().isBefore(courseTimetableUnlockDetailVO.getWorkEndTime()))) {
            log.info(" 当前时间" + LocalDateTime.now() + "，提交作业开始时间：" + courseTimetableUnlockDetailVO.getWorkBeginTime() + "提交作业结束时间" + " 不在作业提交时间范围内-退出听课加分");
            return true;
        }
        WorkInfoPojo workInfo = workInfoPojoMongoService.querySimpleOne(new WorkInfoPojoQueryDTO().setCompanyId(classesMemberDynamicKafkaBO.getCompanyId())
                .setUserId(classesMemberDynamicKafkaBO.getUserId()).setCourseId(classesMemberDynamicKafkaBO.getCourseId())
                .setCourseTimetableId(courseTimetableUnlockDetailVO.getId()));
        if (Objects.isNull(workInfo)) {
            return true;
        }
        ClassesTeamDetailVO classesTeamInfoByUser = classesComponent.getClassesTeamInfoByUser(classesMemberDynamicKafkaBO.getCompanyId(), classesMemberDynamicKafkaBO.getCourseId(), classesMemberDynamicKafkaBO.getUserId());
        if (Objects.isNull(classesTeamInfoByUser)) {
            classesTeamInfoByUser = ClassesTeamDetailVO.builder()
                    .classesId(0)
                    .classesNo("0")
                    .teamId(0)
                    .build();
        }

        if (Objects.nonNull(classesTeamInfoByUser) && Objects.nonNull(classesTeamInfoByUser.getClassesOwnerId())) {
            UserInfoCompanySearchVO classesTeacherUser = userComponent.userByUserIdCompanyId(classesMemberDynamicKafkaBO.getCompanyId(), classesTeamInfoByUser.getClassesOwnerId());
            if (Objects.nonNull(classesTeacherUser)) {
                workInfo.setClassesTeacherPhone(classesTeacherUser.getPhone());
            } else {
                throw new BusinessException("未查询到数据，可能服务异常不处理");
            }

        } else {
            workInfo.setClassesTeacherPhone("");
        }
        log.info("classesMemberDynamic classesTeamInfoByUser:{}", JSON.toJSONString(classesTeamInfoByUser));
        workInfo.setClassesId(classesTeamInfoByUser.getClassesId());
        String classesNo = classesTeamInfoByUser.getClassesNo();
        workInfo.setClassesNo(Objects.nonNull(classesNo) && Pattern.matches("\\d+", classesNo) ? Integer.parseInt(classesNo) : 0);
        workInfo.setTeamId(classesTeamInfoByUser.getTeamId());
        workInfo.setClassesTeacherName(StringUtils.isBlank(classesTeamInfoByUser.getClassesOwnerName()) ? "" : classesTeamInfoByUser.getClassesOwnerName());
        workInfo.setTeamName(StringUtils.isBlank(classesTeamInfoByUser.getTeamName()) ? "" : classesTeamInfoByUser.getTeamName());
        workInfo.setClassesName(StringUtils.isBlank(classesTeamInfoByUser.getClassesName()) ? "" : classesTeamInfoByUser.getClassesName());
        WorkInfo mySqlWorkInfo = new WorkInfo();
        BeanUtil.copyProperties(workInfo, mySqlWorkInfo);
        mySqlWorkInfo.setId(workInfo.getWorkId());
        workInfoMapper.updateById(mySqlWorkInfo);
        workInfoPojoMongoService.saveOrUpdate(workInfo);
        return true;
    }

    @Override
    public Boolean shareWork(Integer courseId, Integer courseTimeTableId, Integer id, HeaderUserInfo headerUserInfo) {
        DoWorkMsgDTO doWorkMsgDTO = new DoWorkMsgDTO();
        doWorkMsgDTO.setCourseId(courseId);
        doWorkMsgDTO.setCourseTimeTableId(courseTimeTableId);
        doWorkMsgDTO.setUserId(headerUserInfo.getId());
        doWorkMsgDTO.setCompanyId(headerUserInfo.getCurrentCompanyId());
        doWorkMsgDTO.setId(id);
        doWorkMsgDTO.setTag(WorkKafkaMessageEnum.SHAR.getCode());
        this.syncShareWork(doWorkMsgDTO);
        return true;
    }

    @Override
    public void syncShareWork(DoWorkMsgDTO dto) {
        log.info("syncShareWork DoWorkMsgDTO dto:{}", JSON.toJSONString(dto));
        CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(dto.getCourseId(), dto.getCourseTimeTableId(), dto.getUserId());
        log.info("syncShareWork 获取课程表 courseTimetableUnlockDetailVO:{}", courseTimetableUnlockDetailVO);
        CourseConfig courseConfig = courseComponent.courseConfigDetail(dto.getCompanyId(), dto.getCourseId());
        UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(dto.getCourseId(), dto.getUserId());
        dto.setCompanyId(userRightsCourse.getCompanyId());
        LocalDateTime now = LocalDateTime.now();
        //校验写作业时间
        this.checkWorkTime(courseTimetableUnlockDetailVO, courseConfig);
        log.info("syncShareWork 当前时间" + now + "，提交作业开始时间：" + courseTimetableUnlockDetailVO.getWorkBeginTime() + "提交作业结束时间" + "在作业提交时间范围内-进行加分操作");
        Jedis jedis = JedisPoolImpl.getResource();
        JedisDistributedLock lock = new JedisDistributedLock(jedis, WorkKafkaMessageEnum.DO_WORK.getCode() + dto.getCompanyId() + "-" + dto.getCourseTimeTableId() + "-" + dto.getUserId(), 10000, 5000);//acquireTimeoutMillis:超过该时间没有获取到锁，直接返回false; expiryTimeMillis:锁过期时间)
        try {
            if (lock.acquireEp()) {
                UserStudyInfo userStudyInfo = this.getUserStudyInfo(dto.getUserId(), dto.getCourseId(), dto.getCompanyId());
                WorkInfo workInfo = workInfoMapper.queryOneWork(dto.getUserId(), dto.getCourseId(), dto.getCompanyId(), dto.getCourseTimeTableId());
                AtomicReference<Integer> score = new AtomicReference<>(0);
                if (Objects.nonNull(workInfo)) {
                    log.info("syncShareWork 作业不为 null,分享加分开始");
                    if (WorkModelFlagEnum.UNIFY.getCode().equals(workInfo.getWorkModelFlag())) {
                        workInfo.setLevel(courseTimetableUnlockDetailVO.getPlanSchemeOption());
                    }
                    workInfo.setWorkModelFlag(courseTimetableUnlockDetailVO.getWorkModelFlag());
                    boolean isOldFlag = getIsOldFlag(courseTimetableUnlockDetailVO.getWorkModelFlag(), dto.getUserId(), dto.getCourseId());
                    workInfo.setUserFlag(isOldFlag ? 1 : 0);
                    WorkUserModel workUserModel = getWorkUserModel(courseTimetableUnlockDetailVO.getWorkModelFlag(), dto.getUserId(), dto.getCourseId());
                    // 如果已经有分或者已写过作业,就不能修改模板等级了
                    if (WorkModelFlagEnum.LEVEL_WORK_TEMPLATE.getCode().equals(workInfo.getWorkModelFlag())) {
                        workInfo.setLevel((Objects.equals(workInfo.getWorkStatus(), 1) || workInfo.getScore() > 0) && StrUtil.isNotBlank(workInfo.getLevel())
                                ? workInfo.getLevel() : Objects.nonNull(workUserModel) && StringUtils.isNotBlank(workUserModel.getLevel()) ? workUserModel.getLevel() : "");
                    }
                    Integer workModelId = getSimpleWorkModelIdByCourseTimetableUnlockDetailVO(courseTimetableUnlockDetailVO, isOldFlag, workUserModel, workInfo);
                    workInfo.setWorkModelId(workModelId);
                    WorkModelDetail workModelDetail = this.getShareTypeWorkDetailById(dto.getId(), courseTimetableUnlockDetailVO.getId());
                    if (Objects.isNull(workModelDetail)) {
                        log.info("syncShareWork 该作业没有分享加分作业项----退出听课加分");
                        return;
                    }
                    List<WorkModelDetailVO> workModelDetailVOS;
                    if (StringUtils.isNotBlank(workInfo.getContent())) {
                        workModelDetailVOS = workContentParse(workInfo.getContent());
                    } else {
                        workModelDetailVOS = queryWorkModelDetailList(workModelId, dto.getCourseId(), courseTimetableUnlockDetailVO);
                    }
                    if (Objects.equals(workInfo.getShareStatus(), 1)) {
                        log.info("syncShareWork 作业已加过分享分----退出分享加分");
                        return;
                    }
                    for (WorkModelDetailVO workInfoDetail : workModelDetailVOS) {
                        if (WorkDetailTypeEnum.SHARE.getCode().equals(workInfoDetail.getType())) {
                            log.info("syncShareWork 作业不为 null,进行分享加分操作");
                            workInfoDetail.setContent("1");
                            workInfoDetail.setScore(workModelDetail.getScore());
                            score.set(score.get() + workModelDetail.getScore());
                            workInfo.setShareStatus(1);
                            workInfo.setResourceTitle(courseTimetableUnlockDetailVO.getResourceTitle());
                            workInfo.setScore(workInfo.getScore() + score.get());
                            workInfo.setContent(JSONObject.toJSONString(workModelDetailVOS));
                            workInfo.setUpdateTime(now);
                            workInfoMapper.saveOrUpdate(workInfo);
                            workInfoPojoMongoService.saveOrUpdate(workInfo);
                            userStudyInfo.setScore(userStudyInfo.getScore() + score.get());
                            userStudyInfoMapper.updateById(userStudyInfo);
                            log.info("syncShareWork 作业不为 null,分享加分结束");
                            break;
                        }
                    }
                } else {
                    log.info("syncShareWork 作业为 null,分享加分开始");
                    workInfo = this.workInfoBuild(courseTimetableUnlockDetailVO, dto.getUserId(), dto.getCourseId(), dto.getCompanyId());
                    if (WorkModelFlagEnum.UNIFY.getCode().equals(workInfo.getWorkModelFlag())) {
                        workInfo.setLevel(courseTimetableUnlockDetailVO.getPlanSchemeOption());
                    }
                    if (YesOrNoEnum.YES.getCode().equals(courseConfig.getMakeUpWork()) && now.isAfter(courseTimetableUnlockDetailVO.getWorkEndTime())) {
                        workInfo.setClassesNo(0);
                        workInfo.setClassesName("");
                        workInfo.setTeamId(0);
                        workInfo.setTeamName("");
                    }
                    WorkModelDetail workModelDetail = this.getShareTypeWorkDetailById(dto.getId(), courseTimetableUnlockDetailVO.getId());
                    if (Objects.isNull(workModelDetail)) {
                        log.info("syncShareWork 该作业没有分享加分作业项----退出听课加分");
                        return;
                    }
                    boolean isOldFlag = getIsOldFlag(courseTimetableUnlockDetailVO.getWorkModelFlag(), dto.getUserId(), dto.getCourseId());
                    workInfo.setUserFlag(isOldFlag ? 1 : 0);
                    WorkUserModel workUserModel = getWorkUserModel(courseTimetableUnlockDetailVO.getWorkModelFlag(), dto.getUserId(), dto.getCourseId());
                    // 如果已经有分或者已写过作业,就不能修改模板等级了
                    if (WorkModelFlagEnum.LEVEL_WORK_TEMPLATE.getCode().equals(workInfo.getWorkModelFlag())) {
                        workInfo.setLevel((Objects.equals(workInfo.getWorkStatus(), 1) || workInfo.getScore() > 0) && StrUtil.isNotBlank(workInfo.getLevel())
                                ? workInfo.getLevel() : Objects.nonNull(workUserModel) && StringUtils.isNotBlank(workUserModel.getLevel()) ? workUserModel.getLevel() : "");
                    }
                    Integer workModelId = getSimpleWorkModelIdByCourseTimetableUnlockDetailVO(courseTimetableUnlockDetailVO, isOldFlag, workUserModel, workInfo);
                    workInfo.setWorkModelId(workModelId);
                    List<WorkModelDetailVO> detail = queryWorkModelDetailList(workInfo.getWorkModelId(), dto.getCourseId(), courseTimetableUnlockDetailVO);
                    for (WorkModelDetailVO workModelDetailVO : detail) {
                        if (SHARE.getCode().equals(workModelDetailVO.getType()) && workModelDetailVO.getId().equals(dto.getId())) {
                            workModelDetailVO.setContent("1");
                            score.set(score.get() + workModelDetail.getScore());
                            workInfo.setScore(workInfo.getScore() + score.get());
                            userStudyInfo.setScore(userStudyInfo.getScore() + score.get());
                            workInfo.setShareStatus(1);
                        }
                    }
                    workInfo.setContent(JSONObject.toJSONString(detail));
                    workInfo.setUpdateTime(now);
                    workInfoMapper.saveOrUpdate(workInfo);
                    workInfoPojoMongoService.saveOrUpdate(workInfo);
                    userStudyInfoMapper.updateById(userStudyInfo);
                    log.info("syncShareWork 作业为 null,分享加分结束");
                }

                DoWorkSendKafkaBO doWorkSendKafkaBO = this.workSendKafkaBoBuild(
                        dto.getCompanyId(),
                        workInfo.getClassesId(),
                        workInfo.getTeamId(),
                        workInfo.getUserId(),
                        workInfo.getCourseId(),
                        workInfo.getCourseTimetableId(),
                        YesOrNoEnum.YES.getCode().equals(workInfo.getListenStatus()),
                        YesOrNoEnum.YES.getCode().equals(workInfo.getWorkStatus()));
                doWorkSendKafkaBO.setScore(workInfo.getScore());
                doWorkSendKafkaBO.setTag(WorkKafkaMessageEnum.SHAR.getCode());
                kafkaProduction.send(workTopic, String.valueOf(doWorkSendKafkaBO.getUserId()), JSONObject.parseObject(JSONObject.toJSONString(doWorkSendKafkaBO)));
                this.generateRank(DoWorkSendKafkaBO.builder()
                        .courseTimeTableId(dto.getCourseTimeTableId())
                        .userId(dto.getUserId())
                        .companyId(dto.getCompanyId())
                        .courseId(dto.getCourseId())
                        .build());
            } else {
                log.info("syncShareWork 加锁失败 dto:{}", JSON.toJSONString(dto));
                throw new BusinessException("提交作业异常请重新提交");
            }
        } catch (InterruptedException e) {
            log.info("syncShareWork 提交作业异常请重新提交 error:{}", e.getMessage(), e);
            throw new BusinessException("提交作业异常请重新提交");
        } finally {
            lock.release();
        }
    }

    public WorkModelDetail getShareTypeWorkDetailById(Integer workModelDetailId, Integer courseTimetableId) {
        WorkModelDetail workModelDetail = new WorkModelDetail();
        CourseTimetableModelDetail courseTimetableModelDetail = courseTimetableModelDetailMapper.selectOne(new QueryWrapper<CourseTimetableModelDetail>().lambda()
                .eq(CourseTimetableModelDetail::getCourseTimetableId, courseTimetableId)
                .eq(CourseTimetableModelDetail::getType, SHARE.getCode())
                .eq(CourseTimetableModelDetail::getId, workModelDetailId));
        if (Objects.isNull(courseTimetableModelDetail)) {
            QueryWrapper<WorkModelDetail> queryWrapper = new QueryWrapper<>();
            queryWrapper
                    .lambda()
                    .eq(WorkModelDetail::getType, SHARE.getCode())
                    .eq(WorkModelDetail::getId, workModelDetailId);
            workModelDetail = workModelDetailMapper.selectOne(queryWrapper);
            if (Objects.isNull(workModelDetail)) {
                log.info("getShareTypeWorkDetailById 该作业没有听课加分作业项----退出听课加分");
                return null;
            }
        } else {
            BeanUtils.copyProperties(courseTimetableModelDetail, workModelDetail);
        }
        return workModelDetail;
    }

    public WorkInfo workInfoBuild(CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO, Integer userId, Integer courseId, Integer companyId) {
        ClassesTeamDetailVO classesTeamInfoByUser = classesComponent.listenGetClassesTeamInfoByUser(companyId, courseId, userId);
        if (Objects.isNull(classesTeamInfoByUser)) {
            classesTeamInfoByUser = ClassesTeamDetailVO.builder()
                    .classesId(0).classesNo("0").teamId(0).build();
        }
        UserInfoCompanySearchVO userInfoCompanySearchVO = userComponent.listenUserByUserIdCompanyId(companyId, userId);
        if (Objects.isNull(userInfoCompanySearchVO)) {
            log.info("syncDoWork 未查询到数据，可能服务异常不处理,userId:{}, courseId:{}, companyId:{}", userId, courseId, companyId);
            throw new BusinessException("未查询到数据，可能服务异常不处理");
        }
        String teacherPhone = "";
        String teacherName = "";
        if (Objects.nonNull(classesTeamInfoByUser.getClassesOwnerId())) {
            UserInfoCompanySearchVO classesTeacherUser = userComponent.listenUserByUserIdCompanyId(companyId, classesTeamInfoByUser.getClassesOwnerId());
            if (Objects.nonNull(classesTeacherUser)) {
                teacherPhone = classesTeacherUser.getPhone();
                if (companyId.equals(-1)) {
                    teacherName = classesTeacherUser.getUserFullName();
                } else {
                    teacherName = classesTeacherUser.getRealName();
                }
            }
        }
        LocalDateTime now = LocalDateTime.now();
        String classesNo = classesTeamInfoByUser.getClassesNo();
        WorkInfo workInfo = WorkInfo.builder()
                .classesId(classesTeamInfoByUser.getClassesId())
                .classesNo(Objects.nonNull(classesNo) && Pattern.matches("\\d+", classesNo) ? Integer.parseInt(classesNo) : 0)
                .classesName(classesTeamInfoByUser.getClassesName())
                .companyCode(userInfoCompanySearchVO.getCompanyCode())
                .companyId(companyId)
                .companyName(userInfoCompanySearchVO.getCompanyName())
                .courseBeginTime(courseTimetableUnlockDetailVO.getWorkBeginTime())
                .courseId(courseId)
                .courseName(courseTimetableUnlockDetailVO.getCourseTitle())
                .courseTimetableId(courseTimetableUnlockDetailVO.getId())
                .teamId(classesTeamInfoByUser.getTeamId())
                .teamName(classesTeamInfoByUser.getTeamName())
                .classesTeacherName(teacherName)
                .score(0)
                .automatic(0)
                .workModelFlag(courseTimetableUnlockDetailVO.getWorkModelFlag())
                .teamId(classesTeamInfoByUser.getTeamId())
                .teamName(classesTeamInfoByUser.getTeamName())
                .classesTeacherPhone(teacherPhone)
                .userId(userId)
                .showStatus(YesOrNoEnum.YES.getCode())
                .userName(Objects.equals(companyId, -1) ? userInfoCompanySearchVO.getUserFullName() : userInfoCompanySearchVO.getRealName())
                .userPhone(userInfoCompanySearchVO.getPhone())
                .resourceTitle(courseTimetableUnlockDetailVO.getResourceTitle())
                .createTime(now)
                .updateTime(now)
                .build();
        if (StrUtil.isBlank(workInfo.getUserName())) {
            workInfo.setUserName("未知");
        }
        return workInfo;
    }

    public UserStudyInfo getUserStudyInfo(Integer userId, Integer courseId, Integer companyId) {
        QueryWrapper<UserStudyInfo> userStudyInfoQueryWrapper = new QueryWrapper<>();
        userStudyInfoQueryWrapper
                .lambda()
                .eq(UserStudyInfo::getUserId, userId)
                .eq(UserStudyInfo::getCompanyId, companyId)
                .eq(UserStudyInfo::getCourseId, courseId);
        UserStudyInfo userStudyInfo = userStudyInfoMapper.selectOne(userStudyInfoQueryWrapper);
        if (Objects.isNull(userStudyInfo)) {
            userStudyInfo = UserStudyInfo.builder()
                    .courseId(courseId)
                    .companyId(companyId)
                    .score(0)
                    .userId(userId)
                    .build();
            userStudyInfoMapper.insert(userStudyInfo);
        }
        return userStudyInfo;
    }

    @Override
    @Async("rankExecutor")
    public Boolean generateRank(DoWorkSendKafkaBO doWorkSendKafkaBo) {
        // 企业+个人版 课程表
        CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(doWorkSendKafkaBo.getCourseId(), doWorkSendKafkaBo.getCourseTimeTableId());
        if (Objects.isNull(courseTimetableUnlockDetailVO)) {
            return true;
        }

        UserStudyInfo userStudyInfo = userStudyInfoMapper.selectOne(new QueryWrapper<UserStudyInfo>()
                .lambda()
                .eq(UserStudyInfo::getUserId, doWorkSendKafkaBo.getUserId())
                .eq(UserStudyInfo::getCourseId, doWorkSendKafkaBo.getCourseId())
                .eq(UserStudyInfo::getCompanyId, doWorkSendKafkaBo.getCompanyId()));
        redisTemplate.opsForZSet().add(RedisConstants.RANK + courseTimetableUnlockDetailVO.getId(), doWorkSendKafkaBo.getUserId(), Objects.nonNull(userStudyInfo) ? userStudyInfo.getScore() : 0);
        redisTemplate.expireAt(RedisConstants.RANK + courseTimetableUnlockDetailVO.getId(), Date.from(LocalDateTime.of(LocalDate.now(), LocalTime.of(23, 59, 59)).atZone(ZoneId.systemDefault()).toInstant()));
        return true;
    }

    @Override
    public Map<Integer, WorkStatusInfoVO> batchGetWorkStatusInfo(WorkStatusInfoBatchDTO batchDTO) {
        if (!(Objects.nonNull(batchDTO.getCourseTimeTableIds()) && batchDTO.getCourseTimeTableIds().size() > 0)) {
            return new HashMap<>();
        }
        Map<Integer, WorkStatusInfoVO> result = new HashMap<>();
        List<WorkInfoPojo> workInfoList = workInfoPojoMongoService.querySimpleList(new WorkInfoPojoQueryDTO().setUserId(batchDTO.getUserId())
                .setCourseTimetableIdList(batchDTO.getCourseTimeTableIds()));
        if (CollectionUtil.isNotEmpty(workInfoList)) {
            workInfoList.forEach(workInfo -> {
                WorkStatusInfoVO workStatusInfoVO = WorkStatusInfoVO.builder()
                        .workStatus(WorkStatusEnum.OK.getCode().equals(workInfo.getWorkStatus()))
                        .score(workInfo.getScore())
                        .build();
                result.put(workInfo.getCourseTimetableId(), workStatusInfoVO);
            });
        }
        return result;
    }

    @Override
    public void workHide(Integer workId) {
        WorkInfoPojo workInfo = workInfoPojoMongoService.getById(workId);
        if (Objects.isNull(workInfo)) {
            throw new BusinessException("作业信息不存在");
        }
        if (workInfo.getShowStatus().equals(WorkShowStatusEnum.SHOW.getCode())) {
            workInfo.setShowStatus(WorkShowStatusEnum.HIDE.getCode());
        } else {
            workInfo.setShowStatus(WorkShowStatusEnum.SHOW.getCode());
        }
        WorkInfo mySqlWorkInfo = new WorkInfo();
        BeanUtil.copyProperties(workInfo,mySqlWorkInfo);
        mySqlWorkInfo.setId(workInfo.getWorkId());
        workInfoMapper.updateById(mySqlWorkInfo);
        workInfoPojoMongoService.saveOrUpdate(workInfo);
    }

    @Override
    public List<MakeUpWorkListVO> makeUpWorkList(Integer courseId, HeaderUserInfo headerUserInfo, HttpServletRequest request) {
        String appOs = request.getHeader("appOs");
        String appVersion = request.getHeader("appVersionCode");
        if ("ios".equals(appOs)){
            if (Integer.valueOf(appVersion) > 248){
                //新版
                return makeUpWorkListNewV(courseId, headerUserInfo);
            }else {
                return makeUpWorkListOldV(courseId, headerUserInfo);
            }
        }else {
            if (Integer.valueOf(appVersion) > 2920){
                //新版
                return makeUpWorkListNewV(courseId, headerUserInfo);
            }else {
                return makeUpWorkListOldV(courseId, headerUserInfo);
            }
        }

    }

    @Override
    public PageInfoBT<AppCompanyWorkInfoListVO> companyWorkList(AppWorkHeartPerceptionDTO dto, HeaderUserInfo headerUserInfo) {
        UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(dto.getCourseId(), headerUserInfo.getId());
        headerUserInfo.setCurrentCompanyId(userRightsCourse.getCompanyId());
        long l1 = System.currentTimeMillis();
        IPage<WorkInfoPojo> workInfoPage = workInfoPojoMongoService.companyWorkList(dto, headerUserInfo);
        if (CollUtil.isEmpty(workInfoPage.getRecords())) {
            return PageInfoBT.noData();
        }
        long l2 = System.currentTimeMillis();
        List<AppWorkInfoListVO> voList = workInfoPage.getRecords().stream().map(workInfo -> {
            AppWorkInfoListVO appWorkInfoListVO = convertAppWorkInfoListVO(workInfo, null, true, headerUserInfo);
            appWorkInfoListVO.setBelongDate(workInfo.getCourseBeginTime().toLocalDate());
            return appWorkInfoListVO;
        }).collect(Collectors.toList());
        long l3 = System.currentTimeMillis();
        Map<LocalDate, List<AppWorkInfoListVO>> appWorkInfoListVOMap = voList.stream().collect(Collectors.groupingBy(AppWorkInfoListVO::getBelongDate));
        List<AppCompanyWorkInfoListVO> appCompanyWorkInfoListVOS = new ArrayList<>();
        appWorkInfoListVOMap.keySet().forEach(key -> {
            appCompanyWorkInfoListVOS.add(AppCompanyWorkInfoListVO.builder()
                    .beginDate(key)
                    .workList(appWorkInfoListVOMap.get(key))
                    .build());
        });
        IPage<AppCompanyWorkInfoListVO> appCompanyWorkInfoPage = new Page<>();
        appCompanyWorkInfoPage.setRecords(appCompanyWorkInfoListVOS.stream().sorted(Comparator.comparing(AppCompanyWorkInfoListVO::getBeginDate).reversed()).collect(Collectors.toList()));
        appCompanyWorkInfoPage.setSize(workInfoPage.getSize());
        appCompanyWorkInfoPage.setCurrent(workInfoPage.getCurrent());
        appCompanyWorkInfoPage.setTotal(workInfoPage.getTotal());
        long l4 = System.currentTimeMillis();
        log.info("企业作业列表接口耗时统计： 【l2-l1】:" + (l2 - l1) + " 【l3-l2】:" + (l3 - l2) + "【l4-l3】:" + (l4 - l3));
        return PageInfoBT.fromPage(appCompanyWorkInfoPage);
    }

    @Override
    public PageInfoBT<WorkingByDateVO> classWorkingByDatePage(WorkingByDateDTO dto, HeaderUserInfo headerUserInfo) {
        log.info("classWorkingByDate classWorkingByDatePage dto:{}", JSON.toJSONString(dto));
        Integer courseTimeTableId = dto.getCourseTimeTableId();
        UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(dto.getCourseId(), headerUserInfo.getId());
        headerUserInfo.setCurrentCompanyId(userRightsCourse.getCompanyId());
        // 课件查询
        if (Objects.isNull(dto.getBelongDate())) {
            CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(dto.getCourseId(), null);
            if (Objects.nonNull(courseTimetableUnlockDetailVO)) {
                dto.setCourseTimeTableId(courseTimetableUnlockDetailVO.getId());
                if (Objects.nonNull(courseTimetableUnlockDetailVO.getWorkBeginTime())) {
                    dto.setBelongDate(courseTimetableUnlockDetailVO.getWorkBeginTime().toLocalDate());
                }
            }
        } else {
            QueryByCourseIdAndDateDTO queryByCourseIdAndDateDTO = new QueryByCourseIdAndDateDTO();
            queryByCourseIdAndDateDTO.setCourseId(dto.getCourseId());
            queryByCourseIdAndDateDTO.setBelongDate(dto.getBelongDate());
            CourseTimetable courseTimetable = courseComponent.queryByCourseIdAndDate(queryByCourseIdAndDateDTO);
            dto.setCourseTimeTableId(courseTimetable.getId());
        }
        CourseInfo courseInfo = courseComponent.courseInfoById(dto.getCourseId());
        WorkInfoPojoQueryDTO query = new WorkInfoPojoQueryDTO()
                .setPageNum(dto.getPageNum()).setPageSize(dto.getPageSize())
                .setCourseId(dto.getCourseId()).setClassesId(dto.getClassesId()).setTeamId(dto.getTeamId())
                .setCourseTimetableId(dto.getCourseTimeTableId()).setTeamId(dto.getTeamId())
                .setCompanyId(headerUserInfo.getCurrentCompanyId());
        long count = workInfoPojoMongoService.querySimpleCount(query);
        IPage<WorkInfoPojo> page = workInfoPojoMongoService.querySimplePage(query);
        if (CollUtil.isEmpty(page.getRecords())) {
            return PageInfoBT.noData();
        }
        List<Integer> userIdList = page.getRecords().stream().map(WorkInfoPojo::getUserId).collect(Collectors.toList());
        Map<Integer, UserStudyInfo> userStudyMap = userStudyInfoMapper.selectList(new LambdaQueryWrapper<UserStudyInfo>()
                        .eq(UserStudyInfo::getCourseId, dto.getCourseId())
                        .eq(UserStudyInfo::getCompanyId, headerUserInfo.getCurrentCompanyId())
                        .in(UserStudyInfo::getUserId, userIdList))
                .stream().collect(Collectors.toMap(UserStudyInfo::getUserId, Function.identity(), (v1, v2) -> v1));

        Map<Integer, Integer> oldUserMap = new HashMap<>();
        if (courseInfo.getWorkModelFlag().equals(WorkModelFlagEnum.DIFF_OLD_NEW.getCode())) {
            oldUserMap = workInfoMapper.checkOldUserByUserIds(dto.getCourseId(), userIdList).stream().collect(Collectors.toMap(Integer::intValue, userId -> userId));
        }
        Map<Integer, Integer> finalOldUserMap = oldUserMap;

        List<WorkingByDateVO> voList = page.getRecords().stream().map(w -> {
            WorkingByDateVO vo = new WorkingByDateVO();
            vo.setUserId(w.getUserId());
            vo.setBelongDate(w.getCourseBeginTime().toLocalDate());
            vo.setUsername(w.getUserName());
            vo.setListenStatus(w.getListenStatus() == 1);
            vo.setWorkStatus(w.getWorkStatus() == 1);
            vo.setWorkScore(w.getScore());
            vo.setWorkModelFlag(w.getWorkModelFlag());
            vo.setLevel(w.getLevel());
            UserStudyInfo userStudyInfo = userStudyMap.get(w.getUserId());
            if (Objects.nonNull(userStudyInfo)) {
                vo.setScore(userStudyInfo.getScore());
            } else {
                vo.setScore(0);
            }
            if (courseInfo.getWorkModelFlag().equals(WorkModelFlagEnum.DIFF_OLD_NEW.getCode())) {
                if (!Objects.nonNull(finalOldUserMap.get(w.getUserId()))) {
                    vo.setUserFlag(true);
                }
            }
            return vo;

        }).collect(Collectors.toList());
        if (Objects.isNull(courseTimeTableId)) {
            Map<Integer, String> userPlanMap = courseComponent.batchGetUserPlanSchemeOption(dto.getCourseTimeTableId(), userIdList);
            voList.forEach(i -> i.setLevel(userPlanMap.get(i.getUserId())));
        }
        PageInfoBT<WorkingByDateVO> workingByDateVOIPage = new PageInfoBT<>();
        workingByDateVOIPage.setCurrent(page.getCurrent());
        workingByDateVOIPage.setTotal(count);
        workingByDateVOIPage.setSize(page.getSize());
        workingByDateVOIPage.setRecords(voList);
        return workingByDateVOIPage;
    }

    @Override
    public PageInfoBT<AppWorkInfoListVO> companyPrefecture(CompanyPrefectureDTO companyPrefectureDTO, HeaderUserInfo headerUserInfo) {
        List<Integer> allTogetherCourseIds = courseComponent.getAllTogetherCourseIds();
        if (allTogetherCourseIds.size() < 1) {
            return PageInfoBT.from(0, new ArrayList<>());
        }
        long l1 = System.currentTimeMillis();
        Query query = new Query();
        Criteria criteria = new Criteria();
        Integer personalCompanyId = -1;
        if (companyPrefectureDTO.getPrefectureType().equals(PrefectureTypeEnums.REPLY_WORK.getCode())) {
            if (Objects.isNull(companyPrefectureDTO.getScreeningType())) {
                return PageInfoBT.from(0, new ArrayList<>());
            } else if (companyPrefectureDTO.getScreeningType().equals(ScreeningTypeEnums.TEACHER_REPLY.getCode())) {
                criteria.andOperator(Criteria.where("teacherReplyStatus").is(YesOrNoEnum.YES.getCode()), Criteria.where("courseId").in(allTogetherCourseIds), Criteria.where("companyId").ne(personalCompanyId), Criteria.where("showStatus").is(YesOrNoEnum.YES.getCode()));
            } else if (companyPrefectureDTO.getScreeningType().equals(ScreeningTypeEnums.COMPANY_REPLY.getCode())) {
                criteria.andOperator(Criteria.where("companyReplyStatus").is(YesOrNoEnum.YES.getCode()), Criteria.where("courseId").in(allTogetherCourseIds), Criteria.where("companyId").is(headerUserInfo.getCurrentCompanyId()), Criteria.where("companyId").ne(personalCompanyId), Criteria.where("showStatus").is(YesOrNoEnum.YES.getCode()));
            } else {
                return PageInfoBT.from(0, new ArrayList<>());
            }
        } else if (companyPrefectureDTO.getPrefectureType().equals(PrefectureTypeEnums.RECOMMEND_WORK.getCode())) {
            if (Objects.nonNull(companyPrefectureDTO.getScreeningType())) {
                if (companyPrefectureDTO.getScreeningType().equals(ScreeningTypeEnums.MY_COMPANY_RECOMMENDED.getCode())) {
                    criteria.andOperator(Criteria.where("companyId").is(headerUserInfo.getCurrentCompanyId()), Criteria.where("courseId").in(allTogetherCourseIds), Criteria.where("recommendStatus").is(YesOrNoEnum.YES.getCode()), Criteria.where("showStatus").is(YesOrNoEnum.YES.getCode()));
                } else if (companyPrefectureDTO.getScreeningType().equals(ScreeningTypeEnums.OTHER_COMPANY_RECOMMENDED.getCode())) {
                    criteria.andOperator(Criteria.where("recommendStatus").is(YesOrNoEnum.YES.getCode()), Criteria.where("courseId").in(allTogetherCourseIds), Criteria.where("companyId").ne(headerUserInfo.getCurrentCompanyId()), Criteria.where("companyId").ne(personalCompanyId), Criteria.where("showStatus").is(YesOrNoEnum.YES.getCode()));
                } else {
                    return PageInfoBT.from(0, new ArrayList<>());
                }
            } else {
                return PageInfoBT.from(0, new ArrayList<>());
            }
        }
        query.addCriteria(criteria);
        query.with(Sort.by(Sort.Order.desc("recommendTime")));

        IPage<WorkInfoPojo> iPage = mongoBase.pagination(WorkInfoPojo.class, companyPrefectureDTO.getPageSize(), companyPrefectureDTO.getPageNum(), query);
        long l2 = System.currentTimeMillis();
        List<Integer> workIds = new ArrayList<>();
        iPage.getRecords().forEach(workInfoPojo -> {
            workIds.add(workInfoPojo.getWorkId());
        });
        Map<Integer, List<WorkTeacherReply>> workTeacherReplyMap = new HashMap<>();
        if (workIds.size() > 0) {
            if (companyPrefectureDTO.getPrefectureType().equals(PrefectureTypeEnums.REPLY_WORK.getCode())) {
                if (companyPrefectureDTO.getScreeningType().equals(ScreeningTypeEnums.TEACHER_REPLY.getCode())) {
                    workTeacherReplyMap = workTeacherReplyMapper.selectList(new QueryWrapper<WorkTeacherReply>()
                            .lambda()
                            .in(WorkTeacherReply::getWorkId, workIds)
                            .eq(WorkTeacherReply::getReplyUserType, ScreeningTypeEnums.TEACHER_REPLY.getCode())
                            .eq(WorkTeacherReply::getDeleteFlag, IsDeleteEnum.NO.getCode())
                            .orderByAsc(WorkTeacherReply::getTeacherReplyTime))
                            .stream().collect(Collectors.groupingBy(WorkTeacherReply::getWorkId));
                } else if (companyPrefectureDTO.getScreeningType().equals(ScreeningTypeEnums.COMPANY_REPLY.getCode())) {
                    workTeacherReplyMap = workTeacherReplyMapper.selectList(new QueryWrapper<WorkTeacherReply>()
                            .lambda()
                            .in(WorkTeacherReply::getWorkId, workIds)
                            .eq(WorkTeacherReply::getReplyUserType, ScreeningTypeEnums.COMPANY_REPLY.getCode())
                            .eq(WorkTeacherReply::getDeleteFlag, IsDeleteEnum.NO.getCode())
                            .orderByAsc(WorkTeacherReply::getTeacherReplyTime))
                            .stream().collect(Collectors.groupingBy(WorkTeacherReply::getWorkId));
                }
            }

        }
        long l3 = System.currentTimeMillis();
        Map<Integer, List<WorkTeacherReply>> finalWorkTeacherReplyMap = workTeacherReplyMap;
        IPage<AppWorkInfoListVO> workInfoIPage = iPage.convert(workInfoPojo -> {
            AppWorkInfoListVO appWorkInfoListVO = convertAppWorkInfoListVO(workInfoPojo, null, false, headerUserInfo);
            appWorkInfoListVO.setIsMyCompany(headerUserInfo.getCurrentCompanyId().equals(workInfoPojo.getCompanyId()));
            if (companyPrefectureDTO.getPrefectureType().equals(PrefectureTypeEnums.REPLY_WORK.getCode())) {
                if (Objects.nonNull(finalWorkTeacherReplyMap) && Objects.nonNull(finalWorkTeacherReplyMap.get(workInfoPojo.getWorkId()))) {
                    List<WorkTeacherReply> workTeacherReplies = finalWorkTeacherReplyMap.get(workInfoPojo.getWorkId());
                    appWorkInfoListVO.setTeacherReplyList(workTeacherReplies.stream().map(workTeacherReply -> {
                                AppWorkTeacherReplyVO replyVO = new AppWorkTeacherReplyVO();
                                BeanUtils.copyProperties(workTeacherReply, replyVO);
                                replyVO.setTeacherReply(workTeacherReply.getTeacherContent());
                                replyVO.setTeacherName(workTeacherReply.getUserName());
                                replyVO.setPostId(workTeacherReply.getWorkId());
                                return replyVO;
                            }
                    ).collect(Collectors.toList()));
                }
            }
            if (Objects.nonNull(companyPrefectureDTO.getScreeningType()) && (companyPrefectureDTO.getScreeningType().equals(ScreeningTypeEnums.COMPANY_REPLY.getCode()) || companyPrefectureDTO.getScreeningType().equals(ScreeningTypeEnums.MY_COMPANY_RECOMMENDED.getCode()))) {
                appWorkInfoListVO.setCompanyName("");
            }
            return appWorkInfoListVO;
        });
        long l4 = System.currentTimeMillis();

        long l7 = System.currentTimeMillis();
        log.info("企业专区接口耗时统计： 【l2-l1】:" + (l2 - l1) + " 【l3-l2】:" + (l3 - l2) + "【l4-l3】:" + (l4 - l3));
        return PageInfoBT.fromPage(workInfoIPage);
    }

    @Override
    public PageInfoBT<AppWorkInfoListVO> companyRecommendWorkList(AppWorkRecommendSearchDTO recommendSearchDTO, HeaderUserInfo headerUserInfo) {
        long l1 = System.currentTimeMillis();
        Query query = new Query();
        Criteria criteria = new Criteria();
        if (Objects.nonNull(recommendSearchDTO.getCourseId())) {
            UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(recommendSearchDTO.getCourseId(), headerUserInfo.getId());
            headerUserInfo.setCurrentCompanyId(userRightsCourse.getCompanyId());
        }
        List<WorkTeacherInfo> workTeacherInfos = workTeacherInfoMapper.selectList(new QueryWrapper<WorkTeacherInfo>().lambda()
                .eq(Objects.nonNull(recommendSearchDTO.getCourseId()) && recommendSearchDTO.getCourseId() != 0, WorkTeacherInfo::getCourseId, recommendSearchDTO.getCourseId())
                .eq(WorkTeacherInfo::getUserId, headerUserInfo.getId()));
        UserInfoCompanySearchVO userInfoCompanySearchVO = userComponent.userByUserIdCompanyId(headerUserInfo.getCurrentCompanyId(), headerUserInfo.getId());
        long l2 = System.currentTimeMillis();
        if (CollUtil.isEmpty(workTeacherInfos) && userInfoCompanySearchVO.getSpecialUserFlag().equals(YesOrNoEnum.NO.getCode())) {
            throw new BusinessException("仅限特定人员可见");
        }
        if (Objects.nonNull(workTeacherInfos) && workTeacherInfos.size() > 0) {
            criteria.and("courseId").in(workTeacherInfos.stream().map(workTeacherInfo -> workTeacherInfo.getCourseId()).collect(Collectors.toList())).and("recommendTeacherStatus").is(YesOrNoEnum.YES.getCode());
            if (recommendSearchDTO.getReplyStatus().equals(YesOrNoEnum.NO.getCode()) || recommendSearchDTO.getReplyStatus().equals(YesOrNoEnum.YES.getCode())) {
                criteria.and("teacherReplyStatus").is(recommendSearchDTO.getReplyStatus()).and("showStatus").is(YesOrNoEnum.YES.getCode());
            } else {
                criteria.and("showStatus").is(YesOrNoEnum.YES.getCode());
            }
            if (recommendSearchDTO.getReplyStatus().equals(YesOrNoEnum.YES.getCode())) {
                query.addCriteria(criteria);
                query.with(Sort.by(Sort.Order.desc("teacherReplyTime")));
            } else {
                query.addCriteria(criteria);
                query.with(Sort.by(Sort.Order.desc("recommendTeacherTime")));
            }
        } else if (userInfoCompanySearchVO.getSpecialUserFlag().equals(YesOrNoEnum.YES.getCode())) {
            criteria.andOperator(Criteria.where("companyId").is(headerUserInfo.getCurrentCompanyId())).and("recommendStatus").is(YesOrNoEnum.YES.getCode());
            if (recommendSearchDTO.getReplyStatus().equals(YesOrNoEnum.NO.getCode()) || recommendSearchDTO.getReplyStatus().equals(YesOrNoEnum.YES.getCode())) {
                criteria.and("companyReplyStatus").is(recommendSearchDTO.getReplyStatus()).and("showStatus").is(YesOrNoEnum.YES.getCode());
            } else {
                criteria.and("showStatus").is(YesOrNoEnum.YES.getCode());
            }
            if (Objects.nonNull(recommendSearchDTO.getCourseId()) && recommendSearchDTO.getCourseId() != 0) {
                criteria.and("courseId").is(recommendSearchDTO.getCourseId());
            }
            if (recommendSearchDTO.getReplyStatus().equals(YesOrNoEnum.YES.getCode())) {
                query.addCriteria(criteria);
                query.with(Sort.by(Sort.Order.desc("companyReplyTime")));
            } else {
                query.addCriteria(criteria);
                query.with(Sort.by(Sort.Order.desc("recommendTime")));
            }
        }
        IPage<WorkInfoPojo> workInfoIPage = mongoBase.pagination(WorkInfoPojo.class, recommendSearchDTO.getPageSize(), recommendSearchDTO.getPageNum(), query);
        if (!(Objects.nonNull(workInfoIPage) && workInfoIPage.getRecords().size() > 0)) {
            return PageInfoBT.from(0, new ArrayList<>());
        }
        long l3 = System.currentTimeMillis();
        List<Integer> workIds = new ArrayList<>();
        List<Integer> userIds = new ArrayList<>();
        List<Integer> courseIds = new ArrayList<>();
        workInfoIPage.getRecords().forEach(workInfo -> {
            workIds.add(workInfo.getWorkId());
            userIds.add(workInfo.getUserId());
            courseIds.add(workInfo.getCourseId());
        });
        // 批量查询学员学分
        List<UserStudyInfo> userStudyInfoList = userStudyInfoMapper.selectList(new LambdaQueryWrapper<UserStudyInfo>()
                .eq(UserStudyInfo::getCompanyId, headerUserInfo.getCurrentCompanyId())
                .in(UserStudyInfo::getCourseId, courseIds)
                .in(UserStudyInfo::getUserId, userIds));
        long l4 = System.currentTimeMillis();
        Map<String, Integer> userStudyMap = userStudyInfoList.stream().collect(Collectors.toMap(userStudyInfo -> userStudyInfo.getUserId() + "-" + userStudyInfo.getCourseId(), userStudyInfo -> userStudyInfo.getScore()));
        // 构建心得关联老师回复数据
        Map<Integer, List<AppWorkTeacherReplyVO>> replyMap = findTeacherReplyListByWorkIds(workIds, headerUserInfo.getId(), workTeacherInfos.size() > 0 ? ScreeningTypeEnums.TEACHER_REPLY.getCode() : ScreeningTypeEnums.COMPANY_REPLY.getCode());
        IPage<AppWorkInfoListVO> appWorkInfoListVOIPage = workInfoIPage.convert(workInfo -> {
            AppWorkInfoListVO appWorkInfoListVO = convertAppWorkInfoListVO(workInfo, null, false, headerUserInfo);
            appWorkInfoListVO.setBelongDate(workInfo.getCourseBeginTime().toLocalDate());
            appWorkInfoListVO.setScoreTotal(Objects.nonNull(userStudyMap.get(workInfo.getUserId() + "-" + workInfo.getCourseId())) ? userStudyMap.get(workInfo.getUserId() + "-" + workInfo.getCourseId()) : 0);
            // 从Map获取当前心得的老师回复数据
            if (ObjectUtil.isNotNull(replyMap)) {
                appWorkInfoListVO.setTeacherReplyList(replyMap.get(workInfo.getWorkId()));
            }
            if (userInfoCompanySearchVO.getSpecialUserFlag().equals(YesOrNoEnum.YES.getCode())) {
                appWorkInfoListVO.setCompanyName("");
            }
            return appWorkInfoListVO;
        });
        log.info("企业作业列表接口耗时统计： 【l2-l1】:" + (l2 - l1) + " 【l3-l2】:" + (l3 - l2) + "【l4-l3】:" + (l4 - l3));
        return PageInfoBT.fromPage(appWorkInfoListVOIPage);
    }

    @Override
    public PageInfoBT<AppWorkInfoListVO> appCompanyWorkList(AppWorkHeartPerceptionDTO heartPerceptionDTO, HeaderUserInfo headerUserInfo) {
        UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(heartPerceptionDTO.getCourseId(), headerUserInfo.getId());
        headerUserInfo.setCurrentCompanyId(userRightsCourse.getCompanyId());
        long l1 = System.currentTimeMillis();
        Query query = new Query();
        Criteria criteria = new Criteria();
        criteria.andOperator(Criteria.where("companyId").is(headerUserInfo.getCurrentCompanyId()).and("contentShowStatus").is(1));
        if (Objects.nonNull(heartPerceptionDTO.getCourseId()) && !heartPerceptionDTO.getCourseId().equals(0)) {
            criteria.and("courseId").is(heartPerceptionDTO.getCourseId());
        }
        if (Objects.nonNull(heartPerceptionDTO.getCourseTimetableId()) && !heartPerceptionDTO.getCourseTimetableId().equals(0)) {
            criteria.and("courseTimetableId").is(heartPerceptionDTO.getCourseTimetableId());
        }
        criteria.and("showStatus").is(YesOrNoEnum.YES.getCode());
        query.addCriteria(criteria);
        query.with(Sort.by(Sort.Order.desc("courseBeginTime")));
        query.with(Sort.by(Sort.Order.desc("workTime")));
        IPage<WorkInfoPojo> workInfoIPage = mongoBase.pagination(WorkInfoPojo.class, heartPerceptionDTO.getPageSize(), heartPerceptionDTO.getPageNum(), query);
        if (!(Objects.nonNull(workInfoIPage) && workInfoIPage.getRecords().size() > 0)) {
            return PageInfoBT.from(0, new ArrayList<>());
        }
        long l2 = System.currentTimeMillis();
        List<Integer> workIds = new ArrayList<>();
        workInfoIPage.getRecords().forEach(workInfo -> {
            workIds.add(workInfo.getWorkId());
        });
        long l3 = System.currentTimeMillis();
        // 调用公共服务-批量获取心得评论数据
        Map<Integer, List<AppWorkCommentListVO>> commentMap = commentComponent.batchSearchCommentList(workIds, headerUserInfo.getId());
        // 调用公共服务-批量获取心得点赞数据
        long l4 = System.currentTimeMillis();
        Map<Integer, List<AppWorkCommentLikeListVO>> likeMap = commentComponent.batchSearchLikeList(workIds, headerUserInfo.getId());
        // 构建心得关联老师回复数据
        long l5 = System.currentTimeMillis();
        IPage<AppWorkInfoListVO> appWorkInfoListVOIPage = workInfoIPage.convert(workInfo -> {
            AppWorkInfoListVO appWorkInfoListVO = convertAppWorkInfoListVO(workInfo, null, true, headerUserInfo);
            appWorkInfoListVO.setBelongDate(workInfo.getCourseBeginTime().toLocalDate());
            // 从Map获取当前心得的评论数据
            if (ObjectUtil.isNotNull(commentMap)) {
                appWorkInfoListVO.setCommentList(commentMap.get(workInfo.getWorkId()));
            }
            // 从Map获取当前心得的点赞数据
            if (ObjectUtil.isNotNull(likeMap)) {
                appWorkInfoListVO.setLikeList(likeMap.get(workInfo.getWorkId()));
            }
            // 判断我的点赞状态
            if (CollectionUtil.isNotEmpty(appWorkInfoListVO.getLikeList())) {
                boolean myLikeFlag = appWorkInfoListVO.getLikeList().stream().anyMatch(like -> like.getPostUserId().equals(headerUserInfo.getId()));
                appWorkInfoListVO.setPostState(myLikeFlag ? 1 : 0);
            } else {
                appWorkInfoListVO.setPostState(0);
            }
            appWorkInfoListVO.setCompanyName("");
            return appWorkInfoListVO;
        });
        long l6 = System.currentTimeMillis();

        log.info("appCompanyWorkList 耗时统计:l2 - l1:{}, l3 - l2:{}, l4 - l3:{}, l5 - l4:{}, l6 - l5:{}", l2 - l1, l3 - l2, l4 - l3, l5 - l4, l6 - l5);

        return PageInfoBT.fromPage(appWorkInfoListVOIPage);
    }

    @Override
    public ClassStatisticsVO classStatisticsByTime(ClassStatisticsDTO classStatisticsDTO) {
        ClassStatisticsVO classStatisticsVO = new ClassStatisticsVO();
        // 企业版+个人版课程表
        CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(classStatisticsDTO.getCourseId(), null);
        if (Objects.isNull(courseTimetableUnlockDetailVO)) {
            return this.companyClassStatisticsBuild(courseTimetableUnlockDetailVO, classStatisticsVO);
        }

        ClassStatisticsVO historyClassStatistics = classStudyStatisticsMapper.classStatisticsHistory(classStatisticsDTO.getClassesId(), classStatisticsDTO.getClassesNo(), classStatisticsDTO.getCourseId(), null, Objects.nonNull(courseTimetableUnlockDetailVO) && Objects.nonNull(courseTimetableUnlockDetailVO.getId()) ? courseTimetableUnlockDetailVO.getId() : null, classStatisticsDTO.getCompanyId());
        ClassStatisticsVO classStatisticsCurrent = classStudyStatisticsMapper.classStatisticsByTime(classStatisticsDTO.getCourseId(), classStatisticsDTO.getCompanyId(), classStatisticsDTO.getStartTime(), classStatisticsDTO.getEndTime());
        if (Objects.nonNull(historyClassStatistics)) {
            classStatisticsVO.setHistoryWorkRate(historyClassStatistics.getHistoryWorkRate());
            classStatisticsVO.setHistoryListenRate(historyClassStatistics.getHistoryListenRate());
        }
        if (Objects.isNull(classStatisticsCurrent)) {
            return this.companyClassStatisticsBuild(courseTimetableUnlockDetailVO, classStatisticsVO);
        } else {
            BeanUtils.copyProperties(classStatisticsCurrent, classStatisticsVO);
            classStatisticsVO.setHistoryWorkRate(historyClassStatistics.getHistoryWorkRate());
            classStatisticsVO.setHistoryListenRate(historyClassStatistics.getHistoryListenRate());
            return this.companyClassStatisticsBuild(courseTimetableUnlockDetailVO, classStatisticsVO);

        }
    }

    @Override
    public AppWorkConfigVO appWorkConfig(AppWorkConfigDTO dto) {
        AppWorkConfigVO vo = new AppWorkConfigVO();
        workLevelShowFlag(dto, vo);
        return vo;
    }

    @Override
    public PageInfoBT<LazyAppWorkInfoVO> companyWorkPage(CompanyWorkPageDTO dto) {
        WorkInfoPojoQueryDTO workInfoPojoQueryDTO = new WorkInfoPojoQueryDTO()
                .setPageNum(dto.getPageNum())
                .setPageSize(dto.getPageSize())
                .setCourseId(Objects.nonNull(dto.getCourseId()) && dto.getCourseId() != 0 ? dto.getCourseId() : null)
                .setCompanyId(Objects.nonNull(dto.getCompanyId()) && dto.getCompanyId() != 0 ? dto.getCompanyId() : null)
                .setShowStatus(WorkInfoEnum.ShowStatus.SHOW.getCode())
                .setContentShowStatus(WorkInfoEnum.ContentShowStatus.SHOW.getCode());
        SortBuilder sort = new SortBuilder().add(WorkInfoPojo::getWorkTime, Sort.Direction.DESC);
        IPage<WorkInfoPojo> workInfoPojoIPage = workInfoPojoMongoService.querySimplePage(workInfoPojoQueryDTO, sort);
        if (CollUtil.isEmpty(workInfoPojoIPage.getRecords())) {
            return PageInfoBT.noData();
        }
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        headerUserInfo.setCurrentCompanyId(dto.getCompanyId());
        if (Objects.nonNull(dto.getCourseId()) && !Objects.equals(dto.getCourseId(), 0)) {
            Integer belongCompanyId = orderComponent.getBelongCompanyId(headerUserInfo.getId(), dto.getCourseId());
            headerUserInfo.setCurrentCompanyId(belongCompanyId);
        }
        List<Integer> workIdList = workInfoPojoIPage.getRecords().stream().map(WorkInfoPojo::getId).collect(Collectors.toList());
        // 评论点赞
        Map<Integer, SearchCommentAndLikeTotalVO> commentLikeMap = getSearchCommentAndLikeTotalVOMap(workIdList, headerUserInfo);
        // 调用公共服务-批量获取心得评论数据
        Map<Integer, List<AppWorkCommentListVO>> commentMap = commentComponent.batchSearchCommentList(workIdList, headerUserInfo.getId());
        // 获取商品 id
        Map<Integer, Integer> goodsIdMap = courseComponent.goodsIdListByCourseIdList(workInfoPojoIPage.getRecords().stream().map(WorkInfoPojo::getCourseId).collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(GoodsIdListByCourseIdListVO::getCourseId, GoodsIdListByCourseIdListVO::getGoodsId));
        // 回应信息
        Map<Integer, List<WorkTeacherReply>> replyMap = getCommpanyReplyMap(workIdList);
        // 非本企业下的用户昵称展示
        List<Integer> notCurCompanyIdList = notCurCompanyIdList(headerUserInfo, workInfoPojoIPage, commentMap);
        Map<Integer, String> userFullNameMap = userComponent.userFullNameByIdList(notCurCompanyIdList);

        List<CommonWorkVO> list = new ArrayList<>();
        workInfoPojoIPage.getRecords().forEach(workInfoPojo -> {
            CommonWorkVO vo = BeanUtil.copyProperties(workInfoPojo, CommonWorkVO.class);
            // 作业内容
            List<WorkModelDetailVO> workInfoDetails = JSONArray.parseArray(workInfoPojo.getContent(), WorkModelDetailVO.class);
            // 过滤展示的作业项
            workInfoDetails = workInfoDetails.stream()
                    .filter(detail -> (Objects.nonNull(detail) && YesOrNoEnum.YES.getCode().equals(detail.getShowStatus())))
                    .filter(detail -> !READ.getCode().equals(detail.getType()))
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(workInfoDetails)) return;
            vo.setContentList(BeanUtil.copyToList(workInfoDetails, AppWorkDetailListVO.class));
            vo.setTime(TimeUtil.getCreateTimeStr(workInfoPojo.getWorkTime()));
            SearchCommentAndLikeTotalVO searchCommentAndLikeTotalVO = commentLikeMap.get(workInfoPojo.getId());
            if (Objects.nonNull(searchCommentAndLikeTotalVO)) {
                vo.setCommentTotal(searchCommentAndLikeTotalVO.getCommentTotal());
                vo.setLikeTotal(searchCommentAndLikeTotalVO.getLikeTotal());
                vo.setLikeFlag(searchCommentAndLikeTotalVO.getLikeFlag());
            }
            // 评论信息
            vo.setCommentList(commentMap.get(workInfoPojo.getId()));
            if (CollUtil.isNotEmpty(vo.getCommentList())) {
                vo.getCommentList().forEach(comment -> {
                    if (comment.getCompanyId() != null && !Objects.equals(comment.getCompanyId(), headerUserInfo.getCurrentCompanyId())) {
                        comment.setUserName(userFullNameMap.get(comment.getUserId()));
                    }
                });
            }
            // 商品Id 信息
            vo.setGoodsId(goodsIdMap.get(workInfoPojo.getCourseId()));
            List<WorkTeacherReply> replyList = replyMap.get(workInfoPojo.getId());
            // 回应信息
            if (CollUtil.isNotEmpty(replyList)) {
                List<WorkReplyVO> replyVOList = convertReplyVOList(replyList);
                vo.setReplyList(replyVOList);
            }
            // 用户昵称
            if (Objects.nonNull(workInfoPojo.getCompanyId()) && !Objects.equals(workInfoPojo.getCompanyId(), headerUserInfo.getCurrentCompanyId())) {
                vo.setUserName(userFullNameMap.get(workInfoPojo.getUserId()));
            }
            list.add(vo);
        });
        PageInfoBT<LazyAppWorkInfoVO> pageInfoBT = new PageInfoBT<>();
        pageInfoBT.setCurrent(workInfoPojoIPage.getCurrent());
        pageInfoBT.setSize(workInfoPojoIPage.getSize());
        pageInfoBT.setTotal(workInfoPojoIPage.getTotal());
        pageInfoBT.setRecords(lazy(list));
        return pageInfoBT;
    }

    private List<Integer> notCurCompanyIdList(HeaderUserInfo headerUserInfo, IPage<WorkInfoPojo> workInfoPojoIPage, Map<Integer, List<AppWorkCommentListVO>> commentMap) {
        List<Integer> notCurCompanyIdList = new ArrayList<>();
        workInfoPojoIPage.getRecords().forEach(workInfoPojo -> {
            if (!Objects.equals(workInfoPojo.getCompanyId(), headerUserInfo.getCurrentCompanyId())) {
                notCurCompanyIdList.add(workInfoPojo.getUserId());
            }
        });
        if (Objects.nonNull(commentMap) && CollUtil.isNotEmpty(commentMap.values())) {
            commentMap.values().forEach(list -> {
                if (CollUtil.isNotEmpty(list)) {
                    list.forEach(appWorkCommentListVO -> {
                        if (!Objects.equals(appWorkCommentListVO.getCompanyId(), headerUserInfo.getCurrentCompanyId())) {
                            notCurCompanyIdList.add(appWorkCommentListVO.getUserId());
                        }
                    });
                }
            });
        }
        return notCurCompanyIdList;
    }

    @Override
    public PageInfoBT<LazyAppWorkInfoVO> workReplyPage(WorkReplyPageDTO dto) {
        WorkInfoPojoQueryDTO workInfoPojoQueryDTO = new WorkInfoPojoQueryDTO()
                .setPageNum(dto.getPageNum())
                .setPageSize(dto.getPageSize())
                .setCourseId(Objects.nonNull(dto.getCourseId()) && dto.getCourseId() != 0 ? dto.getCourseId() : null)
                .setCompanyId(Objects.nonNull(dto.getCompanyId()) && dto.getCompanyId() != 0 ? dto.getCompanyId() : null)
                .setShowStatus(WorkInfoEnum.ShowStatus.SHOW.getCode())
                .setCompanyReplyStatus(WorkInfoEnum.CompanyReplyStatus.REPLY.getCode());
        SortBuilder sort = new SortBuilder().add(WorkInfoPojo::getCompanyReplyTime, Sort.Direction.DESC);
        IPage<WorkInfoPojo> workInfoPojoIPage = workInfoPojoMongoService.querySimplePage(workInfoPojoQueryDTO, sort);
        if (CollUtil.isEmpty(workInfoPojoIPage.getRecords())) {
            return PageInfoBT.noData();
        }
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        if (Objects.nonNull(dto.getCourseId()) && !Objects.equals(dto.getCourseId(), 0)) {
            Integer belongCompanyId = orderComponent.getBelongCompanyId(headerUserInfo.getId(), dto.getCourseId());
            headerUserInfo.setCurrentCompanyId(belongCompanyId);
        }
        List<Integer> workIdList = workInfoPojoIPage.getRecords().stream().map(WorkInfoPojo::getId).collect(Collectors.toList());
        // 评论点赞
        Map<Integer, SearchCommentAndLikeTotalVO> commentLikeMap = getSearchCommentAndLikeTotalVOMap(workIdList, headerUserInfo);
        // 获取商品 id
        Map<Integer, Integer> goodsIdMap = courseComponent.goodsIdListByCourseIdList(workInfoPojoIPage.getRecords().stream().map(WorkInfoPojo::getCourseId).collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(GoodsIdListByCourseIdListVO::getCourseId, GoodsIdListByCourseIdListVO::getGoodsId));
        // 回应信息
        Map<Integer, List<WorkTeacherReply>> replyMap = getCommpanyReplyMap(workIdList);
        // 非本企业下的用户昵称展示
        List<Integer> notCurCompanyIdList = notCurCompanyIdList(headerUserInfo, workInfoPojoIPage, new HashMap<>());
        Map<Integer, String> userFullNameMap = userComponent.userFullNameByIdList(notCurCompanyIdList);
        List<CommonWorkVO> list = new ArrayList<>();
        workInfoPojoIPage.getRecords().forEach(workInfoPojo -> {
            CommonWorkVO vo = BeanUtil.copyProperties(workInfoPojo, CommonWorkVO.class);
            // 作业内容
            List<WorkModelDetailVO> workInfoDetails = JSONArray.parseArray(workInfoPojo.getContent(), WorkModelDetailVO.class);
            // 过滤展示的作业项
            workInfoDetails = workInfoDetails.stream()
                    .filter(detail -> (Objects.nonNull(detail) && YesOrNoEnum.YES.getCode().equals(detail.getShowStatus())))
                    .filter(detail -> !READ.getCode().equals(detail.getType()))
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(workInfoDetails)) return;
            vo.setContentList(BeanUtil.copyToList(workInfoDetails, AppWorkDetailListVO.class));
            vo.setTime(TimeUtil.getCreateTimeStr(workInfoPojo.getWorkTime()));
            SearchCommentAndLikeTotalVO searchCommentAndLikeTotalVO = commentLikeMap.get(workInfoPojo.getId());
            if (Objects.nonNull(searchCommentAndLikeTotalVO)) {
                vo.setCommentTotal(searchCommentAndLikeTotalVO.getCommentTotal());
                vo.setLikeTotal(searchCommentAndLikeTotalVO.getLikeTotal());
                vo.setLikeFlag(searchCommentAndLikeTotalVO.getLikeFlag());
            }
            List<WorkTeacherReply> replyList = replyMap.get(workInfoPojo.getId());
            // 商品Id 信息
            vo.setGoodsId(goodsIdMap.get(workInfoPojo.getCourseId()));
            // 回应信息
            if (CollUtil.isNotEmpty(replyList)) {
                List<WorkReplyVO> replyVOList = convertReplyVOList(replyList);
                vo.setReplyList(replyVOList);
            }
            // 用户昵称
            if (Objects.nonNull(workInfoPojo.getCompanyId()) && !Objects.equals(workInfoPojo.getCompanyId(), headerUserInfo.getCurrentCompanyId())) {
                vo.setUserName(userFullNameMap.get(workInfoPojo.getUserId()));
            }
            list.add(vo);
        });
        PageInfoBT<LazyAppWorkInfoVO> pageInfoBT = new PageInfoBT<>();
        pageInfoBT.setCurrent(workInfoPojoIPage.getCurrent());
        pageInfoBT.setSize(workInfoPojoIPage.getSize());
        pageInfoBT.setTotal(workInfoPojoIPage.getTotal());
        pageInfoBT.setRecords(lazy(list));
        return pageInfoBT;
    }

    @Override
    public PageInfoBT<LazyAppWorkInfoVO> workRecommendPage(WorkRecommendPageDTO dto) {
        WorkInfoPojoQueryDTO workInfoPojoQueryDTO = new WorkInfoPojoQueryDTO()
                .setPageNum(dto.getPageNum())
                .setPageSize(dto.getPageSize())
                .setCourseId(Objects.nonNull(dto.getCourseId()) && dto.getCourseId() != 0 ? dto.getCourseId() : null)
                .setCompanyId(Objects.nonNull(dto.getCompanyId()) && dto.getCompanyId() != 0 ? dto.getCompanyId() : null)
                .setShowStatus(WorkInfoEnum.ShowStatus.SHOW.getCode())
                .setRecommendStatus(WorkInfoEnum.RecommendStatus.RECOMMEND.getCode());
        SortBuilder sort = new SortBuilder().add(WorkInfoPojo::getRecommendTime, Sort.Direction.DESC);
        IPage<WorkInfoPojo> workInfoPojoIPage = workInfoPojoMongoService.querySimplePage(workInfoPojoQueryDTO, sort);
        if (CollUtil.isEmpty(workInfoPojoIPage.getRecords())) {
            return PageInfoBT.noData();
        }
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        if (Objects.nonNull(dto.getCourseId()) && !Objects.equals(dto.getCourseId(), 0)) {
            Integer belongCompanyId = orderComponent.getBelongCompanyId(headerUserInfo.getId(), dto.getCourseId());
            headerUserInfo.setCurrentCompanyId(belongCompanyId);
        }
        List<Integer> workIdList = workInfoPojoIPage.getRecords().stream().map(WorkInfoPojo::getId).collect(Collectors.toList());
        // 评论点赞
        Map<Integer, SearchCommentAndLikeTotalVO> commentLikeMap = getSearchCommentAndLikeTotalVOMap(workIdList, headerUserInfo);
        // 调用公共服务-批量获取心得评论数据
        Map<Integer, List<AppWorkCommentListVO>> commentMap = commentComponent.batchSearchCommentList(workIdList, headerUserInfo.getId());
        // 获取商品 id
        Map<Integer, Integer> goodsIdMap = courseComponent.goodsIdListByCourseIdList(workInfoPojoIPage.getRecords().stream().map(WorkInfoPojo::getCourseId).collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(GoodsIdListByCourseIdListVO::getCourseId, GoodsIdListByCourseIdListVO::getGoodsId));
        // 回应信息
        Map<Integer, List<WorkTeacherReply>> replyMap = getCommpanyReplyMap(workIdList);
        // 非本企业下的用户昵称展示
        List<Integer> notCurCompanyIdList = notCurCompanyIdList(headerUserInfo, workInfoPojoIPage, commentMap);
        Map<Integer, String> userFullNameMap = userComponent.userFullNameByIdList(notCurCompanyIdList);

        List<CommonWorkVO> list = new ArrayList<>();
        workInfoPojoIPage.getRecords().forEach(workInfoPojo -> {
            CommonWorkVO vo = BeanUtil.copyProperties(workInfoPojo, CommonWorkVO.class);
            // 作业内容
            List<WorkModelDetailVO> workInfoDetails = JSONArray.parseArray(workInfoPojo.getContent(), WorkModelDetailVO.class);
            // 过滤展示的作业项
            workInfoDetails = workInfoDetails.stream()
                    .filter(detail -> (Objects.nonNull(detail) && YesOrNoEnum.YES.getCode().equals(detail.getShowStatus())))
                    .filter(detail -> !READ.getCode().equals(detail.getType()))
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(workInfoDetails)) return;
            vo.setContentList(BeanUtil.copyToList(workInfoDetails, AppWorkDetailListVO.class));
            vo.setTime(TimeUtil.getCreateTimeStr(workInfoPojo.getWorkTime()));
            SearchCommentAndLikeTotalVO searchCommentAndLikeTotalVO = commentLikeMap.get(workInfoPojo.getId());
            if (Objects.nonNull(searchCommentAndLikeTotalVO)) {
                vo.setCommentTotal(searchCommentAndLikeTotalVO.getCommentTotal());
                vo.setLikeTotal(searchCommentAndLikeTotalVO.getLikeTotal());
                vo.setLikeFlag(searchCommentAndLikeTotalVO.getLikeFlag());
            }
            List<WorkTeacherReply> replyList = replyMap.get(workInfoPojo.getId());
            // 评论信息
            vo.setCommentList(commentMap.get(workInfoPojo.getId()));
            if (CollUtil.isNotEmpty(vo.getCommentList())) {
                vo.getCommentList().forEach(comment -> {
                    if (comment.getCompanyId() != null && !Objects.equals(comment.getCompanyId(), headerUserInfo.getCurrentCompanyId())) {
                        comment.setUserName(userFullNameMap.get(comment.getUserId()));
                    }
                });
            }
            // 商品Id 信息
            vo.setGoodsId(goodsIdMap.get(workInfoPojo.getCourseId()));
            // 回应信息
            if (CollUtil.isNotEmpty(replyList)) {
                List<WorkReplyVO> replyVOList = convertReplyVOList(replyList);
                vo.setReplyList(replyVOList);
            }
            // 用户昵称
            if (Objects.nonNull(workInfoPojo.getCompanyId()) && !Objects.equals(workInfoPojo.getCompanyId(), headerUserInfo.getCurrentCompanyId())) {
                vo.setUserName(userFullNameMap.get(workInfoPojo.getUserId()));
            }
            list.add(vo);
        });
        PageInfoBT<LazyAppWorkInfoVO> pageInfoBT = new PageInfoBT<>();
        pageInfoBT.setCurrent(workInfoPojoIPage.getCurrent());
        pageInfoBT.setSize(workInfoPojoIPage.getSize());
        pageInfoBT.setTotal(workInfoPojoIPage.getTotal());
        pageInfoBT.setRecords(lazy(list));
        return pageInfoBT;
    }

    @Override
    public ClassWorkDataVO classWorkData(ClassWorkDataDTO dto) {
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(dto.getCourseId(), headerUserInfo.getId());
        dto.setCompanyId(userRightsCourse.getCompanyId());
        ClassWorkDataVO vo = new ClassWorkDataVO();
        vo.setDeadlineTime(LocalDateTime.now());
        ClassesTeamDetailVO classesTeamInfoByUser = classesComponent.getClassesTeamInfoByUser(dto.getCompanyId(), dto.getCourseId(), headerUserInfo.getId());
        if (classesTeamInfoByUser == null) {
            vo.setInClass(0);
            return vo;
        }
        CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(dto.getCourseId(), null);
        Integer courseTimetableId = courseTimetableUnlockDetailVO.getId() != null ? courseTimetableUnlockDetailVO.getId() : null;
        LambdaQueryWrapper<ClassStudyStatistics> wrapper = new LambdaQueryWrapper<ClassStudyStatistics>()
                .eq(ClassStudyStatistics::getClassesId, classesTeamInfoByUser.getClassesId())
                .eq(ClassStudyStatistics::getCompanyId, dto.getCompanyId())
                .eq(ClassStudyStatistics::getCourseId, dto.getCourseId());
        List<ClassStudyStatistics> list = classStudyStatisticsMapper.selectList(wrapper);
        if (CollUtil.isEmpty(list)) {
            return vo;
        }
        list.stream().filter(statistics -> Objects.equals(statistics.getCourseTimetableId(), courseTimetableId)).findFirst()
                .ifPresent(statistics -> {
                    vo.setCurrentWorkRate(!"NaN".equals(statistics.getCurrentSubRate()) ? Double.parseDouble(statistics.getCurrentSubRate()) : 0D);
                    vo.setCurrentWorkRateStr(statistics.getCurrentSubRate());
                    vo.setCurrentListenRate(!"NaN".equals(statistics.getCurrentListenRate()) ? Double.parseDouble(statistics.getCurrentListenRate()) : 0D);
                    vo.setCurrentListenRateStr(statistics.getCurrentListenRate());
                });
        // 累计作业数 累计听课数 作业总数
        CalcWorkVO calcWorkVO = new CalcWorkVO();
        list.stream().filter(statistics -> !Objects.equals(statistics.getCourseTimetableId(), courseTimetableId)).forEach(statistics -> {
            calcWorkVO.setWorkTotal(calcWorkVO.getWorkTotal() + statistics.getSubTotal());
            calcWorkVO.setListenTotal(calcWorkVO.getListenTotal() + statistics.getListenTotal());
            calcWorkVO.setTotal(calcWorkVO.getTotal() + statistics.getClassMemberTotal());
        });
        DecimalFormat decimalFormat = new DecimalFormat("0.00%");
        vo.setAccWorkRate(calcWorkVO.getWorkTotal() != 0 ? calcWorkVO.getWorkTotal() * 100 / calcWorkVO.getTotal() : 0D);
        vo.setAccWorkRateStr(decimalFormat.format(vo.getAccWorkRate()));
        vo.setAccListenRate(calcWorkVO.getListenTotal() != 0 ? calcWorkVO.getListenTotal() * 100 / calcWorkVO.getTotal() : 0D);
        vo.setAccListenRateStr(decimalFormat.format(vo.getAccListenRate()));
        return vo;
    }
    @Override
    public ClassWorkRankVO classWorkRank(ClassWorkRankDTO dto) {
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(dto.getCourseId(), headerUserInfo.getId());
        dto.setCompanyId(userRightsCourse.getCompanyId());
        ClassWorkRankVO vo = new ClassWorkRankVO();
        ClassesTeamDetailVO classesTeamInfoByUser = classesComponent.getClassesTeamInfoByUser(dto.getCompanyId(), dto.getCourseId(), headerUserInfo.getId());
        CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(dto.getCourseId(), null);
        if (Objects.isNull(courseTimetableUnlockDetailVO)) {
            throw new BusinessException("课程已结束");
        }
        LambdaQueryWrapper<ClassStudyStatistics> wrapper = new LambdaQueryWrapper<ClassStudyStatistics>()
                .ne(ClassStudyStatistics::getClassesId, 0)
                .eq(ClassStudyStatistics::getCompanyId, dto.getCompanyId())
                .eq(ClassStudyStatistics::getCourseId, dto.getCourseId())
                .eq(ClassStudyStatistics::getCourseTimetableId, courseTimetableUnlockDetailVO.getId());
        List<ClassStudyStatistics> list = classStudyStatisticsMapper.selectList(wrapper);
        if (CollUtil.isEmpty(list)) {
            return vo;
        }
        // 手动排序
        ListUtil.sort(list, (o1, o2) -> {
            if ("NaN".equals(o1.getCurrentSubRate())) {
                o1.setCurrentSubRate("0.00");
            }
            if ("NaN".equals(o2.getCurrentSubRate())) {
                o2.setCurrentSubRate("0.00");
            }
            if ("NaN".equals(o1.getCurrentListenRate())) {
                o1.setCurrentListenRate("0.00");
            }
            if ("NaN".equals(o2.getCurrentListenRate())) {
                o2.setCurrentListenRate("0.00");
            }
            if (Objects.equals(o1.getCurrentSubRate(), o2.getCurrentSubRate())) {
                return Double.valueOf(o2.getCurrentListenRate()).compareTo(Double.valueOf(o1.getCurrentListenRate()));
            }
            return Double.valueOf(o2.getCurrentSubRate()).compareTo(Double.valueOf(o1.getCurrentSubRate()));
        });
        for (int i = 0; i < list.size(); i++) {
            ClassStudyStatistics classStudyStatistics = list.get(i);
            // 拿资源 id 这个字段暂时作为排名记录
            classStudyStatistics.setResourceId(i + 1);
            if (classesTeamInfoByUser != null && Objects.equals(classStudyStatistics.getClassesId(), classesTeamInfoByUser.getClassesId())) {
                vo.setCurrentRank(i + 1);
            }
        }
        int offset = (dto.getPageNum() - 1) * dto.getPageSize();
        if (offset > list.size()) {
            return vo;
        }
        if (Objects.equals(dto.getSort(), 2)) {
            ListUtil.reverse(list);
        }
        // 手动分页
        List<ClassStudyStatistics> subList = list.subList(offset, Math.min(offset + dto.getPageSize(), list.size()));
        List<Integer> classIdList = subList.stream().map(ClassStudyStatistics::getClassesId).collect(Collectors.toList());
        Map<Integer, GetSimpleClassInfoVO> classMap = classesComponent.getSimpleClassInfo(dto.getCompanyId(), dto.getCourseId(), classIdList)
                .stream().collect(Collectors.toMap(GetSimpleClassInfoVO::getClassesId, Function.identity()));
        DecimalFormat decimalFormat = new DecimalFormat("0.00%");
        List<ClassWorkRankItemVO> voList = subList.stream().map(item -> {
            ClassWorkRankItemVO rankItemVO = new ClassWorkRankItemVO();
            rankItemVO.setRankNum(item.getResourceId());
            rankItemVO.setCurrentWorkRate(item.getCurrentSubRate() != null && !"NaN".equals(item.getCurrentSubRate()) ? Double.parseDouble(item.getCurrentSubRate()) : 0);
            rankItemVO.setCurrentWorkRateStr(decimalFormat.format(rankItemVO.getCurrentWorkRate() / 100));
            rankItemVO.setCurrentListenRate(item.getCurrentListenRate() != null && !"NaN".equals(item.getCurrentListenRate()) ? Double.parseDouble(item.getCurrentListenRate()) : 0);
            rankItemVO.setCurrentListenRateStr(decimalFormat.format(rankItemVO.getCurrentListenRate() / 100));
            GetSimpleClassInfoVO simpleClassInfoVO = classMap.get(item.getClassesId());
            if (Objects.nonNull(simpleClassInfoVO)) {
                rankItemVO.setClassName(simpleClassInfoVO.getClassesName());
                rankItemVO.setClassTeacherName(simpleClassInfoVO.getClassesOwnerName());
            }
            return rankItemVO;
        }).collect(Collectors.toList());
        PageInfoBT<ClassWorkRankItemVO> pageInfoBT = new PageInfoBT<>();
        pageInfoBT.setCurrent((long) dto.getPageNum());
        pageInfoBT.setSize((long) dto.getPageSize());
        pageInfoBT.setRecords(voList);
        pageInfoBT.setTotal((long) list.size());
        vo.setRankPage(pageInfoBT);
        return vo;
    }

    @Override
    public PersonalWorkDataVO personalWorkData(PersonalWorkDataDTO dto) {
        PersonalWorkDataVO vo = new PersonalWorkDataVO();
        vo.setDeadlineTime(LocalDateTime.now());
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(dto.getCourseId(), headerUserInfo.getId());
        dto.setCompanyId(userRightsCourse.getCompanyId());
        List<WorkInfoPojo> list = workInfoPojoMongoService.querySimpleList(new WorkInfoPojoQueryDTO()
                .setCompanyId(dto.getCompanyId())
                .setCourseId(dto.getCourseId())
                .setUserId(headerUserInfo.getId()));
        if (CollUtil.isEmpty(list)) {
            return vo;
        }
        CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(dto.getCourseId(), null);
        DecimalFormat decimalFormat = new DecimalFormat("0.00%");
        long count = list.stream().filter(workInfoPojo -> !courseTimetableUnlockDetailVO.getId().equals(workInfoPojo.getCourseTimetableId())).count();
        long workCount = list.stream()
                .filter(workInfoPojo -> !courseTimetableUnlockDetailVO.getId().equals(workInfoPojo.getCourseTimetableId()))
                .filter(workInfoPojo -> WorkInfoEnum.WorkStatus.OK.getCode().equals(workInfoPojo.getWorkStatus())).count();
        long listenCount = list.stream()
                .filter(workInfoPojo -> !courseTimetableUnlockDetailVO.getId().equals(workInfoPojo.getCourseTimetableId()))
                .filter(workInfoPojo -> WorkInfoEnum.ListenStatus.LISTEN.getCode().equals(workInfoPojo.getListenStatus())).count();
        vo.setAccWorkRate((double) workCount * 100 / count);
        vo.setAccWorkRateStr(decimalFormat.format(vo.getAccWorkRate() / 100));
        vo.setAccListenRate((double) listenCount * 100 / count);
        vo.setAccListenRateStr(decimalFormat.format(vo.getAccListenRate() / 100));
        return vo;
    }

    @Override
    public PersonalWorkRankVO personalWorkRank(PersonalWorkRankDTO dto) {
        CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(dto.getCourseId(), null);
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(dto.getCourseId(), headerUserInfo.getId());
        dto.setCompanyId(userRightsCourse.getCompanyId());
        List<WorkInfoPojo> list = workInfoPojoMongoService.personalWorkRank(dto, courseTimetableUnlockDetailVO.getId());
        PersonalWorkRankVO vo = new PersonalWorkRankVO();
        if (CollUtil.isEmpty(list)) {
            return vo;
        }
        Map<Integer, Long> userCountMap = list.stream().collect(Collectors.groupingBy(WorkInfoPojo::getUserId, Collectors.counting()));
        int offset = (dto.getPageNum() - 1) * dto.getPageSize();
        if (offset > userCountMap.size()) {
            return vo;
        }
        Map<Integer, Long> workCountMap = list.stream()
                .filter(workInfoPojo -> WorkInfoEnum.WorkStatus.OK.getCode().equals(workInfoPojo.getWorkStatus()))
                .collect(Collectors.groupingBy(WorkInfoPojo::getUserId, Collectors.counting()));
        Map<Integer, Long> listenCountMap = list.stream()
                .filter(workInfoPojo -> WorkInfoEnum.ListenStatus.LISTEN.getCode().equals(workInfoPojo.getListenStatus()))
                .collect(Collectors.groupingBy(WorkInfoPojo::getUserId, Collectors.counting()));
        List<PersonalWorkRankItemVO> voList = new ArrayList<>();
        DecimalFormat decimalFormat = new DecimalFormat("0.00%");
        userCountMap.forEach((userId, count) -> {
            PersonalWorkRankItemVO itemVO = new PersonalWorkRankItemVO();
            voList.add(itemVO);
            itemVO.setUserId(userId);
            Long workCount = workCountMap.get(userId);
            Long listenCount = listenCountMap.get(userId);
            if (Objects.nonNull(workCount)) {
                itemVO.setAccWorkRate((double) workCount * 100 / count);
                itemVO.setAccWorkRateStr(decimalFormat.format(itemVO.getAccWorkRate() / 100));
            } else {
                itemVO.setAccWorkRate(0D);
                itemVO.setAccWorkRateStr("0.00%");
            }
            if (Objects.nonNull(listenCount)) {
                itemVO.setAccListenRate((double) listenCount * 100 / count);
                itemVO.setAccListenRateStr(decimalFormat.format(itemVO.getAccListenRate() / 100));
            } else {
                itemVO.setAccListenRate(0D);
                itemVO.setAccListenRateStr("0.00%");
            }
        });
        voList.sort((o1, o2) -> {
            if (o1.getAccWorkRate().equals(o2.getAccWorkRate())) {
                return o2.getAccListenRate().compareTo(o1.getAccListenRate());
            } else {
                return o2.getAccWorkRate().compareTo(o1.getAccWorkRate());
            }
        });
        // 每个人的排名以及当前排名
        for (int i = 0; i < voList.size(); i++) {
            PersonalWorkRankItemVO itemVO = voList.get(i);
            itemVO.setRankNum(i + 1);
            if (itemVO.getUserId().equals(headerUserInfo.getId())) {
                vo.setCurrentRank(i + 1);
            }
        }
        if (Objects.equals(dto.getSort(), 2)) {
            ListUtil.reverse(voList);
        }
        // 手动分页
        List<PersonalWorkRankItemVO> returnList = voList.subList(offset, Math.min(offset + dto.getPageSize(), voList.size()));
        List<Integer> userIdList = returnList.stream().map(PersonalWorkRankItemVO::getUserId).collect(Collectors.toList());
        Map<Integer, String> userCompanyNameMap = userComponent.queryUserCompanyNameById(dto.getCompanyId(), userIdList);
        returnList.forEach(itemVO -> {
            itemVO.setUserName(userCompanyNameMap.get(itemVO.getUserId()));
        });
        PageInfoBT<PersonalWorkRankItemVO> pageInfoBT = new PageInfoBT<>();
        pageInfoBT.setCurrent((long) dto.getPageNum());
        pageInfoBT.setSize((long) dto.getPageSize());
        pageInfoBT.setRecords(returnList);
        pageInfoBT.setTotal((long) voList.size());
        vo.setRankPage(pageInfoBT);
        return vo;
    }

    @Override
    public CompanyRankVO companyRank(CompanyRankDTO dto) {
        CompanyRankVO vo = new CompanyRankVO();
        vo.setDeadlineTime(LocalDateTime.now());
        CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(dto.getCourseId(), null);
        // 往期作业率听课率 从 MySQL 里查
        List<ClassStudyStatistics> accList = classStudyStatisticsMapper.selectList(new LambdaQueryWrapper<ClassStudyStatistics>()
                .eq(ClassStudyStatistics::getCompanyId, dto.getCompanyId())
                .eq(ClassStudyStatistics::getCourseId, dto.getCourseId())
                .ne(ClassStudyStatistics::getCourseTimetableId, courseTimetableUnlockDetailVO.getId())
        );
        DecimalFormat decimalFormat = new DecimalFormat("0.00%");
        if (CollUtil.isNotEmpty(accList)) {
            int count = accList.stream().mapToInt(ClassStudyStatistics::getClassMemberTotal).sum();
            int workCount = accList.stream().mapToInt(ClassStudyStatistics::getSubTotal).sum();
            int listenCount = accList.stream().mapToInt(ClassStudyStatistics::getListenTotal).sum();
            vo.setAccWorkRate((double) workCount * 100 / count);
            vo.setAccWorkRateStr(decimalFormat.format(vo.getAccWorkRate() / 100));
            vo.setAccListenRate((double) listenCount * 100 / count);
            vo.setAccListenRateStr(decimalFormat.format(vo.getAccListenRate() / 100));
        }
        // 企业的当期数据
        Map<Integer, List<ClassStudyStatistics>> companyStatMap = classStudyStatisticsMapper.selectList(new LambdaQueryWrapper<ClassStudyStatistics>()
                        .eq(ClassStudyStatistics::getCourseId, dto.getCourseId())
                        .eq(ClassStudyStatistics::getCourseTimetableId, courseTimetableUnlockDetailVO.getId()))
                .stream().collect(Collectors.groupingBy(ClassStudyStatistics::getCompanyId));
        List<CompanyRankItemVO> rankList = new ArrayList<>();
        companyStatMap.forEach((companyId, list) -> {
            CompanyRankItemVO itemVO = new CompanyRankItemVO();
            itemVO.setCompanyId(companyId);
            long count = list.stream().mapToInt(ClassStudyStatistics::getClassMemberTotal).sum();
            long workCount = list.stream().mapToInt(ClassStudyStatistics::getSubTotal).sum();
            long listenCount = list.stream().mapToInt(ClassStudyStatistics::getListenTotal).sum();
            itemVO.setCurrentWorkRate((double) workCount * 100 / count);
            itemVO.setCurrentWorkRateStr(decimalFormat.format(itemVO.getCurrentWorkRate() / 100));
            itemVO.setCurrentListenRate((double) listenCount * 100 / count);
            itemVO.setCurrentListenRateStr(decimalFormat.format(itemVO.getCurrentListenRate() / 100));
            rankList.add(itemVO);
        });
        // 先按照累计作业率倒序排序，再按照累计听课率倒序排序
        rankList.sort((o1, o2) -> {
            if (o1.getCurrentWorkRate().equals(o2.getCurrentWorkRate())) {
                return o2.getCurrentListenRate().compareTo(o1.getCurrentListenRate());
            } else {
                return o2.getCurrentWorkRate().compareTo(o1.getCurrentWorkRate());
            }
        });
        // 当前排名
        for (int i = 0; i < rankList.size(); i++) {
            if (rankList.get(i).getCompanyId().equals(dto.getCompanyId())) {
                vo.setCurrentRank(i + 1);
                // 当前企业下的当期作业率
                vo.setCurrentWorkRate(rankList.get(i).getCurrentWorkRate());
                vo.setCurrentWorkRateStr(decimalFormat.format(vo.getCurrentWorkRate() / 100));
                vo.setCurrentListenRate(rankList.get(i).getCurrentListenRate());
                vo.setCurrentListenRateStr(decimalFormat.format(vo.getCurrentListenRate() / 100));
                break;
            }
        }
        List<CompanyRankItemVO> returnList = rankList.subList(0, Math.min(10, rankList.size()));
        Map<Integer, String> companyNameMap = userComponent.queryCompanyNameById(returnList.stream().map(CompanyRankItemVO::getCompanyId).collect(Collectors.toList()));
        for (int i = 0; i < returnList.size(); i++) {
            CompanyRankItemVO companyRankItemVO = returnList.get(i);
            companyRankItemVO.setRankNum(i + 1);
            companyRankItemVO.setCompanyName(companyNameMap.get(companyRankItemVO.getCompanyId()));
        }
        vo.setRankList(returnList);
        return vo;
    }

    @Override
    public WorkByIdVO workById(IdDTO dto) {
        WorkInfoPojo work = workInfoPojoMongoService.getByWorkId(dto.getId());
        if (work != null) {
            return BeanUtil.copyProperties(work, WorkByIdVO.class);
        }
        return null;
    }

    @Override
    public void readAddScore(ReadAddScoreDTO dto) {
        List<Integer> courseTimetableIdList = getCourseTimetableIdList(dto);
        if (CollUtil.isEmpty(courseTimetableIdList)) return;
        List<CourseTimetableModelDetail> modelDetailList = courseTimetableModelDetailMapper.selectList(new LambdaQueryWrapper<CourseTimetableModelDetail>()
                .in(CourseTimetableModelDetail::getCourseTimetableId, courseTimetableIdList)
                .eq(CourseTimetableModelDetail::getDeleted, 1) // 是否删除 1:未删除 0:删除
                .eq(CourseTimetableModelDetail::getType, READ.getCode()));
        if (CollUtil.isEmpty(modelDetailList)) {
            log.info("没有正在进行的包含读书作业项的课件");
            return;
        }
        Map<Long, AppChapterVO> chapterIdMap = dto.getChapterList().stream().collect(Collectors.toMap(AppChapterVO::getId, v -> v));
        modelDetailList.stream().filter(courseTimetableModelDetail -> {
            if (StrUtil.isBlank(courseTimetableModelDetail.getEbookJson())) {
                log.info("章节信息未配置,courseTimetableModelDetail Id:{}", courseTimetableModelDetail.getId());
                return false;
            }
            AppEbookVO appEbookVO = JSON.parseObject(courseTimetableModelDetail.getEbookJson(), AppEbookVO.class);
            for (AppChapterVO appChapterVO : appEbookVO.getChapterList()) {
                if (chapterIdMap.containsKey(appChapterVO.getId())) {
                    return true;
                }
            }
            log.info("没有匹配到相同的章节信息");
            return false;
        }).forEach(courseTimetableModelDetail -> {
            WorkInfo workInfo = workInfoMapper.selectOne(new LambdaQueryWrapper<WorkInfo>()
                    .eq(WorkInfo::getUserId, dto.getUserId())
                    .eq(WorkInfo::getCourseTimetableId, courseTimetableModelDetail.getCourseTimetableId())
                    .last("limit 1"));
            if (workInfo == null) {
                // 没有作业，创建作业
                log.error("已读书，没有查到作业 courseTimetableModelDetail:{}", JSON.toJSONString(courseTimetableModelDetail));
                CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(courseTimetableModelDetail.getCourseId(), courseTimetableModelDetail.getCourseTimetableId());
                log.info("syncDoWork courseTimetableUnlockDetailVO:{}", JSON.toJSONString(courseTimetableUnlockDetailVO));
                UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(courseTimetableModelDetail.getCourseId(), dto.getUserId());
                if (userRightsCourse == null) {
                    log.info("暂无这门课的权限，不读书加分,userId:{},courseId:{}", dto.getUserId(), courseTimetableModelDetail.getCourseId());
                    return;
                }
                workInfo = this.workInfoBuild(courseTimetableUnlockDetailVO, dto.getUserId(), courseTimetableModelDetail.getCourseId(), userRightsCourse.getCompanyId());
            }
            CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(workInfo.getCourseId(), workInfo.getCourseTimetableId());
            workInfo.setWorkModelFlag(courseTimetableUnlockDetailVO.getWorkModelFlag());
            boolean isOldFlag = getIsOldFlag(courseTimetableUnlockDetailVO.getWorkModelFlag(), workInfo.getUserId(), workInfo.getCourseId());
            workInfo.setUserFlag(isOldFlag ? 1 : 0);
            WorkUserModel workUserModel = getWorkUserModel(courseTimetableUnlockDetailVO.getWorkModelFlag(), workInfo.getUserId(), workInfo.getCourseId());
            // 如果已经有分或者已写过作业,就不能修改模板等级了
            workInfo.setLevel((Objects.equals(workInfo.getWorkStatus(), 1) || workInfo.getScore() > 0) && StrUtil.isNotBlank(workInfo.getLevel())
                    ? workInfo.getLevel() : Objects.nonNull(workUserModel) && StringUtils.isNotBlank(workUserModel.getLevel()) ? workUserModel.getLevel() : "");
            Integer workModelId = getSimpleWorkModelIdByCourseTimetableUnlockDetailVO(courseTimetableUnlockDetailVO, isOldFlag, workUserModel, workInfo);
            workInfo.setWorkModelId(workModelId);
            List<WorkModelDetailVO> workModelDetailVOList;
            if (StrUtil.isBlank(workInfo.getContent())) {
                workModelDetailVOList = queryWorkModelDetailList(workInfo.getWorkModelId(), workInfo.getCourseId(), courseTimetableUnlockDetailVO);
            } else {
                workModelDetailVOList = JSONObject.parseArray(workInfo.getContent(), WorkModelDetailVO.class);
            }
            if (CollUtil.isEmpty(workModelDetailVOList)) {
                log.info("有作业，作业内容最终为空");
                return;
            }
            Map<Integer, WorkModelDetailVO> workModelDetailVOMap = queryWorkModelDetailList(workInfo.getWorkModelId(), workInfo.getCourseId(), courseTimetableUnlockDetailVO)
                    .stream().collect(Collectors.toMap(WorkModelDetailVO::getId, workModelDetailVO -> workModelDetailVO));
            Jedis jedis = JedisPoolImpl.getResource();
            JedisDistributedLock lock = new JedisDistributedLock(jedis, "readAddScore:" + workInfo.getCompanyId() + "-" + workInfo.getCourseTimetableId() + "-" + dto.getUserId(), 10000, 5000);// acquireTimeoutMillis:超过该时间没有获取到锁，直接返回false; expiryTimeMillis:锁过期时间)
            try {
                if (!lock.acquireEp()) {
                    log.info("readAddScore 加锁失败,userId_{}, courseId_{}, companyId_{}, courseTimeTableId_{}", dto.getUserId(), workInfo.getCourseId(), workInfo.getCompanyId(), workInfo.getCourseTimetableId());
                    return;
                }
                int score = 0;
                for (WorkModelDetailVO workModelDetailVO : workModelDetailVOList) {
                    if (READ.getCode().equals(workModelDetailVO.getType())) {
                        if ("1".equals(workModelDetailVO.getContent())) {
                            if (Objects.isNull(workModelDetailVO.getReadRecordId())) {
                                workModelDetailVO.setReadRecordId(dto.getReadRecordId());
                            }
                            log.info("已经读书加分，本次不加分");
                            continue;
                        }
                        WorkModelDetailVO originWorkModelDetail = workModelDetailVOMap.get(workModelDetailVO.getId());
                        score = score + originWorkModelDetail.getScore();
                        workModelDetailVO.setScore(originWorkModelDetail.getScore());
                        workModelDetailVO.setContent("1");
                        workModelDetailVO.setEbookId(originWorkModelDetail.getEbookId());
                        workModelDetailVO.setEbookName(originWorkModelDetail.getEbookName());
                        workModelDetailVO.setChapterList(originWorkModelDetail.getChapterList());
                        workModelDetailVO.setReadRecordId(dto.getReadRecordId());
                    }
                }
                workInfo.setContent(JSONObject.toJSONString(workModelDetailVOList));
                log.info("readAddScore add score:{}", score);
                workInfo.setScore(workInfo.getScore() + score);
                workInfoMapper.saveOrUpdate(workInfo);
                workInfoPojoMongoService.saveOrUpdate(workInfo);
                log.info("readAddScore 用户 {} 读书加分，作业id:{},作业内容:{}", dto.getUserId(), workInfo.getId(), workInfo.getContent());
                UserStudyInfo userStudyInfo = this.getUserStudyInfo(dto.getUserId(), workInfo.getCourseId(), workInfo.getCompanyId());
                userStudyInfo.setScore(userStudyInfo.getScore() + score);
                userStudyInfoMapper.updateById(userStudyInfo);
                DoWorkSendKafkaBO doWorkSendKafkaBO = this.workSendKafkaBoBuild(workInfo.getCompanyId(), workInfo.getClassesId(), workInfo.getTeamId(), workInfo.getUserId(), workInfo.getCourseId(), workInfo.getCourseTimetableId(), false, false);
                doWorkSendKafkaBO.setScore(workInfo.getScore());
                doWorkSendKafkaBO.setTag(WorkKafkaMessageEnum.DO_WORK.getCode());
                String doWorkSendKafkaBOStr = JSONObject.toJSONString(doWorkSendKafkaBO);
                log.info("readAddScore doWork,userId_{}, courseId_{}, companyId_{}, courseTimeTableId_{}, kafka消息内容:{}",
                        workInfo.getUserId(), workInfo.getCourseId(), workInfo.getCompanyId(), workInfo.getCourseTimetableId(), doWorkSendKafkaBOStr);
                kafkaProduction.send(workTopic, String.valueOf(doWorkSendKafkaBO.getUserId()), JSONObject.parseObject(doWorkSendKafkaBOStr));
            } catch (Exception e) {
                log.error("readAddScore 报错,userId_{}, courseId_{}, companyId_{}, courseTimeTableId_{},error:{}",
                        dto.getUserId(), workInfo.getCourseId(), workInfo.getCompanyId(), workInfo.getCourseTimetableId(), e.getMessage(), e);
            } finally {
                lock.release();
            }
        });
    }

    private List<Integer> getCourseTimetableIdList(ReadAddScoreDTO dto) {
        if (dto.getCourseTimetableId() != null && dto.getCourseTimetableId() != 0) {
            log.info("有准确的课件 id:{}", dto.getCourseTimetableId());
            return Collections.singletonList(dto.getCourseTimetableId());
        }
        List<Integer> courseIdList = orderComponent.rightList(dto.getUserId())
                .stream().map(LegalRightCourse::getCourseId).collect(Collectors.toList());
        if (CollUtil.isEmpty(courseIdList)) {
            log.info("用户没有课程权限");
            return null;
        }
        List<Integer> courseTimetableIdList = courseComponent.batchGetWorkingCourseTimetables(courseIdList)
                .stream().map(CourseTimetableHistoryVO::getId).collect(Collectors.toList());
        if (CollUtil.isEmpty(courseTimetableIdList)) {
            log.info("没有正处于作业时间的课件");
            return null;
        }
        return courseTimetableIdList;
    }

    @Override
    public OneWorkVO oneWork(OneWorkDTO dto) {
        WorkInfoPojo workInfo = workInfoPojoMongoService.getByWorkId(dto.getId());
        if (workInfo == null) {
            throw new BusinessException("作业信息不存在");
        }
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        OneWorkVO vo = BeanUtil.copyProperties(workInfo, OneWorkVO.class);
        List<WorkModelDetailVO> workInfoDetails = JSONArray.parseArray(workInfo.getContent(), WorkModelDetailVO.class);
        List<Long> readRecordIdList = workInfoDetails.stream().filter(detail -> (Objects.nonNull(detail) && YesOrNoEnum.YES.getCode().equals(detail.getShowStatus())))
                .filter(detail -> {
                    if ("h5".equals(headerUserInfo.getAppOs())) {
                        return true;
                    } else {
                        return TEXT.getCode().equals(detail.getType()) || (AUDIO_TEXT.getCode().equals(detail.getType()) && StrUtil.isNotBlank(detail.getContent()));
                    }
                }).map(WorkModelDetailVO::getReadRecordId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, HeartReadListVO> readRecordMap = activityComponent.heartReadList(readRecordIdList);
        List<AppWorkDetailListVO> appWorkDetailList = workInfoDetails.stream().filter(detail -> (Objects.nonNull(detail) && YesOrNoEnum.YES.getCode().equals(detail.getShowStatus())))
                .filter(detail -> {
                    if ("h5".equals(headerUserInfo.getAppOs())) {
                        return true;
                    } else {
                        return TEXT.getCode().equals(detail.getType()) || (AUDIO_TEXT.getCode().equals(detail.getType()) && StrUtil.isNotBlank(detail.getContent()));
                    }
                })
                .sorted(this::moveReadTypeToLast)
                .map(workInfoDetail -> {
                    AppWorkDetailListVO appWorkDetailListVO = BeanUtil.copyProperties(workInfoDetail, AppWorkDetailListVO.class);
                    if (!"h5".equals(headerUserInfo.getAppOs()) && AUDIO_TEXT.getCode().equals(workInfoDetail.getType())) {
                        appWorkDetailListVO.setAudioList(null);
                    }
                    if (Objects.nonNull(readRecordMap)) {
                        if (READ.getCode().equals(workInfoDetail.getType())) {
                            HeartReadListVO heartReadListVO = readRecordMap.get(workInfoDetail.getReadRecordId());
                            AppWorkReadHeartVO appWorkReadHeartVO = BeanUtil.copyProperties(heartReadListVO, AppWorkReadHeartVO.class);
                            appWorkDetailListVO.setReadVO(appWorkReadHeartVO);
                            appWorkDetailListVO.setContent(null);
                        }
                    }
                    return appWorkDetailListVO;
                }).collect(Collectors.toList());
        vo.setWorkContentList(appWorkDetailList);
        List<Integer> idList = Collections.singletonList(vo.getId());
        SearchCommentAndLikeTotalDTO searchCommentAndLikeTotalDTO = new SearchCommentAndLikeTotalDTO();
        searchCommentAndLikeTotalDTO.setCommentType(CommentTypeEnum.EXPERIENCE.getCode());
        searchCommentAndLikeTotalDTO.setLikeType(LikeTypeEnum.HEART_LIKE.getCode());
        searchCommentAndLikeTotalDTO.setUserId(headerUserInfo.getId());
        searchCommentAndLikeTotalDTO.setDataIdList(idList);
        Map<Integer, SearchCommentAndLikeTotalVO> commentAndLikeTotalVOMap = commentComponent.searchCommentAndLikeTotal(searchCommentAndLikeTotalDTO);
        SearchCommentAndLikeTotalVO searchCommentAndLikeTotalVO = commentAndLikeTotalVOMap.get(vo.getId());
        if (searchCommentAndLikeTotalVO != null) {
            vo.setLikeTotal(searchCommentAndLikeTotalVO.getLikeTotal());
            vo.setLikeFlag(searchCommentAndLikeTotalVO.getLikeFlag());
        }
        // 标签信息
        vo.setCommonLabelVOS(convertCommonLabel(workInfo));
        Map<Integer, List<AppWorkTeacherReplyVO>> replyMap = findTeacherReplyListByWorkIds(idList, headerUserInfo.getId(), null);
        vo.setTeacherReplyList(replyMap.get(vo.getId()));
        CourseInfo courseInfo = courseComponent.courseInfoById(workInfo.getCourseId());
        vo.setCourseImgUrl(courseInfo != null ? courseInfo.getCourseImgUrl() : "");
        return vo;
    }

    @Override
    public CourseWorkPlanSchemeOptionVO courseWorkPlanSchemeOption(CourseWorkPlanSchemeOptionDTO dto) {
        if (Objects.equals(dto.getTeamId(), 9)) {
            dto.setTeamId(null);
        }
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(dto.getCourseId(), headerUserInfo.getId());
        headerUserInfo.setCurrentCompanyId(userRightsCourse.getCompanyId());
        CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(dto.getCourseId(), dto.getCourseTimetableId(), dto.getPlanSchemeOption());
        ClassesTeamDetailVO classesTeamInfoByUser = classesComponent.getClassesTeamInfoByUser(headerUserInfo.getCurrentCompanyId(), dto.getCourseId(), headerUserInfo.getId());
        CourseWorkPlanSchemeOptionVO vo = new CourseWorkPlanSchemeOptionVO();
        vo.setCourseTimeTableName(courseTimetableUnlockDetailVO.getResourceTitle());
        List<WorkInfoPojo> workList = workInfoPojoMongoService.querySimpleList(new WorkInfoPojoQueryDTO().setCourseId(dto.getCourseId()).setCourseTimetableId(dto.getCourseTimetableId()).setClassesId(classesTeamInfoByUser.getClassesId())
                .setTeamId(dto.getTeamId()));
        if (CollUtil.isEmpty(workList)) {
            return vo;
        }
        Map<Integer, String> map = courseComponent.batchGetUserPlanSchemeOption(dto.getCourseTimetableId(), workList.stream().map(WorkInfoPojo::getUserId).collect(Collectors.toList()));
        List<Integer> userIdList = map.entrySet().stream().filter(i -> Objects.equals(i.getValue(), dto.getPlanSchemeOption()))
                .map(Map.Entry::getKey).collect(Collectors.toList());
        HashSet<Integer> hashSet = new HashSet<>(userIdList);
        long workCount = workList.stream().filter(i -> hashSet.contains(i.getUserId()) && Objects.equals(i.getWorkStatus(), 1)).count();
        long listenCount = workList.stream().filter(i -> hashSet.contains(i.getUserId()) && Objects.equals(i.getListenStatus(), 1)).count();
        vo.setListenNum((int) listenCount);
        vo.setDoWorkNum((int) workCount);
        int score = workList.stream().filter(i -> hashSet.contains(i.getUserId())).mapToInt(WorkInfoPojo::getScore).sum();
        vo.setScore(score);
        return vo;
    }

    @Override
    public void flushToMongo(Integer workId) {
        WorkInfo workInfo = workInfoMapper.selectById(workId);
        workInfoPojoMongoService.saveOrUpdate(workInfo);
    }

    private List<CommonLabelVO> convertCommonLabel(WorkInfoPojo workInfo) {
        Integer goodsId = courseComponent.getSkuIdByCourseId(workInfo.getCourseId());
        GoodsInfoDetailVO goodsInfoDetailVO = courseComponent.innerGoodsInfoDetail(goodsId);
        List<CommonLabelPageVO> commonLabelPageVOS =
                commentComponent.listCommonLabelByGoodId(goodsId, new ArrayList<>(Arrays.asList(2, 3)));
        List<CommonLabelVO> commonLabelVOS = new ArrayList<>();
        for (CommonLabelPageVO commonLabelPageVO : commonLabelPageVOS) {
            CommonLabelVO commonLabelVO = new CommonLabelVO();
            BeanUtil.copyProperties(commonLabelPageVO, commonLabelVO);
            if (commonLabelPageVO.getJumpType().equals(3) && StrUtil.isNotBlank(commonLabelPageVO.getJumpLocation())) {
                GoodsInfoDetailVO goodsInfoDetail = courseComponent.innerGoodsInfoDetail(Integer.valueOf(commonLabelPageVO.getJumpLocation()));
                commonLabelVO.setCourseId(goodsInfoDetail.getDataId());
            }
            if (ObjectUtil.isNotEmpty(goodsInfoDetailVO)) {
                commonLabelVO.setLogoPic(goodsInfoDetailVO.getCourseImgUrl());
            }
            commonLabelVO.setDesc(commonLabelPageVO.getDesc());
            commonLabelVOS.add(commonLabelVO);
        }
        if (ObjectUtil.isNotEmpty(workInfo.getCompanyId()) && workInfo.getCompanyId() > 0) {
            CommonLabelVO commonLabelVO = new CommonLabelVO();
            Map<Integer, BatchGetCompanySimpleVO> batchGetCompanySimpleVOMap =
                    userComponent.batchGetCompanySimple(Collections.singletonList(workInfo.getCompanyId()));
            BatchGetCompanySimpleVO batchGetCompanySimpleVO = batchGetCompanySimpleVOMap.get(workInfo.getCompanyId());
            if (ObjectUtil.isNotEmpty(batchGetCompanySimpleVO)) {
                commonLabelVO.setLogoPic(batchGetCompanySimpleVO.getCompanyLogo());
            }
            commonLabelVO.setName(workInfo.getCompanyName());
            commonLabelVO.setJumpType(4);
            commonLabelVO.setJumpLocation(workInfo.getCompanyId().toString());
            commonLabelVOS.add(commonLabelVO);
        }
        return commonLabelVOS;
    }
    private static List<WorkReplyVO> convertReplyVOList(List<WorkTeacherReply> replyList) {
        return replyList.stream().map(reply -> {
            WorkReplyVO workReplyVO = new WorkReplyVO();
            workReplyVO.setId(reply.getId());
            workReplyVO.setContent(reply.getTeacherContent());
            workReplyVO.setReplyType(reply.getTeacherReplyType());
            workReplyVO.setSourceType(reply.getSourceType());
            workReplyVO.setUserId(reply.getTeacherUserId());
            workReplyVO.setUserName(reply.getUserName());
            workReplyVO.setAudioTimeLength(reply.getAudioTimeLength());
            workReplyVO.setAudioSize(reply.getAudioSize());
            workReplyVO.setReplyUserType(reply.getReplyUserType());
            return workReplyVO;
        }).collect(Collectors.toList());
    }

    private Map<Integer, List<WorkTeacherReply>> getCommpanyReplyMap(List<Integer> workIdList) {
        return workTeacherReplyService.list(new LambdaQueryWrapper<WorkTeacherReply>()
                        .in(WorkTeacherReply::getWorkId, workIdList)
                        .eq(WorkTeacherReply::getDeleteFlag, IsDeleteEnum.NO.getCode())
                        .eq(WorkTeacherReply::getReplyUserType, ReplyUserTypeEnums.COMPANY_USER.getCode())
                        .orderByAsc(WorkTeacherReply::getTeacherReplyTime))
                .stream().collect(Collectors.groupingBy(WorkTeacherReply::getWorkId));
    }

    private Map<Integer, SearchCommentAndLikeTotalVO> getSearchCommentAndLikeTotalVOMap(List<Integer> workIdList, HeaderUserInfo headerUserInfo) {
        SearchCommentAndLikeTotalDTO searchCommentAndLikeTotalDTO = new SearchCommentAndLikeTotalDTO();
        searchCommentAndLikeTotalDTO.setCommentType(CommentTypeEnum.EXPERIENCE.getCode());
        searchCommentAndLikeTotalDTO.setLikeType(LikeTypeEnum.HEART_LIKE.getCode());
        searchCommentAndLikeTotalDTO.setUserId(headerUserInfo.getId());
        searchCommentAndLikeTotalDTO.setDataIdList(workIdList);
        return commentComponent.searchCommentAndLikeTotal(searchCommentAndLikeTotalDTO);
    }

    private void workLevelShowFlag(AppWorkConfigDTO dto, AppWorkConfigVO vo) {
        CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(dto.getCourseId(), dto.getCourseTimeTableId());
        if (Objects.isNull(courseTimetableUnlockDetailVO)) {
            vo.setWorkLevelShowFlag(0);
            return;
        }
        if (WorkModelFlagEnum.LEVEL_WORK_TEMPLATE.getCode().equals(courseTimetableUnlockDetailVO.getWorkModelFlag())) {
            vo.setWorkLevelShowFlag(1);
        } else {
            vo.setWorkLevelShowFlag(0);
        }
    }

    public void repairAddtionalScore(RepairScoreDTO repairScoreDTO) {
        QueryWrapper<UserStudyInfo> userStudyInfoQueryWrapper = new QueryWrapper<>();
        userStudyInfoQueryWrapper
                .lambda()
                .eq(UserStudyInfo::getUserId, repairScoreDTO.getUserId())
                .eq(UserStudyInfo::getCompanyId, repairScoreDTO.getCompanyId())
                .eq(UserStudyInfo::getCourseId, repairScoreDTO.getCourseId());
        UserStudyInfo userStudyInfo = userStudyInfoMapper.selectOne(userStudyInfoQueryWrapper);
        if (Objects.isNull(userStudyInfo)) {
            userStudyInfo = UserStudyInfo.builder()
                    .courseId(repairScoreDTO.getCourseId())
                    .companyId(repairScoreDTO.getCompanyId())
                    .score(0)
                    .userId(repairScoreDTO.getUserId())
                    .build();
            userStudyInfoMapper.insert(userStudyInfo);
        }

    }

    public void repairListenScore(RepairScoreDTO dto) {
        CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(dto.getCourseId(), dto.getCourseTimetableId());
        log.info("获取课程表" + courseTimetableUnlockDetailVO);
        if (Objects.isNull(courseTimetableUnlockDetailVO)) {
            log.info("课程表信息未查询到-退出听课加分");
            throw new BusinessException("课程表信息未查询到-退出听课加分");
        }
        log.info("当前时间" + LocalDateTime.now() + "，提交作业开始时间：" + courseTimetableUnlockDetailVO.getWorkBeginTime() + "提交作业结束时间" + "在作业提交时间范围内-进行加分操作");

        UserStudyInfo userStudyInfo = this.getUserStudyInfo(dto.getUserId(), dto.getCourseId(), dto.getCompanyId());
        WorkInfo workInfo = workInfoMapper.queryOneWork(dto.getUserId(), dto.getCourseId(), dto.getCompanyId(), courseTimetableUnlockDetailVO.getId());
        Integer score = 0;
        if (Objects.nonNull(workInfo)) {
            boolean isOldFlag = getIsOldFlag(courseTimetableUnlockDetailVO.getWorkModelFlag(), dto.getUserId(), dto.getCourseId());
            workInfo.setUserFlag(isOldFlag ? 1 : 0);
            WorkUserModel workUserModel = getWorkUserModel(courseTimetableUnlockDetailVO.getWorkModelFlag(), dto.getUserId(), dto.getCourseId());
            // 如果已经有分或者已写过作业,就不能修改模板等级了
            workInfo.setLevel((Objects.equals(workInfo.getWorkStatus(), 1) || workInfo.getScore() > 0) && StrUtil.isNotBlank(workInfo.getLevel())
                    ? workInfo.getLevel() : Objects.nonNull(workUserModel) && StringUtils.isNotBlank(workUserModel.getLevel()) ? workUserModel.getLevel() : "");
            Integer workModelId = getSimpleWorkModelIdByCourseTimetableUnlockDetailVO(courseTimetableUnlockDetailVO, isOldFlag, workUserModel, workInfo);
            workInfo.setWorkModelId(workModelId);
            DoWorkSendKafkaBO doWorkSendKafkaBO = this.workSendKafkaBoBuild(workInfo.getCompanyId(), workInfo.getClassesId(), workInfo.getTeamId(), workInfo.getUserId(), workInfo.getCourseId(), courseTimetableUnlockDetailVO.getId(), false, false);
            if (workInfo.getWorkStatus().equals(1)) {
                doWorkSendKafkaBO.setWorkStatus(true);
            }
            doWorkSendKafkaBO.setTeamId(workInfo.getTeamId());
            doWorkSendKafkaBO.setClassesId(workInfo.getClassesId());
            QueryWrapper<WorkModelDetail> queryWrapper = new QueryWrapper<>();
            queryWrapper
                    .lambda()
                    .eq(WorkModelDetail::getType, 1)
                    .eq(WorkModelDetail::getWorkModelId, workModelId);
            WorkModelDetail workModelDetail = workModelDetailMapper.selectOne(queryWrapper);
            if (Objects.isNull(workModelDetail)) {
                log.info("该作业没有听课加分作业项----退出听课加分");
                return;
            }
            if (workInfo.getListenStatus().equals(1)) {
                if (!workInfo.getAutomatic().equals(1)) {
                    workInfo.setAutomatic(1);
                    workInfoMapper.saveOrUpdate(workInfo);
                    workInfoPojoMongoService.saveOrUpdate(workInfo);
                }
                return;
            }
            List<WorkModelDetailVO> workModelDetailVOS = new ArrayList<>();
            if (StringUtils.isNotBlank(workInfo.getContent())) {
                workModelDetailVOS = workContentParse(workInfo.getContent());
            } else {
                workModelDetailVOS = queryWorkModelDetailList(workModelId, dto.getCourseId(), courseTimetableUnlockDetailVO);
            }
            for (WorkModelDetailVO workInfoDetail : workModelDetailVOS) {
                if (workInfoDetail.getType().equals(WorkDetailTypeEnum.LISTEN.getCode())) {

                    if ("1".equals(workInfoDetail.getContent())) {
                        workInfo.setAutomatic(1);
                        workInfoMapper.saveOrUpdate(workInfo);
                        workInfoPojoMongoService.saveOrUpdate(workInfo);
                        log.info("作业已加过听课分----退出听课加分");
                        return;
                    } else {
                        workInfoDetail.setContent("1");
                        workInfoDetail.setScore(workModelDetail.getScore());
                        score = score + workModelDetail.getScore();
                        log.info("作业未加过听课分----进行听课加分操作");
                    }
                }
                if (workInfoDetail.getType().equals(WorkDetailTypeEnum.TEXT.getCode()) || workInfoDetail.getType().equals(DUSING.getCode())) {
                    if (Objects.nonNull(workInfoDetail.getShowStatus()) && workInfoDetail.getShowStatus().equals(YesOrNoEnum.YES.getCode())) {
                        workInfoDetail.setShowStatus(YesOrNoEnum.YES.getCode());
                        if (StringUtils.isBlank(workInfoDetail.getShowTitle())) {
                            workInfoDetail.setShowTitle(workInfoDetail.getTitle());
                        }
                    } else {
                        workInfoDetail.setShowStatus(YesOrNoEnum.NO.getCode());
                    }
                }
            }
            if (dto.getCompanyId().equals(-1)) {
                if (StringUtils.isBlank(workInfo.getAdditional())) {
                    CourseConfig courseConfig = courseComponent.courseConfigDetail(-1, dto.getCourseId());
                    if (Objects.nonNull(courseConfig) && YesOrNoEnum.YES.getCode().equals(courseConfig.getExtendStudySwitch())) {
                        AdditionalInfoVO additionalInfoVO = oldServerComponent.additionalInfo(dto.getCourseId(), courseTimetableUnlockDetailVO.getWorkBeginTime().toLocalDate());
                        if (Objects.nonNull(additionalInfoVO)) {
                            additionalInfoVO.setAdditionalScore(courseConfig.getExtendStudyScore());
                            additionalInfoVO.setStatus(1);
                            workInfo.setAdditional(JSON.toJSONString(additionalInfoVO));
                        }
                    }
                }
            }

            workInfo.setScore(workInfo.getScore() + score);
            workInfo.setContent(JSONObject.toJSONString(workModelDetailVOS));
            workInfo.setAutomatic(1);
            workInfo.setListenStatus(1);
            workInfoMapper.saveOrUpdate(workInfo);
            workInfoPojoMongoService.saveOrUpdate(workInfo);
            userStudyInfo.setScore(userStudyInfo.getScore() + score);
            userStudyInfoMapper.updateById(userStudyInfo);
            doWorkSendKafkaBO.setScore(workInfo.getScore());
            doWorkSendKafkaBO.setListenStatus(true);
            doWorkSendKafkaBO.setTag(WorkKafkaMessageEnum.LISTEN_UP.getCode());
            log.info("repairListenScore,topic:{},send kafka,doWorkSendKafkaBO:{}", workTopic, JSON.toJSONString(doWorkSendKafkaBO));
            kafkaProduction.send(workTopic, String.valueOf(doWorkSendKafkaBO.getUserId()), JSONObject.parseObject(JSONObject.toJSONString(doWorkSendKafkaBO)));
            // 如果作业项只有听课项，且没有写过作业，则自动提交作业
            if (!Objects.equals(workInfo.getWorkStatus(), 1)
                    && Objects.isNull(workModelDetailVOS.stream().filter(workInfoDetailVO -> !LISTEN.getCode().equals(workInfoDetailVO.getType())).findFirst().orElse(null))) {
                DoWorkMsgDTO doWorkMsgDTO = BeanUtil.copyProperties(dto, DoWorkMsgDTO.class);
                doWorkMsgDTO.setCourseTimeTableId(dto.getCourseTimetableId());
                doWorkMsgDTO.setWorkDetails(BeanUtil.copyToList(workModelDetailVOS, DoWorkDetailDTO.class));
                log.info("listenAddScore syncDoWork 如果作业项只有听课项，则自动提交作业");
                syncDoWork(doWorkMsgDTO);
            }
        } else {
            log.info("未写作业---进行听课加分操作");

            ClassesTeamDetailVO classesTeamInfoByUser = classesComponent.getClassesTeamInfoByUser(dto.getCompanyId(), dto.getCourseId(), dto.getUserId());
            if (Objects.isNull(classesTeamInfoByUser)) {
                classesTeamInfoByUser = ClassesTeamDetailVO.builder()
                        .classesId(0)
                        .classesNo("0")
                        .teamId(0)
                        .build();
            }
            if (Objects.isNull(classesTeamInfoByUser)) {
                throw new BusinessException("未查询到数据，可能服务异常不处理");
            }

            UserInfoCompanySearchVO userInfoCompanySearchVO = userComponent.userByUserIdCompanyId(dto.getCompanyId(), dto.getUserId());
            if (Objects.isNull(userInfoCompanySearchVO)) {
                throw new BusinessException("未查询到数据，可能服务异常不处理");
            }
            workInfo = this.workInfoBuild(courseTimetableUnlockDetailVO, dto.getUserId(), dto.getCourseId(), dto.getCompanyId());
            boolean isOldFlag = getIsOldFlag(courseTimetableUnlockDetailVO.getWorkModelFlag(), dto.getUserId(), dto.getCourseId());
            workInfo.setUserFlag(isOldFlag ? 1 : 0);
            WorkUserModel workUserModel = getWorkUserModel(courseTimetableUnlockDetailVO.getWorkModelFlag(), dto.getUserId(), dto.getCourseId());
            // 如果已经有分或者已写过作业,就不能修改模板等级了
            workInfo.setLevel((Objects.equals(workInfo.getWorkStatus(), 1) || workInfo.getScore() > 0) && StrUtil.isNotBlank(workInfo.getLevel())
                    ? workInfo.getLevel() : Objects.nonNull(workUserModel) && StringUtils.isNotBlank(workUserModel.getLevel()) ? workUserModel.getLevel() : "");
            Integer workModelId = getSimpleWorkModelIdByCourseTimetableUnlockDetailVO(courseTimetableUnlockDetailVO, isOldFlag, workUserModel, workInfo);
            workInfo.setWorkModelId(workModelId);
            List<WorkModelDetailVO> detail;
            if (StringUtils.isNotBlank(workInfo.getContent())) {
                detail = workContentParse(workInfo.getContent());
            } else {
                detail = queryWorkModelDetailList(workInfo.getWorkModelId(), dto.getCourseId(), courseTimetableUnlockDetailVO);
            }
            QueryWrapper<WorkModelDetail> queryWrapper = new QueryWrapper<>();
            queryWrapper
                    .lambda()
                    .eq(WorkModelDetail::getType, 1)
                    .eq(WorkModelDetail::getWorkModelId, workInfo.getWorkModelId());
            WorkModelDetail workModelDetail = workModelDetailMapper.selectOne(queryWrapper);
            if (Objects.isNull(workModelDetail)) {
                log.info("该作业没有听课加分作业项----退出听课加分");
                return;
            }
            log.info("作业未加过听课分----进行听课加分操作---begin");
            for (WorkModelDetailVO workModelDetailVO : detail) {
                if (workModelDetailVO.getType().equals(WorkDetailTypeEnum.LISTEN.getCode())) {
                    if (Objects.nonNull(classesTeamInfoByUser.getClassesOwnerId())) {
                        UserInfoCompanySearchVO classesTeacherUser = userComponent.userByUserIdCompanyId(dto.getCompanyId(), classesTeamInfoByUser.getClassesOwnerId());
                        if (Objects.nonNull(classesTeacherUser)) {
                            workInfo.setClassesTeacherPhone(classesTeacherUser.getPhone());
                        } else {
                            throw new BusinessException("未查询到数据，可能服务异常不处理");
                        }
                    }
                    workModelDetailVO.setContent("1");
                    workModelDetailVO.setScore(workModelDetailVO.getScore());
                    score = score + workModelDetail.getScore();
                    workInfo.setAutomatic(1);
                    workInfo.setScore(workInfo.getScore() + score);
                    userStudyInfo.setScore(userStudyInfo.getScore() + score);
                    userStudyInfoMapper.updateById(userStudyInfo);
                    log.info("作业未加过听课分----进行听课加分操作---end");
                } else {
                    if (workModelDetailVO.getType().equals(WorkDetailTypeEnum.TEXT.getCode()) || workModelDetailVO.getType().equals(DUSING.getCode())) {
                        if (Objects.nonNull(workModelDetailVO.getShowStatus()) && workModelDetailVO.getShowStatus().equals(YesOrNoEnum.YES.getCode())) {
                            workModelDetailVO.setShowStatus(YesOrNoEnum.YES.getCode());
                            if (StringUtils.isBlank(workModelDetailVO.getShowTitle())) {
                                workModelDetailVO.setShowTitle(workModelDetailVO.getTitle());
                            }
                        } else {
                            workModelDetailVO.setShowStatus(YesOrNoEnum.NO.getCode());
                        }
                    } else {
                        workModelDetailVO.setContent("0");
                    }
                    workModelDetailVO.setScore(0);
                }
            }
            DoWorkSendKafkaBO doWorkSendKafkaBO = this.workSendKafkaBoBuild(workInfo.getCompanyId(), workInfo.getClassesId(), workInfo.getTeamId(), workInfo.getUserId(), workInfo.getCourseId(), courseTimetableUnlockDetailVO.getId(), false, false);
            workInfo.setContent(JSONObject.toJSONString(detail));
            workInfo.setAutomatic(1);
            workInfo.setListenStatus(1);
            workInfoMapper.saveOrUpdate(workInfo);
            workInfoPojoMongoService.saveOrUpdate(workInfo);
            doWorkSendKafkaBO.setTeamId(workInfo.getTeamId());
            doWorkSendKafkaBO.setClassesId(workInfo.getClassesId());
            doWorkSendKafkaBO.setScore(workInfo.getScore());
            doWorkSendKafkaBO.setListenStatus(true);
            doWorkSendKafkaBO.setTag(WorkKafkaMessageEnum.LISTEN_UP.getCode());
            log.info("repairListenScore,topic:{},send kafka,doWorkSendKafkaBO:{}", workTopic, JSON.toJSONString(doWorkSendKafkaBO));
            kafkaProduction.send(workTopic, String.valueOf(doWorkSendKafkaBO.getUserId()), JSONObject.parseObject(JSONObject.toJSONString(doWorkSendKafkaBO)));
            generateRank(doWorkSendKafkaBO);
            // 如果作业项只有听课项，且没有写过作业，则自动提交作业
            if (!Objects.equals(workInfo.getWorkStatus(), 1)
                    && Objects.isNull(detail.stream().filter(workInfoDetailVO -> !LISTEN.getCode().equals(workInfoDetailVO.getType())).findFirst().orElse(null))) {
                DoWorkMsgDTO doWorkMsgDTO = BeanUtil.copyProperties(dto, DoWorkMsgDTO.class);
                doWorkMsgDTO.setCourseTimeTableId(dto.getCourseTimetableId());
                doWorkMsgDTO.setWorkDetails(BeanUtil.copyToList(detail, DoWorkDetailDTO.class));
                log.info("listenAddScore syncDoWork 如果作业项只有听课项，则自动提交作业");
                syncDoWork(doWorkMsgDTO);
            }
        }
    }

    public Integer getCurrentRank(Integer companyId, Integer courseId, String classesNo, Integer courseTimetableId) {
        ClassStudyStatistics classStudyStatistics = classStudyStatisticsMapper.selectOne(new QueryWrapper<ClassStudyStatistics>()
                .lambda()
                .eq(ClassStudyStatistics::getCompanyId, companyId)
                .eq(ClassStudyStatistics::getCourseId, courseId)
                .eq(ClassStudyStatistics::getClassesNo, classesNo)
                .eq(ClassStudyStatistics::getCourseTimetableId, courseTimetableId)
                .orderByDesc(ClassStudyStatistics::getId));
        if (Objects.isNull(classStudyStatistics)) {
            return 0;
        }
        return classStudyStatisticsMapper.getCurrentRank(classStudyStatistics);
    }

    @Override
    public ResultInfo workRemind(WorkRemindDTO dto, HeaderUserInfo headerUserInfo) {
        String redisLockKey = "workRemindLock:" + dto.getCourseId() + ":" + dto.getClassesNo();
        boolean lockSuccess = RedissonLockUtil.tryLock(redisLockKey);
        if (!lockSuccess) {
            log.info("作业提醒,加锁失败,{}", dto);
            return ResultInfo.error("请稍后再试");
        }
        log.info("作业提醒,加锁成功,{}", dto);
        try {
            String redisKey = "work:remind:" + dto.getCourseId() + ":" + dto.getClassesNo();
            Integer remindCount = (Integer) redisTemplate.boundValueOps(redisKey).get();
            log.info("当前提醒次数为:{}", remindCount);
            if (remindCount == null) {
                remindCount = 0;
            }
            checkRemindCount(dto, remindCount);
            LocalDateTime now = LocalDateTime.now();
            // 最近一次的作业配置
            CourseTimetableDetailDTO detailDTO = new CourseTimetableDetailDTO();
            detailDTO.setCourseId(dto.getCourseId());
            CourseTimetableUnlockDetailVO lastResDateVO = Optional.ofNullable(courseFeignClient.courseTimetableUnlockDetail(detailDTO))
                    .map(ResultInfo::getData)
                    .orElse(null);
            checkLastResDate(lastResDateVO, now);

            // 查询该班级下的所有学员
            UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(dto.getCourseId(), headerUserInfo.getId());
            List<InnerClassesUserInfoListVO> userInfoList = classesComponent.innerClassesUserInfoList(userRightsCourse.getCompanyId(), dto.getCourseId(), dto.getClassesNo(), null);
            if (CollectionUtils.isEmpty(userInfoList)) {
                return ResultInfo.error("班级下的学员为空");
            }
            List<Integer> userIdList = userInfoList.stream().map(InnerClassesUserInfoListVO::getUserId).collect(Collectors.toList());
            // 查询已提交作业的用户 id
            List<Integer> excludeUserIdList = queryCommitWorkUserIdList(dto, headerUserInfo, lastResDateVO, userIdList);
            userIdList.removeAll(excludeUserIdList);
            if (CollectionUtils.isEmpty(userIdList)) {
                return ResultInfo.error("学员都已提交作业");
            }
            redisTemplate.boundValueOps(redisKey).set(remindCount + 1);
            long expireMillis = Duration.between(now, lastResDateVO.getWorkEndTime()).toMillis() / 1000;
            redisTemplate.expire(redisKey, expireMillis, TimeUnit.SECONDS);
            // 获取跳转作业提醒需要的参数,然后发送 kafka 消息
            workRemindSendKafka(dto, lastResDateVO, userIdList);
        } finally {
            log.info("作业提醒,释放锁:{}", dto);
            RedissonLockUtil.unlock(redisLockKey);
        }
        return ResultInfo.ok(BaseResultCode.SUCCESS.getCode(), "作业提醒成功", null);
    }

    private void checkRemindCount(WorkRemindDTO dto, Integer remindCount) {
        if (remindCount >= 3) {
            log.info("当期作业最多可提醒3次,{}", dto);
            throw new BusinessException("当期作业最多可提醒 3 次");
        }
    }

    private void checkLastResDate(CourseTimetableUnlockDetailVO lastResDateVO, LocalDateTime now) {
        if (lastResDateVO == null) {
            throw new BusinessException("未查询到最近一次的作业");
        }
        if (now.isBefore(lastResDateVO.getWorkBeginTime())) {
            throw new BusinessException("本次作业未开始,不提醒");
        }
        if (now.isAfter(lastResDateVO.getWorkEndTime())) {
            throw new BusinessException("本次作业已结束,不提醒");
        }
    }

    @Async("sendCommentExecutor")
    public void workRemindSendKafka(WorkRemindDTO dto, CourseTimetableUnlockDetailVO lastResDateVO, List<Integer> userIdList) {
        WorkRemindDTO workRemindDTO = new WorkRemindDTO();
        workRemindDTO.setCourseId(dto.getCourseId());
        workRemindDTO.setClassesNo(dto.getClassesNo());
        log.info("workRemindDTO:{}", JSON.toJSONString(workRemindDTO));
        JumpPageWorkRemindParamVO jumpPageWorkRemindParamVO = oldServerComponent.jumpPageWorkRemindParam(workRemindDTO);
        log.info("JumpPageWorkRemindParamVO:{}", JSON.toJSONString(jumpPageWorkRemindParamVO));
        if (jumpPageWorkRemindParamVO == null) {
            log.info("根据{}未查询到班级作业等信息未查询", jumpPageWorkRemindParamVO);
            throw new BusinessException("班级作业等信息未查询到");
        }
        CommonPushMessageDTO pushMessageDTO = new CommonPushMessageDTO();
        pushMessageDTO.setClassesId(jumpPageWorkRemindParamVO.getClassesId());
        pushMessageDTO.setSkuId(jumpPageWorkRemindParamVO.getSkuId());
        pushMessageDTO.setClassName(jumpPageWorkRemindParamVO.getClassName());
        pushMessageDTO.setCourseType(jumpPageWorkRemindParamVO.getCourseType());
        pushMessageDTO.setCourseId(dto.getCourseId());
        pushMessageDTO.setUserIdList(userIdList);
        pushMessageDTO.setPushType(PushTypeConstant.STUDY_REMIND);
        pushMessageDTO.setRemindType(1);
        pushMessageDTO.setTitle("作业提醒");
        String endTimeStr = lastResDateVO.getWorkEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
        pushMessageDTO.setContent("《" + lastResDateVO.getResourceTitle() + "》作业还未提交,作业提交截止时间" + endTimeStr + "赶紧快去提交作业哦~");
        log.info("pushMessageDTO:{}", JSON.toJSONString(pushMessageDTO));
        kafkaProduction.sendCommonPushSysMsg(pushMessageDTO);
    }

    private List<Integer> queryCommitWorkUserIdList(WorkRemindDTO dto, HeaderUserInfo headerUserInfo, CourseTimetableUnlockDetailVO lastResDateVO, List<Integer> userIdList) {
        List<WorkInfoPojo> commitWorkList = workInfoPojoMongoService.querySimpleList(new WorkInfoPojoQueryDTO().setCourseId(dto.getCourseId()).setCourseTimetableId(lastResDateVO.getId())
                .setUserIdList(userIdList).setCompanyId(headerUserInfo.getCurrentCompanyId()).setWorkStatus(1));
        List<Integer> excludeUserIdList;
        if (CollectionUtils.isEmpty(commitWorkList)) {
            excludeUserIdList = new ArrayList<>();
        } else {
            excludeUserIdList = commitWorkList.stream().map(WorkInfoPojo::getUserId).collect(Collectors.toList());
        }
        return excludeUserIdList;
    }

    private AppWorkInfoListVO convertAppWorkInfoListVO(WorkInfoPojo workInfo, Map<Long, HeartReadListVO> readRecordMap, boolean showReadFlag, HeaderUserInfo headerUserInfo) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
        AppWorkInfoListVO appWorkInfoListVO = new AppWorkInfoListVO();
        appWorkInfoListVO.setId(workInfo.getWorkId());
        List<WorkModelDetailVO> workInfoDetails = JSONArray.parseArray(workInfo.getContent(), WorkModelDetailVO.class);
        List<AppWorkDetailListVO> appWorkDetailList = workInfoDetails.stream().filter(workInfoDetail -> (Objects.nonNull(workInfoDetail) && YesOrNoEnum.YES.getCode().equals(workInfoDetail.getShowStatus())))
                .filter(workInfoDetail -> {
                    if (StrUtil.isBlank(headerUserInfo.getAppOs()) || Objects.isNull(headerUserInfo.getAppVersionCode())) {
                        return true;
                    }
                    if (READ.getCode().equals(workInfoDetail.getType())
                            && ("ios".equals(headerUserInfo.getAppOs()) && headerUserInfo.getAppVersionCode() <= 212
                            || "android".equals(headerUserInfo.getAppOs()) && headerUserInfo.getAppVersionCode() <= 2812)) {
                        return false;
                    }
                    return true;
                })
                .filter(workInfoDetail -> {
                    if (READ.getCode().equals(workInfoDetail.getType())) {
                        if (showReadFlag) {
                            return Objects.nonNull(workInfoDetail.getReadRecordId()) && workInfoDetail.getReadRecordId() != 0;
                        }
                        return false;
                    } else {
                        return true;
                    }
                })
                .sorted(this::moveReadTypeToLast)
                .map(workInfoDetail -> {
                    AppWorkDetailListVO appWorkDetailListVO = BeanUtil.copyProperties(workInfoDetail, AppWorkDetailListVO.class);
                    if (Objects.nonNull(readRecordMap)) {
                        if (READ.getCode().equals(workInfoDetail.getType())) {
                            HeartReadListVO heartReadListVO = readRecordMap.get(workInfoDetail.getReadRecordId());
                            AppWorkReadHeartVO appWorkReadHeartVO = BeanUtil.copyProperties(heartReadListVO, AppWorkReadHeartVO.class);
                            appWorkDetailListVO.setReadVO(appWorkReadHeartVO);
                            appWorkDetailListVO.setContent(null);
                        }
                    }
                    return appWorkDetailListVO;
                }).collect(Collectors.toList());
        appWorkInfoListVO.setWorkContentList(appWorkDetailList);
        appWorkInfoListVO.setGroupId(workInfo.getClassesNo());
        appWorkInfoListVO.setCreateTime(workInfo.getWorkTime());
        appWorkInfoListVO.setResourceTitle(workInfo.getResourceTitle());
        appWorkInfoListVO.setClassTeacherName(workInfo.getClassesTeacherName());
        appWorkInfoListVO.setUserId(workInfo.getUserId());
        appWorkInfoListVO.setCompanyName(workInfo.getCompanyName());
        appWorkInfoListVO.setUserName(workInfo.getUserName());
        appWorkInfoListVO.setCourseTitle(workInfo.getCourseName());
        appWorkInfoListVO.setBelongDate(workInfo.getCourseBeginTime().toLocalDate());
        appWorkInfoListVO.setRecommendStatus(workInfo.getRecommendStatus());
        appWorkInfoListVO.setDoneDate(workInfo.getCourseBeginTime().format(formatter));
        appWorkInfoListVO.setScore(workInfo.getScore());
        appWorkInfoListVO.setWorkModelFlag(workInfo.getWorkModelFlag());
        appWorkInfoListVO.setUserFlag(workInfo.getUserFlag());
        appWorkInfoListVO.setCourseTimetableId(workInfo.getCourseTimetableId());
        appWorkInfoListVO.setLevel(workInfo.getLevel());
        return appWorkInfoListVO;
    }

    public Map<Integer, List<AppWorkTeacherReplyVO>> findTeacherReplyList(List<WorkTeacherReply> replyList, HeaderUserInfo headerUserInfo) {
        if (CollectionUtil.isEmpty(replyList)) {
            return new HashMap<>();
        }
        return replyList.stream().map(workTeacherReply -> {
            AppWorkTeacherReplyVO replyVO = new AppWorkTeacherReplyVO();
            BeanUtils.copyProperties(workTeacherReply, replyVO);
            replyVO.setTeacherReply(workTeacherReply.getTeacherContent());
            replyVO.setTeacherName(workTeacherReply.getUserName());
            replyVO.setPostId(workTeacherReply.getWorkId());
            replyVO.setIsMyReply(workTeacherReply.getTeacherUserId().equals(headerUserInfo.getId()));
            return replyVO;
        }).collect(Collectors.groupingBy(AppWorkTeacherReplyVO::getPostId));
    }

    public Map<Integer, List<AppWorkTeacherReplyVO>> findTeacherReplyListByWorkIds(List<Integer> workIds, Integer userId, Integer replyUserType) {
        List<WorkTeacherReply> replyList = workTeacherReplyService.list(new LambdaQueryWrapper<WorkTeacherReply>()
                .in(WorkTeacherReply::getWorkId, workIds)
                .eq(WorkTeacherReply::getDeleteFlag, IsDeleteEnum.NO.getCode())
                .eq(Objects.nonNull(replyUserType), WorkTeacherReply::getReplyUserType, replyUserType)
                .orderByAsc(WorkTeacherReply::getTeacherReplyTime));
        if (CollectionUtil.isEmpty(replyList)) {
            return new HashMap<>();
        }
        return replyList.stream().map(workTeacherReply -> {
            AppWorkTeacherReplyVO replyVO = new AppWorkTeacherReplyVO();
            BeanUtils.copyProperties(workTeacherReply, replyVO);
            replyVO.setTeacherReply(workTeacherReply.getTeacherContent());
            replyVO.setTeacherName(workTeacherReply.getUserName());
            replyVO.setPostId(workTeacherReply.getWorkId());
            replyVO.setIsMyReply(workTeacherReply.getTeacherUserId().equals(userId));
            return replyVO;
        }).collect(Collectors.groupingBy(AppWorkTeacherReplyVO::getPostId));
    }

    public List<WorkModelDetailVO> workContentParse(String content) {
        return JSONObject.parseArray(content, WorkModelDetailVO.class);
    }

    public AdditionalInfoVO additionalParse(String content) {
        return JSONObject.parseObject(content, AdditionalInfoVO.class);
    }

    public List<WorkModelDetailVO> queryWorkModelDetailList(Integer workModelId, Integer courseId, CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO) {
        // 有可能会对单独的某一个课件进行作业的设置,所以需要先从这个表查一下
        List<CourseTimetableModelDetail> courseTimetableModelDetails = courseTimetableModelDetailMapper.selectList(new LambdaQueryWrapper<CourseTimetableModelDetail>()
                .eq(CourseTimetableModelDetail::getCourseTimetableId, courseTimetableUnlockDetailVO.getId())
                .eq(CourseTimetableModelDetail::getCourseId, courseId)
                // 非统一作业模板的，才需要根据模板 id 判断
                .eq(!WorkModelFlagEnum.UNIFY.getCode().equals(courseTimetableUnlockDetailVO.getWorkModelFlag()), CourseTimetableModelDetail::getWorkModelId, workModelId)
                .orderByAsc(CourseTimetableModelDetail::getSorted));
        if (CollUtil.isNotEmpty(courseTimetableModelDetails)) {
            return courseTimetableModelDetails.stream()
                    .filter(courseTimetableModelDetail -> {
                        if (READ.getCode().equals(courseTimetableModelDetail.getType())) {
                            return StrUtil.isNotBlank(courseTimetableModelDetail.getEbookJson());
                        }
                        return true;
                    })
                    .map(courseTimetableModelDetail -> {
                        WorkModelDetailVO workModelDetailVO = BeanUtil.copyProperties(courseTimetableModelDetail, WorkModelDetailVO.class);
                        if (DYNAMIC_MULTIPLE_CHOICE.getCode().equals(courseTimetableModelDetail.getType())) {
                            List<AppOptionVO> appOptionVOList = JSON.parseArray(courseTimetableModelDetail.getOptionJson(), String.class)
                                    .stream().map(option -> {
                                        AppOptionVO appOptionVO = new AppOptionVO();
                                        appOptionVO.setChecked(0);
                                        appOptionVO.setOptionContent(option);
                                        return appOptionVO;
                                    }).collect(Collectors.toList());
                            workModelDetailVO.setAppOptionList(appOptionVOList);
                            workModelDetailVO.setShowStyle(courseTimetableModelDetail.getShowStyle());
                        } else if (DYNAMIC_SINGLE_CHOICE.getCode().equals(courseTimetableModelDetail.getType())) {
                            List<AppOptionVO> appOptionVOList = JSON.parseArray(courseTimetableModelDetail.getOptionJson(), String.class)
                                    .stream().map(option -> {
                                        AppOptionVO appOptionVO = new AppOptionVO();
                                        appOptionVO.setChecked(0);
                                        appOptionVO.setOptionContent(option);
                                        return appOptionVO;
                                    }).collect(Collectors.toList());
                            workModelDetailVO.setAppOptionList(appOptionVOList);
                        } else if (READ.getCode().equals(courseTimetableModelDetail.getType())) {
                            AppEbookVO appEbookVO = JSON.parseObject(courseTimetableModelDetail.getEbookJson(), AppEbookVO.class);
                            workModelDetailVO.setEbookId(appEbookVO.getEbookId());
                            workModelDetailVO.setEbookName(appEbookVO.getEbookName());
                            workModelDetailVO.setChapterList(appEbookVO.getChapterList());
                        }
                        return workModelDetailVO;
                    })
                    .collect(Collectors.toList());
        }

        QueryWrapper<WorkModelDetail> workModelDetailQueryWrapper = new QueryWrapper<>();
        workModelDetailQueryWrapper
                .lambda()
                .eq(WorkModelDetail::getWorkModelId, workModelId)
                // 在这个表里，1 是未删除
                .eq(WorkModelDetail::getDeleted, 1)
                .orderByAsc(WorkModelDetail::getSorted);
        return workModelDetailMapper.selectList(workModelDetailQueryWrapper).stream()
                .filter(courseTimetableModelDetail -> {
                    if (READ.getCode().equals(courseTimetableModelDetail.getType())) {
                        return StrUtil.isNotBlank(courseTimetableModelDetail.getEbookJson());
                    }
                    return true;
                })
                .map(workModelDetail -> {
                    WorkModelDetailVO workModelDetailVO = BeanUtil.copyProperties(workModelDetail, WorkModelDetailVO.class);
                    if (WorkDetailTypeEnum.DYNAMIC_MULTIPLE_CHOICE.getCode().equals(workModelDetail.getType())) {
                        List<AppOptionVO> appOptionVOList = JSON.parseArray(workModelDetail.getOptionJson(), String.class)
                                .stream().map(option -> {
                                    AppOptionVO appOptionVO = new AppOptionVO();
                                    appOptionVO.setChecked(0);
                                    appOptionVO.setOptionContent(option);
                                    return appOptionVO;
                                }).collect(Collectors.toList());
                        workModelDetailVO.setAppOptionList(appOptionVOList);
                        workModelDetailVO.setShowStyle(workModelDetail.getShowStyle());
                    } else if (DYNAMIC_SINGLE_CHOICE.getCode().equals(workModelDetail.getType())) {
                        List<AppOptionVO> appOptionVOList = JSON.parseArray(workModelDetail.getOptionJson(), String.class)
                                .stream().map(option -> {
                                    AppOptionVO appOptionVO = new AppOptionVO();
                                    appOptionVO.setChecked(0);
                                    appOptionVO.setOptionContent(option);
                                    return appOptionVO;
                                }).collect(Collectors.toList());
                        workModelDetailVO.setAppOptionList(appOptionVOList);
                    } else if (READ.getCode().equals(workModelDetail.getType())) {
                        AppEbookVO appEbookVO = JSON.parseObject(workModelDetail.getEbookJson(), AppEbookVO.class);
                        workModelDetailVO.setEbookId(appEbookVO.getEbookId());
                        workModelDetailVO.setEbookName(appEbookVO.getEbookName());
                        workModelDetailVO.setChapterList(appEbookVO.getChapterList());
                    }
                    return workModelDetailVO;
                })
                .collect(Collectors.toList());
    }

    private List<LazyAppWorkInfoVO> lazy(List<CommonWorkVO> list) {
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(commonWorkVO -> {
            LazyAppWorkInfoVO lazy = BeanUtil.copyProperties(commonWorkVO, LazyAppWorkInfoVO.class);
            lazy.setPostState(commonWorkVO.getLikeFlag());
            lazy.setWorkContentList(commonWorkVO.getContentList());
            lazy.setGroupId(commonWorkVO.getClassesNo());
            lazy.setCreateTime(commonWorkVO.getWorkTime());
            lazy.setCommentList(commonWorkVO.getCommentList());
            lazy.setCourseId(commonWorkVO.getCourseId());
            lazy.setCourseTitle(commonWorkVO.getCourseName());
            lazy.setGoodsId(commonWorkVO.getGoodsId());
            lazy.setCommentTotal(commonWorkVO.getCommentTotal());
            lazy.setLikeTotal(commonWorkVO.getLikeTotal());
            if (CollUtil.isNotEmpty(commonWorkVO.getReplyList())) {
                List<AppWorkTeacherReplyVO> teacherReplyList = commonWorkVO.getReplyList()
                        .stream().map(workReplyVO -> {
                            AppWorkTeacherReplyVO replyVO = new AppWorkTeacherReplyVO();
                            replyVO.setId(workReplyVO.getId());
                            replyVO.setTeacherReply(workReplyVO.getContent());
                            replyVO.setTeacherReplyType(workReplyVO.getReplyType());
                            replyVO.setSourceType(workReplyVO.getSourceType());
                            replyVO.setTeacherUserId(workReplyVO.getUserId());
                            replyVO.setTeacherName(workReplyVO.getUserName());
                            replyVO.setAudioTimeLength(workReplyVO.getAudioTimeLength());
                            replyVO.setAudioSize(workReplyVO.getAudioSize());
                            replyVO.setReplyUserType(workReplyVO.getReplyUserType());
                            return replyVO;
                        }).collect(Collectors.toList());
                lazy.setTeacherReplyList(teacherReplyList);
            }
            return lazy;
        }).collect(Collectors.toList());
    }

    private List<MakeUpWorkListVO> makeUpWorkListOldV(Integer courseId, HeaderUserInfo headerUserInfo) {
        long l1 = System.currentTimeMillis();
        UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(courseId, headerUserInfo.getId());
        headerUserInfo.setCurrentCompanyId(userRightsCourse.getCompanyId());
        CourseInfo courseInfo = courseComponent.courseInfoById(courseId);
        if (Objects.isNull(courseInfo)) {
            return new ArrayList<>();
        }
        List<MakeUpWorkListVO> makeUpWorkListVOS = new ArrayList<>();
        long l2 = System.currentTimeMillis();
        List<CourseTimetable> makeUpWorkCourseTimetableList = courseComponent.getMakeUpWorkCourseTimetableList(courseId);
        if (CollUtil.isEmpty(makeUpWorkCourseTimetableList)) {
            return new ArrayList<>();
        }
        long l3 = System.currentTimeMillis();
        List<WorkInfoPojo> workInfoPojoList = workInfoPojoMongoService.querySimpleList(new WorkInfoPojoQueryDTO().setCourseId(courseId)
                .setUserId(headerUserInfo.getId()).setCompanyId(headerUserInfo.getCurrentCompanyId())
                .setCourseTimetableIdList(makeUpWorkCourseTimetableList.stream().map(CourseTimetable::getId).collect(Collectors.toList())));
        Map<Integer, WorkInfoPojo> workInfoMap = workInfoPojoList.stream().collect(Collectors.toMap(WorkInfoPojo::getCourseTimetableId, workInfo -> workInfo));
        long l4 = System.currentTimeMillis();
        log.info("makeUpWorkList 耗时统计:l2 - l1:{}, l3 - l2:{}, l4 - l3:{}", l2 - l1, l3 - l2, l4 - l3);
        makeUpWorkCourseTimetableList.forEach(makeUpWorkCourseTimetable -> {
            WorkInfoPojo workInfo = workInfoMap.get(makeUpWorkCourseTimetable.getId());
            if (Objects.nonNull(workInfo) && YesOrNoEnum.YES.getCode().equals(workInfo.getWorkStatus())) {
                return;
            }
            makeUpWorkListVOS.add(MakeUpWorkListVO.builder()
                    .courseId(courseId)
                    .belongDate(Objects.nonNull(makeUpWorkCourseTimetable.getWorkBeginTime()) ? makeUpWorkCourseTimetable.getWorkBeginTime().toLocalDate().format(DateTimeFormatter.ofPattern("MM月dd日")) : "")
                    .courseTimeTableId(makeUpWorkCourseTimetable.getId())
                    .workModelId(courseInfo.getWorkModelId())
                    .workScore(Objects.nonNull(workInfo) ? workInfo.getScore() : 0)
                    .workStatus(Objects.nonNull(workInfo) && workInfo.getWorkStatus().equals(YesOrNoEnum.YES.getCode()))
                    .listenStatus(Objects.nonNull(workInfo) && workInfo.getListenStatus().equals(YesOrNoEnum.YES.getCode()))
                    .build());
        });

        List<MakeUpWorkListVO> makeUpWorkListSort = makeUpWorkListVOS.stream()
                .sorted(Comparator.comparing(MakeUpWorkListVO::getCourseTimeTableId).reversed())
                .collect(Collectors.toList());
        return makeUpWorkListSort;
    }

    private List<MakeUpWorkListVO> makeUpWorkListNewV(Integer courseId, HeaderUserInfo headerUserInfo) {
        long l1 = System.currentTimeMillis();
        UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(courseId, headerUserInfo.getId());
        headerUserInfo.setCurrentCompanyId(userRightsCourse.getCompanyId());
        CourseInfo courseInfo = courseComponent.courseInfoById(courseId);
        if (Objects.isNull(courseInfo)) {
            return new ArrayList<>();
        }
        List<MakeUpWorkListVO> makeUpWorkListVOS = new ArrayList<>();
        long l2 = System.currentTimeMillis();
        List<GetMakeUpWorkCttListVO> makeUpWorkCourseTimetableList = courseComponent.getMakeUpWorkCttList(courseId, headerUserInfo.getId());
        if (CollUtil.isEmpty(makeUpWorkCourseTimetableList)) {
            return new ArrayList<>();
        }
        long l3 = System.currentTimeMillis();
        List<Integer> cttIdList = makeUpWorkCourseTimetableList.stream().map(GetMakeUpWorkCttListVO::getId).collect(Collectors.toList());
        List<WorkInfoPojo> workInfoPojoList = workInfoPojoMongoService.querySimpleList(new WorkInfoPojoQueryDTO().setCourseId(courseId)
                .setUserId(headerUserInfo.getId()).setCompanyId(headerUserInfo.getCurrentCompanyId())
                .setCourseTimetableIdList(cttIdList));
        Map<Integer, WorkInfoPojo> workInfoMap = workInfoPojoList.stream().collect(Collectors.toMap(WorkInfoPojo::getCourseTimetableId, workInfo -> workInfo));
        long l4 = System.currentTimeMillis();
        log.info("makeUpWorkList 耗时统计:l2 - l1:{}, l3 - l2:{}, l4 - l3:{}", l2 - l1, l3 - l2, l4 - l3);
        makeUpWorkCourseTimetableList.forEach(makeUpWorkCourseTimetable -> {
            WorkInfoPojo workInfo = workInfoMap.get(makeUpWorkCourseTimetable.getId());
            if (Objects.nonNull(workInfo) && YesOrNoEnum.YES.getCode().equals(workInfo.getWorkStatus())) {
                return;
            }
            makeUpWorkListVOS.add(MakeUpWorkListVO.builder()
                    .courseId(courseId)
                    .belongDate(Objects.nonNull(makeUpWorkCourseTimetable.getWorkBeginTime()) ? makeUpWorkCourseTimetable.getWorkBeginTime().toLocalDate().format(DateTimeFormatter.ofPattern("MM月dd日")) : "")
                    .courseTimeTableId(makeUpWorkCourseTimetable.getId())
                    .courseTimeTableName(makeUpWorkCourseTimetable.getResourceTitle()).courseBeginTime(makeUpWorkCourseTimetable.getWorkBeginTime())
                    .makeUpFlag(makeUpWorkCourseTimetable.getMakeUpFlag())
                    .workModelId(courseInfo.getWorkModelId())
                    .workScore(Objects.nonNull(workInfo) ? workInfo.getScore() : 0)
                    .workStatus(Objects.nonNull(workInfo) && YesOrNoEnum.YES.getCode().equals(workInfo.getWorkStatus()))
                    .listenStatus(Objects.nonNull(workInfo) && YesOrNoEnum.YES.getCode().equals(workInfo.getListenStatus()))
                    .build());
        });
        return makeUpWorkListVOS;
    }

    private void doneAction(DoWorkSendKafkaBO bo){
        CourseTimetableInfoDTO courseTimetableInfoDTO = new CourseTimetableInfoDTO();
        courseTimetableInfoDTO.setCourseId(bo.getCourseId());
        courseTimetableInfoDTO.setCourseTimetableId(bo.getCourseTimeTableId());
        CourseTimetableInfoVO model = courseComponent.courseTimetableInfo(courseTimetableInfoDTO);
        log.info("listenAddScore 获取课件信息model:{}", JSON.toJSONString(model));
        UserDoneActionInfoDTO userDoneActionInfoDTO = new UserDoneActionInfoDTO();
        userDoneActionInfoDTO.setUserId(bo.getUserId());
        userDoneActionInfoDTO.setCourseId(bo.getCourseId());
        userDoneActionInfoDTO.setDataId(bo.getCourseTimeTableId());
        userDoneActionInfoDTO.setDataType(1);
        userDoneActionInfoDTO.setDataName(model.getResourceTitle());
        userDoneActionInfoDTO.setDataNotes("");
        userDoneActionInfoDTO.setDataDescribe(model.getResourceSubtitle());
        userDoneActionInfoDTO.setBelongDate(model.getCourseBeginTime() != null ? model.getCourseBeginTime().toLocalDate() : null);
        userDoneActionInfoDTO.setNormalCourseFlag(model.getNormalCourseFlag());
        bidataComponent.addUserDoneAction(userDoneActionInfoDTO);

    }
}
