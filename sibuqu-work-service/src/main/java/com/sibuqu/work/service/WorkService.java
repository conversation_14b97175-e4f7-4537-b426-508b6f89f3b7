package com.sibuqu.work.service;

import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.base.common.result.HeaderUserInfo;
import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.work.bo.ClassesMemberDynamicKafkaBO;
import com.sibuqu.work.bo.DoWorkSendKafkaBO;
import com.sibuqu.work.dto.*;
import com.sibuqu.work.vo.AppWorkConfigVO;
import com.sibuqu.work.vo.admin.WorkInfoVO;
import com.sibuqu.work.vo.admin.WorkListVO;
import com.sibuqu.work.vo.api.*;
import com.sibuqu.work.webclient.model.vo.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

public interface WorkService {
    WorkCardInfoVO workCardInfo(WorkCardInfoDTO workCardInfoDTO, HeaderUserInfo headerUserInfo);

    void doWork(DoWorkDTO doWorkDTO,HeaderUserInfo headerUserInfo);

    PageInfoBT<MyWorkVO> myWork(MyWorkDTO myWorkDto,HeaderUserInfo headerUserInfo);

    MyScoreVO myScore(MyScoreDTO myScoreDTO,HeaderUserInfo headerUserInfo);

    ClassStatisticsVO classStatistics(ClassStatisticsDTO classStatisticsDTO,HeaderUserInfo headerUserInfo);

    List<WorkingByDateVO> classWorkingByDate(WorkingByDateDTO workingByDateDTO,HeaderUserInfo headerUserInfo);

    List<TeamStatisticsVO> teamStatistics(TeamStatisticsDTO teamStatisticsDTO, HeaderUserInfo headerUserInfo);

    UserStudyStatisticsVO userStudyStatistics(UserStudyStatisticsDTO userStudyStatisticsDTO,HeaderUserInfo headerUserInfo) throws ExecutionException, InterruptedException;

    WorkStatusInfoVO workStatusInfo(WorkStatusInfoDTO workStatusInfoDTO);

    PageInfoBT<WorkListVO> workList(UserWorkListDTO userWorkListDTO);

    WorkInfoVO workInfo(Integer id);

    List<WorkListVO> workListExport(UserWorkListDTO userWorkListDTO);

    Boolean listenAddScore(DoWorkSendKafkaBO doWorkSendKafkaBo);

    LearningSituationVO learningSituation(HeaderUserInfo headerUserInfo);

    void recommendAndCancelWork(Integer id,HeaderUserInfo headerUserInfo);

    WorkCardInfoVO todayWork(WorkCardInfoDTO workCardInfoDTO, HeaderUserInfo headerUserInfo);

    /**
     * @param heartPerceptionDTO,headerUserInfo
     * @Return List<AppWorkInfoListVO>
     * <AUTHOR>
     * @Date 2022年5月17日17:08:33
     * @Description App心得列表
     */
    PageInfoBT<AppWorkInfoListVO> heartPerceptionList(AppWorkHeartPerceptionDTO heartPerceptionDTO, HeaderUserInfo headerUserInfo);

    PageInfoBT<WorkCardInfoVO> previousWorkList(PreviousWorkDTO previousWorkDTO, HeaderUserInfo headerUserInfo);

    /**
     * @param replySearchDTO,headerUserInfo
     * @Return List<AppWorkInfoListVO>
     * <AUTHOR>
     * @Date 2022年5月17日17:08:33
     * @Description App老师回复心得列表
     */
    PageInfoBT<AppWorkInfoListVO> teacherReplyHeartPerceptionList(AppWorkReplySearchDTO replySearchDTO, HeaderUserInfo headerUserInfo);

    /**
     * @param replyDTO,headerUserInfo
     * @Return List<AppWorkInfoListVO>
     * <AUTHOR>
     * @Date 2022年5月17日17:08:33
     * @Description App老师回复
     */
    List<AppWorkTeacherReplyVO> teacherReply(AppWorkReplyDTO replyDTO, HeaderUserInfo headerUserInfo);

    /**
     * @param delDTO,headerUserInfo
     * @Return List<AppWorkInfoListVO>
     * <AUTHOR>
     * @Date 2022年5月17日17:08:33
     * @Description 老师删除作业回复
     */
    void teacherDeleteReply(AppWorkReplyDelDTO delDTO, HeaderUserInfo headerUserInfo);

    AdditionalInfoVO additionalInfo(Integer courseId);

    Boolean addAdditionalScore(Integer courseId,Integer courseTimeTableId,HeaderUserInfo headerUserInfo);

    /**
     * @param recommendSearchDTO,headerUserInfo
     * @Return List<AppWorkInfoListVO>
     * <AUTHOR>
     * @Date 2022年5月17日17:08:33
     * @Description App推荐作业列表
     */
    PageInfoBT<AppWorkInfoListVO> recommendWorkList(AppWorkRecommendSearchDTO recommendSearchDTO, HeaderUserInfo headerUserInfo);

    ResultInfo workRemind(WorkRemindDTO dto, HeaderUserInfo headerUserInfo);

    ClassGroupRoleVO recommendWork(Integer workId, HeaderUserInfo headerUserInfo);

    ClassHworkStatisticsVO classStatisticsByPerson(ClassStatisticsByPersonDTO classStatisticsDTO, HeaderUserInfo headerUserInfo);

    PageInfoBT<ToDayWorkVO> todayWorking(WorkingByDateDTO workingByDateDTO, HeaderUserInfo headerUserInfo);

    PageInfoBT<HistoryWorkingVO> historyWorking(HistoryWorkingDTO teamStatisticsDTO, HeaderUserInfo headerUserInfo);

    List<MyWorkDateVO> memberWorking(MyWorkDTO myWorkDTO, HeaderUserInfo headerUserInfo);

    ClassHworkStatisticsVO getWorkGroupStatistics(ClassStatisticsDTO classStatisticsDTO, HeaderUserInfo headerUserInfo);

    List<ClassRankVO> classRanking(Integer courseId, Integer type, HeaderUserInfo headerUserInfo);

    List<ClassMemberRankVO> classMemberRanking(Integer courseId,HeaderUserInfo headerUserInfo);

    GetRecommendVO getRecommendNum(GetRecommendNumDTO dto);

    void oldWorkSync();


    UserStudyVO getUserStudy(UserStudyDTO userStudyDTO);

    void repairScore(RepairScoreDTO repairScoreDTO);

    Boolean classesMemberDynamic(ClassesMemberDynamicKafkaBO classesMemberDynamicKafkaBO);

    Boolean shareWork(Integer courseId, Integer courseTimeTableId,Integer id, HeaderUserInfo headerUserInfo);

    Boolean generateRank(DoWorkSendKafkaBO doWorkSendKafkaBo);

    void syncShareWork(DoWorkMsgDTO doWorkMsgDTO);

    void syncAddAdditionalScore(DoWorkMsgDTO doWorkMsgDTO);

    void syncDoWork(DoWorkMsgDTO doWorkDTO);

    Map<Integer, WorkStatusInfoVO> batchGetWorkStatusInfo(WorkStatusInfoBatchDTO batchDTO);

    void workHide(Integer workId);

    List<MakeUpWorkListVO> makeUpWorkList(Integer courseId, HeaderUserInfo headerUserInfo, HttpServletRequest request);

    PageInfoBT<AppCompanyWorkInfoListVO> companyWorkList(AppWorkHeartPerceptionDTO heartPerceptionDTO, HeaderUserInfo headerUserInfo);

    PageInfoBT<WorkingByDateVO> classWorkingByDatePage(WorkingByDateDTO workingByDateDTO, HeaderUserInfo headerUserInfo);

    PageInfoBT<AppWorkInfoListVO> companyPrefecture(CompanyPrefectureDTO companyPrefectureDTO, HeaderUserInfo headerUserInfo);

    PageInfoBT<AppWorkInfoListVO> companyRecommendWorkList(AppWorkRecommendSearchDTO recommendSearchDTO, HeaderUserInfo headerUserInfo);

    PageInfoBT<AppWorkInfoListVO> appCompanyWorkList(AppWorkHeartPerceptionDTO heartPerceptionDTO, HeaderUserInfo headerUserInfo);

    ClassStatisticsVO classStatisticsByTime(ClassStatisticsDTO classStatisticsDTO);

    AppWorkConfigVO appWorkConfig(AppWorkConfigDTO dto);

    PageInfoBT<LazyAppWorkInfoVO> companyWorkPage(CompanyWorkPageDTO dto);

    PageInfoBT<LazyAppWorkInfoVO> workReplyPage(WorkReplyPageDTO dto);

    PageInfoBT<LazyAppWorkInfoVO> workRecommendPage(WorkRecommendPageDTO dto);

    ClassWorkDataVO classWorkData(ClassWorkDataDTO dto);

    ClassWorkRankVO classWorkRank(ClassWorkRankDTO dto);

    PersonalWorkDataVO personalWorkData(PersonalWorkDataDTO dto);

    PersonalWorkRankVO personalWorkRank(PersonalWorkRankDTO dto);

    CompanyRankVO companyRank(CompanyRankDTO dto);

    WorkByIdVO workById(IdDTO dto);

    void readAddScore(ReadAddScoreDTO dto);

    OneWorkVO oneWork(OneWorkDTO dto);

    CourseWorkPlanSchemeOptionVO courseWorkPlanSchemeOption(CourseWorkPlanSchemeOptionDTO dto);

    void flushToMongo(Integer workId);
}
