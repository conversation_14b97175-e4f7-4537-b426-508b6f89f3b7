package com.sibuqu.work.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sibuqu.work.entity.WeekGoal;
import com.sibuqu.work.mapper.WeekGoalMapper;
import com.sibuqu.work.service.WeekGoalService;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@Service
public class WeekGoalServiceImpl extends ServiceImpl<WeekGoalMapper, WeekGoal> implements WeekGoalService {

    @Override
    public WeekGoal getOneByDate(Integer courseId, LocalDate now) {
        return getOne(new LambdaQueryWrapper<WeekGoal>()
                .eq(WeekGoal::getCourseId, courseId)
                .le(WeekGoal::getWeekBeginDate, now)
                .ge(WeekGoal::getWeekEndDate, now)
                .eq(WeekGoal::getUpStatus, 1)
                .last("limit 1"));
    }

    @Override
    public List<WeekGoal> listByBeginDateEndDate(Integer courseId, LocalDate beginDate, LocalDate endDate) {
        return list(new LambdaQueryWrapper<WeekGoal>()
                .eq(WeekGoal::getCourseId, courseId)
                .not(wrapper -> {
                    wrapper.lt(WeekGoal::getWeekEndDate, beginDate)
                            .or()
                            .gt(WeekGoal::getWeekBeginDate, endDate);
                })
                .eq(WeekGoal::getUpStatus, 1)
        );
    }
}
