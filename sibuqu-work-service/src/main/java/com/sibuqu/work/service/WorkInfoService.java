package com.sibuqu.work.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.base.common.result.HeaderUserInfo;
import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.work.dto.CheckOldStudentCountDTO;
import com.sibuqu.work.dto.CheckOldStudentDTO;
import com.sibuqu.work.dto.CurWeekWorkDTO;
import com.sibuqu.work.dto.HistoryWeekWorkDTO;
import com.sibuqu.work.dto.RecordThinkDTO;
import com.sibuqu.work.dto.WorkFlexibleItemSearchDTO;
import com.sibuqu.work.dto.WorkInfoListDTO;
import com.sibuqu.work.dto.WorkInfoRecommendTeacherDTO;
import com.sibuqu.work.dto.sennuo.OpenClessesWorkDTO;
import com.sibuqu.work.entity.WorkInfo;
import com.sibuqu.work.mongo.WorkInfoPojo;
import com.sibuqu.work.vo.CurWeekWorkVO;
import com.sibuqu.work.vo.HistoryWeekWorkItemVO;
import com.sibuqu.work.vo.HistoryWeekWorkVO;
import com.sibuqu.work.vo.admin.WorkFlexibleItemListVO;
import com.sibuqu.work.vo.admin.WorkInfoDetailVO;
import com.sibuqu.work.vo.admin.WorkInfoListVO;
import com.sibuqu.work.vo.api.OpenCompanyWorkVO;
import com.sibuqu.work.vo.api.sennuo.OpenClassesWorkVO;

import java.time.LocalDate;
import java.util.List;

/**
 * 作业信息
 * <AUTHOR>
 */
public interface WorkInfoService extends IService<WorkInfo> {


    /**
     * 后台管理系统获取作业列表
     * @param dto WorkInfoListDTO
     * @param isExport  是否导出
     * @return 作业列表
     * <AUTHOR>
     * @date 2022年5月19日10:08:33
     * @description 后台管理 作业列表
     */
    PageInfoBT<WorkInfoListVO> getWorkInfoList(WorkInfoListDTO dto,Boolean isExport);

    /**
     * 根据主键获取作业详情信息
     * @param id
     * @return
     * <AUTHOR>
     * @Description 后台管理 作业列表中的详情
     */
    WorkInfoDetailVO workInfoDetail(Integer id );
    /**
     * 推荐给老师或者取消推荐
     * @param dto 推荐给老师的实体类
     * @param operatorUserId  操作人
     * <AUTHOR>
     * @return
     * @Description 后台管理 作业列表中的详情
     */
    Boolean recommendToTeacher(WorkInfoRecommendTeacherDTO dto,Integer operatorUserId);

    /**
     * 课程下的作业动态文本类型项 目前最多5列
     * @param dto
     * @return
     */
    WorkFlexibleItemListVO workFlexibleColumnNameList(WorkFlexibleItemSearchDTO dto );

    void batchAsyncWork(Integer courseId, Integer contentType);

    boolean checkOldStudent(CheckOldStudentDTO dto);

    List<Integer> checkOldStudentCount(CheckOldStudentCountDTO dto);

    ResultInfo<List<OpenCompanyWorkVO>> openCompanyWorkList(LocalDate startDate, LocalDate endDate);

    WorkInfo getLastWork(Integer userId, Integer courseId, Integer companyId);

    WorkInfoPojo todayWorkInfo(Integer courseId, HeaderUserInfo headerUserInfo);

    int workMySQLtoMongo(List<Integer> courseIdList);

    String replacePhone();

    PageInfoBT<OpenClassesWorkVO> getOpenClassesWork(OpenClessesWorkDTO openClessesWorkDTO );

    CurWeekWorkVO curWeekWork(CurWeekWorkDTO dto);

    Integer recordThink(RecordThinkDTO dto);

    PageInfoBT<HistoryWeekWorkItemVO> historyWeekWork(HistoryWeekWorkDTO dto);

    Integer updateThink(RecordThinkDTO dto);
}
