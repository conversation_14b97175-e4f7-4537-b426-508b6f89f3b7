package com.sibuqu.work.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sibuqu.work.entity.WeekGoal;

import java.time.LocalDate;
import java.util.List;

public interface WeekGoalService extends IService<WeekGoal> {


    /**
     * 根据日期查询周目标
     */
    WeekGoal getOneByDate(Integer courseId, LocalDate now);

    /**
     * 介于beginDate和endDate之间的周目标
     */
    List<WeekGoal> listByBeginDateEndDate(Integer courseId, LocalDate beginDate, LocalDate endDate);
}
