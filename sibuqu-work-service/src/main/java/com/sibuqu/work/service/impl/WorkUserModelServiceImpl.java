package com.sibuqu.work.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sibuqu.base.common.result.HeaderUserInfo;
import com.sibuqu.base.common.utils.FieldUtil;
import com.sibuqu.course.vo.courseinfo.CourseLevelWorkModelVO;
import com.sibuqu.work.componet.CourseComponent;
import com.sibuqu.work.dto.AddWorkUserModelDTO;
import com.sibuqu.work.dto.QueryWorkUserModelDTO;
import com.sibuqu.work.entity.WorkUserModel;
import com.sibuqu.work.mapper.WorkUserModelMapper;
import com.sibuqu.work.mongo.WorkInfoPojo;
import com.sibuqu.work.service.WorkInfoService;
import com.sibuqu.work.service.WorkUserModelService;
import com.sibuqu.work.util.HeaderUserInfoUtil;
import com.sibuqu.work.vo.QueryWorkUserModelListVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class WorkUserModelServiceImpl extends ServiceImpl<WorkUserModelMapper, WorkUserModel> implements WorkUserModelService {

    @Autowired
    private CourseComponent courseComponent;
    @Autowired
    private WorkInfoService workInfoService;

    @Override
    public String addWorkUserModel(AddWorkUserModelDTO dto) {
        String toast = "";
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        // 如果今天作业有加过分就不能更改作业模板了
        WorkInfoPojo workInfo = workInfoService.todayWorkInfo(dto.getCourseId(), headerUserInfo);
        if (Objects.nonNull(workInfo) && (Objects.equals(workInfo.getWorkStatus(), 1) || workInfo.getScore() > 0)) {
            toast = "作业设置成功，下次写作业才可生效。";
        }
        WorkUserModel workUserModel = baseMapper.getByUserIdAndCourseId(headerUserInfo.getId(), dto.getCourseId());
        if (Objects.isNull(workUserModel)) {
            workUserModel = BeanUtil.copyProperties(dto, WorkUserModel.class);
            workUserModel.setUserId(headerUserInfo.getId());
            FieldUtil.setCreateAndUpdate(workUserModel, headerUserInfo, LocalDateTime.now());
            save(workUserModel);
        } else {
            BeanUtil.copyProperties(dto, workUserModel);
            FieldUtil.setUpdate(workUserModel, headerUserInfo, LocalDateTime.now());
            updateById(workUserModel);
        }
        return toast;
    }

    @Override
    public WorkUserModel getByUserIdAndCourseId(Integer userId, Integer courseId) {
        return baseMapper.getByUserIdAndCourseId(userId, courseId);
    }

    @Override
    public List<WorkUserModel> getByUserIdListAndCourseId(List<Integer> userIdList, Integer courseId) {
        return baseMapper.getByUserIdListAndCourseId(userIdList, courseId);
    }

    @Override
    public List<QueryWorkUserModelListVO> queryWorkUserModel(QueryWorkUserModelDTO dto) {
        List<CourseLevelWorkModelVO> list = courseComponent.courseLevelWorkModel(dto.getCourseId());
        if (CollUtil.isEmpty(list)) return new ArrayList<>();
        List<QueryWorkUserModelListVO> voList = BeanUtil.copyToList(list, QueryWorkUserModelListVO.class);
        WorkUserModel workUserModel = baseMapper.getByUserIdAndCourseId(HeaderUserInfoUtil.get().getId(), dto.getCourseId());
        // 如果没有数据,默认为 a
        if (Objects.isNull(workUserModel)) {
            workUserModel = new WorkUserModel();
            workUserModel.setLevel("a");
        }
        for (QueryWorkUserModelListVO vo : voList) {
            if (workUserModel.getLevel().equals(vo.getLevel())) {
                vo.setSelectFlag(1);
            }
            if ("a".equals(vo.getLevel())) {
                vo.setTitle("A级");
                vo.setDesc("适合新学员，轻度学习，零打扰。只听课即可");
            } else if ("b".equals(vo.getLevel())) {
                vo.setTitle("B级");
                vo.setDesc("适合小组共学，知行并进，更稳健成长。听课与践行结合");
            } else if ("c".equals(vo.getLevel())) {
                vo.setTitle("C级");
                vo.setDesc("适合学有余力的同学，一门深入。重点是机制学习");
            }
        }
        return voList;
    }


}
