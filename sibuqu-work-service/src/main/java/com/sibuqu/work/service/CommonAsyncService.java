package com.sibuqu.work.service;

import cn.hutool.core.util.ObjectUtil;
import com.sibuqu.base.common.result.HeaderUserInfo;
import com.sibuqu.common.dto.common.CommonPushMessageDTO;
import com.sibuqu.common.enums.PushTypeConstant;
import com.sibuqu.course.vo.coursetimetable.CourseTimetableUnlockDetailVO;
import com.sibuqu.work.componet.CourseComponent;
import com.sibuqu.work.entity.WorkTeacherReply;
import com.sibuqu.work.enums.CommonPushJumpPageEnum;
import com.sibuqu.work.kafka.production.KafkaProduction;
import com.sibuqu.work.mongo.WorkInfoPojo;
import com.sibuqu.work.webclient.client.OrderWebClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2022年6月9日10:55:27
 * @Version 1.0
 * @Description 异步方法service
 **/
@Service("commonAsyncService")
public class CommonAsyncService {

    @Autowired
    CourseComponent courseComponent;
    @Autowired
    KafkaProduction kafkaProduction;
    @Autowired
    OrderWebClient orderWebClient;

    /**
     * 异步方法——推荐作业推送消息通知
     */
    @Async("sendCommentExecutor")
    public void recommendWorkPushMsg(HeaderUserInfo headerUserInfo, WorkInfoPojo workInfo) {
        CommonPushMessageDTO commonPushMessageDTO = new CommonPushMessageDTO();
        commonPushMessageDTO.setClassesId(workInfo.getClassesNo());
        commonPushMessageDTO.setSkuId(courseComponent.getSkuIdByCourseId(workInfo.getCourseId()));
        commonPushMessageDTO.setWorkId(workInfo.getWorkId());
        commonPushMessageDTO.setReceiveUserId(workInfo.getUserId());
        commonPushMessageDTO.setSendUserId(headerUserInfo.getId());
        commonPushMessageDTO.setSendUserName(headerUserInfo.getUserFullName());
        commonPushMessageDTO.setHeadUrl(headerUserInfo.getAvatar());
        commonPushMessageDTO.setTitle("推荐了你的作业");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日");
        commonPushMessageDTO.setContent("你" + workInfo.getWorkTime().format(formatter) + "提交的作业被班主任" + headerUserInfo.getUserFullName() + "成功推荐给了老师，继续加油哦~");
        commonPushMessageDTO.setPushType(PushTypeConstant.COMMENT_REPLY);
        commonPushMessageDTO.setJumpPage(CommonPushJumpPageEnum.NEW_WORK_LIST.getCode());
        commonPushMessageDTO.setAllFlag(0);
        kafkaProduction.sendCommonPushSysMsg(commonPushMessageDTO);
    }

    /**
     * 异步方法——老师回复作业推送消息通知
     */
    @Async("sendCommentExecutor")
    public void teacherReplayPushMsg(HeaderUserInfo headerUserInfo, WorkInfoPojo workInfo, WorkTeacherReply workTeacherReply) {
        // 获取解锁课件信息
        CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(workInfo.getCourseId(), workInfo.getCourseTimetableId());
        CommonPushMessageDTO commonPushMessageDTO = new CommonPushMessageDTO();
        commonPushMessageDTO.setClassesId(workInfo.getClassesNo());
        commonPushMessageDTO.setSkuId(courseComponent.getSkuIdByCourseId(workInfo.getCourseId()));
        commonPushMessageDTO.setWorkId(workInfo.getWorkId());
        commonPushMessageDTO.setReceiveUserId(workInfo.getUserId());
        commonPushMessageDTO.setSendUserId(headerUserInfo.getId());
        commonPushMessageDTO.setSendUserName(headerUserInfo.getUserFullName());
        commonPushMessageDTO.setHeadUrl(headerUserInfo.getAvatar());
        commonPushMessageDTO.setTitle("回复了你的作业");
        commonPushMessageDTO.setCourseId(workInfo.getCourseId());
        String resourceTitle = ObjectUtil.isNotNull(courseTimetableUnlockDetailVO) ? courseTimetableUnlockDetailVO.getResourceTitle() : "";
        if (Objects.equals(workTeacherReply.getTeacherReplyType(), 0)) {
            commonPushMessageDTO.setContent(workTeacherReply.getTeacherContent());
        } else {
            commonPushMessageDTO.setContent("你的作业《" + resourceTitle + "》被" + headerUserInfo.getUserFullName() + "回复了，快去看看吧~");
        }
        commonPushMessageDTO.setPushType(PushTypeConstant.COMMENT_REPLY);
        commonPushMessageDTO.setAllFlag(0);
        commonPushMessageDTO.setCompanyId(workInfo.getCompanyId());
        if (workInfo.getCompanyId() == -1) {
            commonPushMessageDTO.setJumpPage(CommonPushJumpPageEnum.NEW_WORK_LIST.getCode());
        }else{
            commonPushMessageDTO.setJumpPage(CommonPushJumpPageEnum.ENTERPRISE_CLASS_HOME.getCode());
        }
        kafkaProduction.sendCommonPushSysMsg(commonPushMessageDTO);
    }

}
