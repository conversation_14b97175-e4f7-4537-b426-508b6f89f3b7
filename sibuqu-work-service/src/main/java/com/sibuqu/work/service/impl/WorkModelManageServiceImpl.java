package com.sibuqu.work.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sibuqu.base.common.exception.BusinessException;
import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.base.common.result.HeaderUserInfo;
import com.sibuqu.work.componet.CourseComponent;
import com.sibuqu.work.dto.*;
import com.sibuqu.work.entity.CourseTimetableModelDetail;
import com.sibuqu.work.entity.WorkModel;
import com.sibuqu.work.entity.WorkModelDetail;
import com.sibuqu.work.enums.WorkDetailTypeEnum;
import com.sibuqu.work.mapper.CourseTimetableModelDetailMapper;
import com.sibuqu.work.mapper.WorkModelDetailMapper;
import com.sibuqu.work.mapper.WorkModelMapper;
import com.sibuqu.work.service.WorkModelManageService;
import com.sibuqu.work.vo.AudioConfigVO;
import com.sibuqu.work.vo.admin.ModelDetailInfoVO;
import com.sibuqu.work.vo.admin.ModelDetailVO;
import com.sibuqu.work.vo.admin.WorkModelInfoVO;
import com.sibuqu.work.vo.admin.WorkModelListVO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service("WorkModelManageService")
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class WorkModelManageServiceImpl extends ServiceImpl<WorkModelMapper, WorkModel> implements WorkModelManageService {
    private final WorkModelDetailMapper workModelDetailMapper;
    private final CourseTimetableModelDetailMapper courseTimetableModelDetailMapper;
    private final CourseComponent courseComponent;
    @Override
    public PageInfoBT<WorkModelListVO> list(WorkModelListDTO workModelListDTO) {
        QueryWrapper<WorkModel> workModelQueryWrapper = new QueryWrapper<>();
        workModelQueryWrapper
                .lambda()
                .eq(StringUtils.isNotBlank(workModelListDTO.getModelCode()),WorkModel::getModelCode,workModelListDTO.getModelCode())
                .like(StringUtils.isNotBlank(workModelListDTO.getModelTitle()),WorkModel::getModelTitle,workModelListDTO.getModelTitle())
                .orderByDesc(WorkModel::getCreateTime);
        return PageInfoBT.fromPage(baseMapper.selectPage(workModelListDTO.getPage(), workModelQueryWrapper).convert(workModel -> {
            WorkModelListVO workModelListVO = new WorkModelListVO();
            workModelListVO.setModelId(workModel.getId());
            workModelListVO.setModelCode(workModel.getModelCode());
            workModelListVO.setModelTitle(workModel.getModelTitle());
            workModelListVO.setUpdateTime(workModel.getUpdateTime());
            return workModelListVO;
        }));

    }

    @Override
    public WorkModelInfoVO info(Integer workModelId) {
        WorkModel workModel = baseMapper.selectById(workModelId);
        if(Objects.isNull(workModel)){
            throw new BusinessException("作业模版信息不存在");
        }
        WorkModelInfoVO workModelInfoVO = new WorkModelInfoVO();
        BeanUtils.copyProperties(workModel,workModelInfoVO);
        workModelInfoVO.setModelId(workModel.getId());
        return workModelInfoVO;
    }

    @Override
    public List<ModelDetailVO> modelDetail(Integer workModelId) {
        QueryWrapper<WorkModelDetail> workModelDetailQueryWrapper = new QueryWrapper<>();
        workModelDetailQueryWrapper
                .lambda()
                .eq(WorkModelDetail::getWorkModelId,workModelId)
                .eq(WorkModelDetail::getWorkModelId,workModelId)
                .eq(WorkModelDetail::getDeleted,1)
                .orderByAsc(WorkModelDetail::getSorted);
        return workModelDetailMapper.selectList(workModelDetailQueryWrapper).stream().map(workModelDetail -> {
            ModelDetailVO workModelDetailVO = new ModelDetailVO();
            BeanUtils.copyProperties(workModelDetail,workModelDetailVO);
            if (WorkDetailTypeEnum.NPS.getCode().equals(workModelDetailVO.getType())){
                workModelDetailVO.setScore(workModelDetailVO.getScore() * 10);
            } else if (WorkDetailTypeEnum.DYNAMIC_MULTIPLE_CHOICE.getCode().equals(workModelDetailVO.getType())) {
                if (StrUtil.isNotBlank(workModelDetail.getOptionJson())) {
                    workModelDetailVO.setOptionList(JSON.parseArray(workModelDetail.getOptionJson(), String.class));
                }
            } else if (WorkDetailTypeEnum.DYNAMIC_SINGLE_CHOICE.getCode().equals(workModelDetailVO.getType())) {
                if (StrUtil.isNotBlank(workModelDetail.getOptionJson())) {
                    workModelDetailVO.setOptionList(JSON.parseArray(workModelDetail.getOptionJson(), String.class));
                }
            }
            return workModelDetailVO;
        }).collect(Collectors.toList());

    }

    @Override
    public void addModel(WorkModelAddDTO workModelAddDTO, HeaderUserInfo headerUserInfo) {
        this.baseMapper.insert(WorkModel.builder()
                        .modelTitle(workModelAddDTO.getModelTitle())
                        .createUserId(headerUserInfo.getId())
                .build());

    }

    @Override
    public void update(WorkModelUpdateDTO workModelUpdateDTO, HeaderUserInfo headerUserInfo) {
        if (Objects.isNull(baseMapper.selectById(workModelUpdateDTO.getId()))){
            throw new BusinessException("作业模版信息不存在");
        }
        baseMapper.updateById(WorkModel.builder()
                        .modelTitle(workModelUpdateDTO.getModelTitle())
                        .id(workModelUpdateDTO.getId())
                        .updateUserId(headerUserInfo.getId())
                        .updateTime(LocalDateTime.now())
                .build());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addModelDetail(WorkModelDetailAddDTO dto, HeaderUserInfo headerUserInfo) {
        if (Objects.isNull(baseMapper.selectById(dto.getModelId()))){
            throw new BusinessException("作业模版信息不存在");
        }
        QueryWrapper<WorkModelDetail> workModelDetailQueryWrapper = new QueryWrapper<>();
        workModelDetailQueryWrapper
                .lambda()
                .eq(WorkModelDetail::getDeleted,1)
                .eq(WorkModelDetail::getWorkModelId,dto.getModelId())
                .eq(WorkModelDetail::getSorted,dto.getSorted());
        if(workModelDetailMapper.selectCount(workModelDetailQueryWrapper) > 0){
            throw new BusinessException("作业项序号已存在");
        }
        WorkModelDetail workModelDetail = new WorkModelDetail();
        BeanUtils.copyProperties(dto, workModelDetail);
        workModelDetail.setWorkModelId(dto.getModelId());
        workModelDetail.setCreateUserId(headerUserInfo.getId());
        if (WorkDetailTypeEnum.NPS.getCode().equals(workModelDetail.getType())){
            workModelDetail.setScore(workModelDetail.getScore() / 10);
        } else if (WorkDetailTypeEnum.DYNAMIC_MULTIPLE_CHOICE.getCode().equals(workModelDetail.getType())) {
            if (CollUtil.isEmpty(dto.getOptionList())) {
                throw new BusinessException("选项不能为空");
            }
            workModelDetail.setOptionJson(JSON.toJSONString(dto.getOptionList()));
        } else if (WorkDetailTypeEnum.DYNAMIC_SINGLE_CHOICE.getCode().equals(workModelDetail.getType())) {
            if (CollUtil.isEmpty(dto.getOptionList())) {
                throw new BusinessException("选项不能为空");
            }
            workModelDetail.setOptionJson(JSON.toJSONString(dto.getOptionList()));
        }
        workModelDetailMapper.insert(workModelDetail);
        List<WorkModelDetail> workModelDetailList = workModelDetailMapper.selectList(new LambdaQueryWrapper<WorkModelDetail>()
                .eq(WorkModelDetail::getWorkModelId, workModelDetail.getWorkModelId())
                .eq(WorkModelDetail::getDeleted, 1));
        long count = workModelDetailList.stream().filter(detail -> WorkDetailTypeEnum.READ.getCode().equals(detail.getType())).count();
        if (count > 1) {
            throw new BusinessException("最多设置一个读书项");
        }
        cancelWorkModel(dto.getModelId());
    }

    public void cancelWorkModel(Integer workModelId){
        List<Integer> courseTimeTableIds =  courseComponent.courTimeTableIdsByWorkModelId(workModelId);
        if (courseTimeTableIds.size() > 0) {
            courseTimetableModelDetailMapper.delete(new QueryWrapper<CourseTimetableModelDetail>().lambda()
                    .eq(CourseTimetableModelDetail::getWorkModelId, workModelId)
                    .in(CourseTimetableModelDetail::getCourseTimetableId, courseTimeTableIds));
        }
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateModelDetail(WorkModelDetailUpdateDTO dto, HeaderUserInfo headerUserInfo) {
        WorkModelDetail modelDetail = workModelDetailMapper.selectById(dto.getId());
        if (Objects.isNull(modelDetail)){
            throw new BusinessException("作业项信息不存在");
        }
        QueryWrapper<WorkModelDetail> workModelDetailQueryWrapper = new QueryWrapper<>();
        workModelDetailQueryWrapper
                .lambda()
                .eq(WorkModelDetail::getWorkModelId,modelDetail.getWorkModelId())
                .eq(WorkModelDetail::getSorted,dto.getSorted())
                .ne(WorkModelDetail::getId,dto.getId())
                .eq(WorkModelDetail::getDeleted,1);
        if(workModelDetailMapper.selectCount(workModelDetailQueryWrapper) > 0){
            throw new BusinessException("作业项序号已存在");
        }
        WorkModelDetail workModelDetail = new WorkModelDetail();
        BeanUtils.copyProperties(dto, workModelDetail);
        if (Objects.nonNull(workModelDetail.getType()) && workModelDetail.getType().equals(WorkDetailTypeEnum.NPS.getCode())){
            workModelDetail.setScore(workModelDetail.getScore() / 10);
        } else if (WorkDetailTypeEnum.DYNAMIC_MULTIPLE_CHOICE.getCode().equals(workModelDetail.getType())) {
            if (CollUtil.isEmpty(dto.getOptionList())) {
                throw new BusinessException("选项不能为空");
            }
            workModelDetail.setOptionJson(JSON.toJSONString(dto.getOptionList()));
        } else if (WorkDetailTypeEnum.DYNAMIC_SINGLE_CHOICE.getCode().equals(workModelDetail.getType())) {
            if (CollUtil.isEmpty(dto.getOptionList())) {
                throw new BusinessException("选项不能为空");
            }
            workModelDetail.setOptionJson(JSON.toJSONString(dto.getOptionList()));
        }
        workModelDetail.setUpdateUserId(headerUserInfo.getId());
        workModelDetailMapper.updateById(workModelDetail);
        List<WorkModelDetail> workModelDetailList = workModelDetailMapper.selectList(new LambdaQueryWrapper<WorkModelDetail>()
                .eq(WorkModelDetail::getWorkModelId, modelDetail.getWorkModelId())
                .eq(WorkModelDetail::getDeleted, 1));
        long count = workModelDetailList.stream().filter(detail -> WorkDetailTypeEnum.READ.getCode().equals(detail.getType())).count();
        if (count > 1) {
            throw new BusinessException("最多设置一个读书项");
        }
        cancelWorkModel(modelDetail.getWorkModelId());
    }

    @Override
    public ModelDetailInfoVO modelDetailInfo(Integer modelDetailId, HeaderUserInfo headerUserInfo) {
        WorkModelDetail workModelDetail = workModelDetailMapper.selectById(modelDetailId);
        if (Objects.isNull(workModelDetail)) {
            throw new BusinessException("作业项信息不存在");
        }
        ModelDetailInfoVO workModelDetailVO = new ModelDetailInfoVO();
        BeanUtils.copyProperties(workModelDetail, workModelDetailVO);
        if (workModelDetailVO.getType().equals(WorkDetailTypeEnum.NPS.getCode())) {
            workModelDetailVO.setScore(workModelDetailVO.getScore() * 10);
        } else if (WorkDetailTypeEnum.DYNAMIC_MULTIPLE_CHOICE.getCode().equals(workModelDetail.getType())) {
            if (StrUtil.isNotBlank(workModelDetail.getOptionJson())) {
                workModelDetailVO.setOptionList(JSON.parseArray(workModelDetail.getOptionJson(), String.class));
            }
        } else if (WorkDetailTypeEnum.DYNAMIC_SINGLE_CHOICE.getCode().equals(workModelDetail.getType())) {
            if (StrUtil.isNotBlank(workModelDetail.getOptionJson())) {
                workModelDetailVO.setOptionList(JSON.parseArray(workModelDetail.getOptionJson(), String.class));
            }
        }
        return workModelDetailVO;
    }

    @Override
    public void deleteModelDetail(DeleteModelDetailDTO deleteModelDetailDTO, HeaderUserInfo headerUserInfo) {
        List<WorkModelDetail> workModelDetails = workModelDetailMapper.selectBatchIds(deleteModelDetailDTO.getIds());

        if (workModelDetails.size() < 1){
            throw new BusinessException("作业项信息不存在");
        }
        LambdaUpdateWrapper<WorkModelDetail> workModelDetailQueryWrapper = new LambdaUpdateWrapper<>();
        workModelDetailQueryWrapper
                .in(WorkModelDetail::getId,deleteModelDetailDTO.getIds())
                .set(WorkModelDetail::getDeleted,0);
        workModelDetailMapper.update(new WorkModelDetail(),workModelDetailQueryWrapper);
        cancelWorkModel(workModelDetails.get(0).getWorkModelId());
    }

    @Override
    public AudioConfigVO audioConfig() {
        return new AudioConfigVO();
    }
}
