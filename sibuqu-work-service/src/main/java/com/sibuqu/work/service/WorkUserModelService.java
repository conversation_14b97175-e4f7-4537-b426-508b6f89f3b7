package com.sibuqu.work.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sibuqu.work.dto.AddWorkUserModelDTO;
import com.sibuqu.work.dto.QueryWorkUserModelDTO;
import com.sibuqu.work.entity.WorkUserModel;
import com.sibuqu.work.vo.QueryWorkUserModelListVO;

import java.util.List;

public interface WorkUserModelService extends IService<WorkUserModel> {


    String addWorkUserModel(AddWorkUserModelDTO dto);

    WorkUserModel getByUserIdAndCourseId(Integer userId, Integer courseId);

    List<WorkUserModel> getByUserIdListAndCourseId(List<Integer> userIdList, Integer courseId);

    List<QueryWorkUserModelListVO> queryWorkUserModel(QueryWorkUserModelDTO dto);
}
