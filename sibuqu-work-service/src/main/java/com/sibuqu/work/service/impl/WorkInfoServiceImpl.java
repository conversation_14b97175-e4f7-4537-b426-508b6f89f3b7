package com.sibuqu.work.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sibuqu.base.common.enums.IsDeleteEnum;
import com.sibuqu.base.common.enums.YesOrNoEnum;
import com.sibuqu.base.common.exception.BusinessException;
import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.base.common.result.HeaderUserInfo;
import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.classes.vo.InnerClassesInfoVO;
import com.sibuqu.course.entity.CourseConfig;
import com.sibuqu.course.entity.CourseInfo;
import com.sibuqu.course.entity.CourseTimetable;
import com.sibuqu.course.vo.coursetimetable.CourseTimetableSimpleListVO;
import com.sibuqu.course.vo.coursetimetable.CourseTimetableUnlockDetailVO;
import com.sibuqu.course.vo.personal.app.CttByCourseIdBeginDateEndDateVO;
import com.sibuqu.mysqlmultiple.annotation.DataSource;
import com.sibuqu.mysqlmultiple.enums.DataSourceTypeEnum;
import com.sibuqu.order.entity.auth.UserRightsCourse;
import com.sibuqu.starter.redis.headeruserinfo.HeaderUserInfoUtil;
import com.sibuqu.starter.redis.util.RedisUtil;
import com.sibuqu.user.dto.user.UserInfoListSearchByPhoneDTO;
import com.sibuqu.user.vo.api.UserInfoVO;
import com.sibuqu.user.vo.user.UserInfoSearchSimpleVO;
import com.sibuqu.work.bo.WorkInfoPojoQueryDTO;
import com.sibuqu.work.componet.ClassesComponent;
import com.sibuqu.work.componet.CourseComponent;
import com.sibuqu.work.componet.OrderComponent;
import com.sibuqu.work.componet.PrefectureComponent;
import com.sibuqu.work.componet.UserComponent;
import com.sibuqu.work.dto.CheckOldStudentCountDTO;
import com.sibuqu.work.dto.CheckOldStudentDTO;
import com.sibuqu.work.dto.CurWeekWorkDTO;
import com.sibuqu.work.dto.DoWorkDetailDTO;
import com.sibuqu.work.dto.DoWorkMsgDTO;
import com.sibuqu.work.dto.HistoryWeekWorkDTO;
import com.sibuqu.work.dto.RecordThinkDTO;
import com.sibuqu.work.dto.WorkCardInfoDTO;
import com.sibuqu.work.dto.WorkFlexibleItemSearchDTO;
import com.sibuqu.work.dto.WorkInfoListDTO;
import com.sibuqu.work.dto.WorkInfoRecommendTeacherDTO;
import com.sibuqu.work.dto.sennuo.OpenClessesWorkDTO;
import com.sibuqu.work.entity.CourseTimetableModelDetail;
import com.sibuqu.work.entity.WeekGoal;
import com.sibuqu.work.entity.WorkInfo;
import com.sibuqu.work.entity.WorkModelDetail;
import com.sibuqu.work.entity.WorkTeacherReply;
import com.sibuqu.work.enums.RecommendStatusEnums;
import com.sibuqu.work.enums.UserFlagEnum;
import com.sibuqu.work.enums.WorkDetailTypeEnum;
import com.sibuqu.work.enums.WorkKafkaMessageEnum;
import com.sibuqu.work.enums.WorkModelFlagEnum;
import com.sibuqu.work.mapper.WorkInfoMapper;
import com.sibuqu.work.mapper.WorkModelDetailMapper;
import com.sibuqu.work.mongo.WorkInfoPojo;
import com.sibuqu.work.service.CourseTimetableModelDetailService;
import com.sibuqu.work.service.WeekGoalService;
import com.sibuqu.work.service.WorkInfoPojoMongoService;
import com.sibuqu.work.service.WorkInfoService;
import com.sibuqu.work.service.WorkService;
import com.sibuqu.work.service.WorkTeacherReplyService;
import com.sibuqu.work.util.AESUtil;
import com.sibuqu.work.vo.HistoryWeekWorkItemVO;
import com.sibuqu.work.vo.WeekWorkItemVO;
import com.sibuqu.work.vo.CurWeekWorkVO;
import com.sibuqu.work.vo.admin.*;
import com.sibuqu.work.vo.api.AdditionalInfoVO;
import com.sibuqu.work.vo.api.OpenCompanyWorkVO;
import com.sibuqu.work.vo.api.WorkCardInfoVO;
import com.sibuqu.work.vo.api.sennuo.OpenClassesWorkVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;


import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoField;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RefreshScope
public class WorkInfoServiceImpl extends ServiceImpl<WorkInfoMapper, WorkInfo> implements WorkInfoService {
    private final UserComponent userComponent;
    private final WorkTeacherReplyService workTeacherReplyService;
    private final WorkModelDetailMapper workModelDetailMapper;
    private final CourseComponent courseComponent;
    private final MongoTemplate mongoTemplate;
    private final PrefectureComponent prefectureComponent;
    private final RedisUtil redisUtil;
    private final WorkInfoPojoMongoService workInfoPojoMongoService;
    private final ClassesComponent classesComponent;
    private final WeekGoalService weekGoalService;
    private final WorkService workService;
    private final OrderComponent orderComponent;
    private final CourseTimetableModelDetailService courseTimetableModelDetailService;

    @Value("${openapi.company.work.courseid:480}")
    private Integer openApiWorkCourse;
    @Value("${openapi.company.work.numb:10}")
    private Integer openApiWorkNumb;

    @Override
    public PageInfoBT<WorkInfoListVO> getWorkInfoList(WorkInfoListDTO dto, Boolean isExport) {
        if (isExport) {
            dto.setPageNum(1);
            dto.setPageSize(100000);
        }
        IPage<WorkInfoPojo> page = workInfoPojoMongoService.getWorkInfoList(dto);
        if (CollUtil.isEmpty(page.getRecords())) {
            return PageInfoBT.from(0, new ArrayList<>());
        }

        List<Integer> usersList = new ArrayList<>();
        List<Integer> courseTimetableIdList = new ArrayList<>();
        List<String> usersPhoneList = new ArrayList<>();
        page.getRecords().forEach(workInfo -> {
            usersList.add(workInfo.getUserId());
            courseTimetableIdList.add(workInfo.getCourseTimetableId());
            if (StringUtils.isNotBlank(workInfo.getClassesTeacherPhone())) {
                usersPhoneList.add(workInfo.getClassesTeacherPhone());
            }
        });
        Map<Integer, UserInfoSearchSimpleVO> userinfoMap = userComponent.userSimpleListByUserIds(usersList, null).stream().collect(Collectors.toMap(UserInfoSearchSimpleVO::getUserId, userInfo -> userInfo));
        Map<Integer, String> courseTimetableIdMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(courseTimetableIdList)) {
            List<CourseTimetableSimpleListVO> courseTimetableList = courseComponent.getCourseTimetableSimpleByCourseTimetableIds(courseTimetableIdList);
            if (CollectionUtil.isNotEmpty(courseTimetableList)) {
                courseTimetableIdMap = courseTimetableList.stream().collect(Collectors.toMap(CourseTimetableSimpleListVO::getCourseTimetableId, CourseTimetableSimpleListVO::getResourceTitle));
            }
        }
        Map<Integer, String> finalCourseTimetableIdMap = courseTimetableIdMap;
        UserInfoListSearchByPhoneDTO entityListSearchDTO = new UserInfoListSearchByPhoneDTO();
        entityListSearchDTO.setPhones(usersPhoneList);
        List<UserInfoVO> userInfoTeacherSearchSimpleVOS = userComponent.userListByPhone(entityListSearchDTO);
        Map<String, String> userTeacherInfoMap = null;
        if (CollectionUtil.isNotEmpty(userInfoTeacherSearchSimpleVOS)) {
            userTeacherInfoMap = userInfoTeacherSearchSimpleVOS.stream().collect(Collectors.toMap(UserInfoVO::getPhone, UserInfoVO::getUserFullName, (oldValue, newValue) -> oldValue));
        }
        Map<String, String> finalUserTeacherInfoMap = userTeacherInfoMap;
        IPage<WorkInfoListVO> workInfoListVOIPage = page.convert(workInfo -> {
            WorkInfoListVO workInfoListVO = new WorkInfoListVO();
            BeanUtil.copyProperties(workInfo, workInfoListVO);
            workInfoListVO.setCreateTime(workInfo.getWorkTime());
            workInfoListVO.setId(workInfo.getWorkId());
            Integer userId = workInfo.getUserId();
            String teacherPhone = workInfo.getClassesTeacherPhone();
            if (Objects.nonNull(userinfoMap.get(userId))) {
                UserInfoSearchSimpleVO userInfo = userinfoMap.get(userId);
                workInfoListVO.setUserName(userInfo.getUserFullName());
                workInfoListVO.setUserPhone(userInfo.getPhone());
            }
            Integer courseTimetableId = workInfo.getCourseTimetableId();
            if (Objects.nonNull(finalCourseTimetableIdMap.get(courseTimetableId))) {
                String resTitle = finalCourseTimetableIdMap.get(courseTimetableId);
                workInfoListVO.setCourseWareName(resTitle);
            }
            // 有班级才赋值
            if (workInfo.getClassesId() > 0) {
                if (Objects.nonNull(finalUserTeacherInfoMap) && StringUtils.isNotBlank(teacherPhone) && Objects.nonNull(finalUserTeacherInfoMap.get(teacherPhone))) {
                    String userFullName = finalUserTeacherInfoMap.get(teacherPhone);
                    workInfoListVO.setClassesTeacherName(userFullName);
                }
                workInfoListVO.setClassesTeacherPhone(workInfo.getClassesTeacherPhone());
            } else {
                // 无班级 容错班主任手机号不为空就赋值给空
                workInfoListVO.setClassesTeacherPhone(null);
            }
            workInfoListVO.setTotalScore(workInfo.getScore());
            workInfoListVO.setCourseBeginTime(DateUtil.format(workInfo.getCourseBeginTime(), DatePattern.NORM_DATE_PATTERN));
            workInfoListVO.setHaveClass(Objects.nonNull(workInfo.getClassesNo()) && !Objects.equals(workInfo.getClassesNo(), 0));
            if (StringUtils.isNotBlank(workInfo.getContent())) {
                List<WorkModelDetailVO> workModelDetailVOS = JSONObject.parseArray(workInfo.getContent(), WorkModelDetailVO.class);
                if (CollectionUtil.isNotEmpty(workModelDetailVOS)) {
                    Integer dayTotalScore = workModelDetailVOS.stream().mapToInt(WorkModelDetailVO::getScore).sum();
                    workInfoListVO.setDayTotalScore(dayTotalScore);
                    List<String> contentList = workModelDetailVOS.stream().filter(detail ->
                                    WorkDetailTypeEnum.TEXT.getCode().equals(detail.getType()) ||
                                            WorkDetailTypeEnum.DUSING.getCode().equals(detail.getType()) ||
                                            WorkDetailTypeEnum.AUDIO_TEXT.getCode().equals(detail.getType()))
                            .map(WorkModelDetailVO::getContent).collect(Collectors.toList());

                    List<String> collectListTitle = workModelDetailVOS.stream().filter(detail -> WorkDetailTypeEnum.TEXT.getCode().equals(detail.getType()) ||
                            WorkDetailTypeEnum.DUSING.getCode().equals(detail.getType()) ||
                            WorkDetailTypeEnum.AUDIO_TEXT.getCode().equals(detail.getType()))
                            .map(WorkModelDetailVO::getTitle).collect(Collectors.toList());
                    for (int i = 0; i < contentList.size(); i++) {
                        if (i == 0) {
                            workInfoListVO.setWorkContentOne(contentList.get(i));
                            workInfoListVO.setWorkTitleOne(collectListTitle.get(i));
                        }
                        if (i == 1) {
                            workInfoListVO.setWorkContentTwo(contentList.get(i));
                            workInfoListVO.setWorkTitleTwo(collectListTitle.get(i));
                        }
                        if (i == 2) {
                            workInfoListVO.setWorkContentThree(contentList.get(i));
                            workInfoListVO.setWorkTitleThree(collectListTitle.get(i));
                        }
                        if (i == 3) {
                            workInfoListVO.setWorkContentFour(contentList.get(i));
                            workInfoListVO.setWorkTitleFour(collectListTitle.get(i));
                        }
                        if (i == 4) {
                            workInfoListVO.setWorkContentFive(contentList.get(i));
                            workInfoListVO.setWorkTitleFive(collectListTitle.get(i));
                        }
                    }
                }
            }
            if (workInfoListVO.getRecommendTeacherStatus() == null) {
                workInfoListVO.setRecommendTeacherStatus(0);
            }
            return workInfoListVO;
        });
        return PageInfoBT.fromPage(workInfoListVOIPage);
    }

    @Override
    public WorkInfoDetailVO workInfoDetail(Integer id) {
        WorkInfoDetailVO vo = new WorkInfoDetailVO();
        WorkInfoPojo workInfo = workInfoPojoMongoService.getByWorkId(id);
        if (Objects.isNull(workInfo)) {
            return vo;
        }
        Integer userId = workInfo.getUserId();
        BeanUtils.copyProperties(workInfo, vo);
        vo.setCreateTime(workInfo.getWorkTime());
        vo.setClassesNo(String.valueOf(workInfo.getClassesNo()));
        UserInfoSearchSimpleVO userSimpleVO = userComponent.userSimpleByUserId(userId);
        if (userSimpleVO != null) {
            String userFullName = userSimpleVO.getUserFullName();
            String phone = userSimpleVO.getPhone();
            vo.setUserName(userFullName);
            vo.setUserPhone(phone);

        }
        /**
         * 班主任信息
         */
        InnerClassesInfoVO innerClassesInfoVO = classesComponent.innerClassesInfo(workInfo.getClassesId());
        if (Objects.nonNull(innerClassesInfoVO)) {
            vo.setClassesTeacherName(innerClassesInfoVO.getClassesOwnerName());
            if (StringUtils.isNotBlank(innerClassesInfoVO.getClassesOwnerPhone()) && innerClassesInfoVO.getClassesOwnerPhone().length() < 16) {
                vo.setClassesTeacherPhone(AESUtil.encrypt(innerClassesInfoVO.getClassesOwnerPhone()));
            } else {
                vo.setClassesTeacherPhone(innerClassesInfoVO.getClassesOwnerPhone());
            }
        }
        /** 课程表信息**/
        CourseTimetable courseTimetableDetail = courseComponent.getCourseTimetableDetail(workInfo.getCourseTimetableId());
        if (courseTimetableDetail != null) {
            vo.setCourseWareName(courseTimetableDetail.getResourceTitle());
        }

        // 老师回复的信息
        LambdaQueryWrapper<WorkTeacherReply> teacherReplyLambdaQueryWrapper = new LambdaQueryWrapper<>();
        teacherReplyLambdaQueryWrapper
                .eq(WorkTeacherReply::getWorkId, workInfo.getId())
                .eq(WorkTeacherReply::getCourseId, workInfo.getCourseId())
        ;
        List<WorkTeacherReply> teacherReplyList = workTeacherReplyService.list(teacherReplyLambdaQueryWrapper);
        List<WorkTeacherReplyVO> workTeacherReplyVos = new ArrayList<>();
        for (WorkTeacherReply workTeacherReply : teacherReplyList) {
            WorkTeacherReplyVO replyVO = new WorkTeacherReplyVO();
            BeanUtils.copyProperties(workTeacherReply, replyVO);

            replyVO.setTeacherReply(workTeacherReply.getTeacherContent());
            replyVO.setPostId(workTeacherReply.getWorkId());
            replyVO.setTeacherReplyTime(workTeacherReply.getTeacherReplyTime());
            if (workTeacherReply.getTeacherUserId().equals(9999999)){
                replyVO.setAiReplyStatus(1);
            }else {
                replyVO.setAiReplyStatus(0);
            }
            replyVO.setShowStatus(workTeacherReply.getDeleteFlag());
            replyVO.setTeacherReplyType(workTeacherReply.getTeacherReplyType());
            workTeacherReplyVos.add(replyVO);

            vo.setTeacherReplyDate(workTeacherReply.getTeacherReplyTime());
        }

        vo.setWorkTeacherReplyVos(workTeacherReplyVos);

        /** 作业明细  **/
        List<WorkModelDetailVO> workDetails = new ArrayList<>();
        if (StringUtils.isNotBlank(workInfo.getContent())) {
            List<WorkModelDetailVO> workModelDetailVOS = JSONObject.parseArray(workInfo.getContent(), WorkModelDetailVO.class);
            if (CollectionUtil.isNotEmpty(workModelDetailVOS)) {
                for (WorkModelDetailVO workModelDetailVO : workModelDetailVOS) {
                    Integer type = workModelDetailVO.getType();
                    String content = workModelDetailVO.getContent();
                    // 1听课类型2打卡  9打卡不加分 11分享加分 12多选题
                    String contentName = content;
                    if (WorkDetailTypeEnum.LISTEN.getCode().equals(type)) {
                        contentName = StringUtils.isNotBlank(content) && "1".equals(content) ? "已" + WorkDetailTypeEnum.LISTEN.getDesc() : "未" + WorkDetailTypeEnum.LISTEN.getDesc();
                    } else if (WorkDetailTypeEnum.CLOCK.getCode().equals(type)) {
                        contentName = StringUtils.isNotBlank(content) && "1".equals(content) ? "已" + WorkDetailTypeEnum.CLOCK.getDesc() : "未" + WorkDetailTypeEnum.CLOCK.getDesc();
                    } else if (WorkDetailTypeEnum.SHARE.getCode().equals(type)) {
                        contentName = StringUtils.isNotBlank(content) && "1".equals(content) ? "已分享" : "未分享";
                    } else if (WorkDetailTypeEnum.EVALUATE.getCode().equals(type)) {
                        if (StringUtils.isBlank(content)) {
                            contentName = "未" + WorkDetailTypeEnum.EVALUATE.getDesc();
                        } else {
                            contentName = StringUtils.isNotBlank(content) && "0".equals(content) ? "未" + WorkDetailTypeEnum.EVALUATE.getDesc() : "已" + WorkDetailTypeEnum.EVALUATE.getDesc();
                        }
                    }
                    // TODO 评分属于哪个字段？
                    workModelDetailVO.setContent(contentName);
                }
            }
            workDetails.addAll(workModelDetailVOS);
            if (CollectionUtil.isNotEmpty(workModelDetailVOS)) {
                Integer dayTotalScore = workModelDetailVOS.stream().collect(Collectors.summingInt(WorkModelDetailVO::getScore));
                vo.setDayTotalScore(dayTotalScore);
            }
        }
        vo.setWorkDetails(workDetails);

        vo.setTotalScore(workInfo.getScore());
        vo.setCourseBeginTime(DateUtil.format(workInfo.getCourseBeginTime(), DatePattern.NORM_DATE_PATTERN));
        String additional = workInfo.getAdditional();
        if (StringUtils.isNotBlank(additional)) {
            /**
             * 附加作业添加信息
             */
            AdditionalInfoVO additionalInfoVO = JSONObject.parseObject(additional, AdditionalInfoVO.class);
            // vo.setAdditionalInfo(additionalInfoVO);
            WorkModelDetailVO workModelDetailVO = WorkModelDetailVO.builder()
                    .prompt(additionalInfoVO.getTitle())
                    .score(additionalInfoVO.getAdditionalScore())
                    .content(additionalInfoVO.getStatus() > 0 ? "已分享附加作业" : "未分享附加作业")
                    .title("延伸学习")
                    .description(additionalInfoVO.getSummary())
                    .build();
            workDetails.add(workModelDetailVO);

        }

        return vo;
    }

    @Override
    public Boolean recommendToTeacher(WorkInfoRecommendTeacherDTO dto, Integer operatorUserId) {
        WorkInfo workInfo = this.getById(dto.getId());
        LocalDateTime now = LocalDateTime.now();
        if (YesOrNoEnum.NO.getCode().equals(workInfo.getRecommendStatus())) {
            workInfo.setRecommendStatus(YesOrNoEnum.YES.getCode());
            workInfo.setRecommendTime(now);
        }
        workInfo.setRecommendTeacherStatus(dto.getRecommendTeacherStatus());
        if (RecommendStatusEnums.NO.getCode().equals(dto.getRecommendTeacherStatus())) {
            workInfo.setRecommendTeacherTime(null);
        } else {
            workInfo.setRecommendTeacherTime(now);
        }
        boolean update = this.updateById(workInfo);
        Query query = new Query().addCriteria(Criteria.where("workId").is(workInfo.getId()));
        WorkInfoPojo workInfoPojoFindOne = mongoTemplate.findOne(query, WorkInfoPojo.class);
        if (Objects.isNull(workInfoPojoFindOne)) {
            WorkInfoPojo workInfoPojo = new WorkInfoPojo();
            BeanUtils.copyProperties(workInfo, workInfoPojo);
            workInfoPojo.setWorkId(workInfo.getId());
            mongoTemplate.save(workInfoPojo);
        } else {
            WorkInfoPojo workInfoPojo = new WorkInfoPojo();
            BeanUtils.copyProperties(workInfo, workInfoPojo);
            workInfoPojo.setWorkId(workInfo.getId());
            mongoTemplate.remove(query, WorkInfoPojo.class);
            mongoTemplate.save(workInfoPojo);
        }
        return update;
    }

    @Override
    public WorkFlexibleItemListVO workFlexibleColumnNameList(WorkFlexibleItemSearchDTO dto) {
        //  获取课程下course_info表的模板id
        WorkFlexibleItemListVO vo = new WorkFlexibleItemListVO();
        CourseInfo courseInfo = courseComponent.courseInfoById(dto.getCourseId());
        if (courseInfo == null) {
            return vo;
        }
        vo.setWorkModelFlag(courseInfo.getWorkModelFlag());
        List<Integer> workModelIds = new ArrayList<>();
        if (WorkModelFlagEnum.DIFF_OLD_NEW.getCode().equals(courseInfo.getWorkModelFlag())) {
            workModelIds.add(courseInfo.getWorkModelId());
            workModelIds.add(courseInfo.getOldUserWorkModelId());
        } else if (WorkModelFlagEnum.LEVEL_WORK_TEMPLATE.getCode().equals(courseInfo.getWorkModelFlag())) {
            workModelIds.add(courseInfo.getAlevelWorkModelId());
            workModelIds.add(courseInfo.getBlevelWorkModelId());
            workModelIds.add(courseInfo.getClevelWorkModelId());
        } else {
            workModelIds.add(courseInfo.getWorkModelId());
        }

        List<WorkModelVO> workModelVOS = new ArrayList<>();
        List<Integer> detailType = new ArrayList<>();
        detailType.add(WorkDetailTypeEnum.TEXT.getCode());
        detailType.add(WorkDetailTypeEnum.DUSING.getCode());
        detailType.add(WorkDetailTypeEnum.AUDIO_TEXT.getCode());
        Map<Integer, List<WorkModelDetail>> workModelDetailsMap = workModelDetailMapper.selectList(new LambdaQueryWrapper<WorkModelDetail>()
                .in(WorkModelDetail::getWorkModelId, workModelIds)
                .in(WorkModelDetail::getType, detailType)
                .eq(WorkModelDetail::getDeleted, 1)
                .orderByAsc(WorkModelDetail::getSorted)).stream().collect(Collectors.groupingBy(WorkModelDetail::getWorkModelId));


        if (WorkModelFlagEnum.DIFF_OLD_NEW.getCode().equals(courseInfo.getWorkModelFlag()) && Objects.nonNull(workModelDetailsMap.get(courseInfo.getOldUserWorkModelId()))) {
            List<WorkModelDetail> workModelDetails = workModelDetailsMap.get(courseInfo.getWorkModelId());
            workModelVOS.add(workModelBuild(courseInfo.getWorkModelId(), UserFlagEnum.NEW_USER.getCode(), courseInfo.getWorkModelName(), workModelDetails));
            List<WorkModelDetail> oldWorkModelDetails = workModelDetailsMap.get(courseInfo.getOldUserWorkModelId());
            workModelVOS.add(workModelBuild(courseInfo.getOldUserWorkModelId(), UserFlagEnum.OLD_USER.getCode(), courseInfo.getOldUserWorkModelName(), oldWorkModelDetails));
        } else if (WorkModelFlagEnum.LEVEL_WORK_TEMPLATE.getCode().equals(courseInfo.getWorkModelFlag())) {
            List<WorkModelDetail> alevelWorkModelDetails = workModelDetailsMap.get(courseInfo.getAlevelWorkModelId());
            workModelVOS.add(workModelBuild(courseInfo.getAlevelWorkModelId(), courseInfo.getWorkModelFlag(), courseInfo.getAlevelWorkModelName(), alevelWorkModelDetails));
            List<WorkModelDetail> blevelWorkModelDetails = workModelDetailsMap.get(courseInfo.getBlevelWorkModelId());
            workModelVOS.add(workModelBuild(courseInfo.getBlevelWorkModelId(), courseInfo.getWorkModelFlag(), courseInfo.getBlevelWorkModelName(), blevelWorkModelDetails));
            List<WorkModelDetail> clevelWorkModelDetails = workModelDetailsMap.get(courseInfo.getClevelWorkModelId());
            workModelVOS.add(workModelBuild(courseInfo.getClevelWorkModelId(), courseInfo.getWorkModelFlag(), courseInfo.getClevelWorkModelName(), clevelWorkModelDetails));
        } else {
            List<WorkModelDetail> workModelDetails = workModelDetailsMap.get(courseInfo.getWorkModelId());
            workModelVOS.add(workModelBuild(courseInfo.getWorkModelId(), UserFlagEnum.NEW_USER.getCode(), courseInfo.getWorkModelName(), workModelDetails));
        }
        vo.setWorkModelList(workModelVOS);
        return vo;
    }

    @Override
    public void batchAsyncWork(Integer courseId, Integer contentType) {
        List<WorkInfoPojo> workInfos = workInfoPojoMongoService.batchAsyncWork(courseId, contentType);
        workInfos.forEach(workInfo -> {
            prefectureComponent.workContentAsyncPrefecture(workInfo, contentType);
        });
    }

    @Override
    public boolean checkOldStudent(CheckOldStudentDTO dto) {
        Integer count = this.baseMapper.checkOldUser(dto.getCourseId(), dto.getUserId());
        return count > 0;
    }

    @Override
    public List<Integer> checkOldStudentCount(CheckOldStudentCountDTO dto) {
        if (CollUtil.isEmpty(dto.getUserIdList())) {
            return new ArrayList<>();
        }
        return this.baseMapper.checkOldStudentCount(dto);
    }

    @Override
    @DataSource(DataSourceTypeEnum.SLAVE)
    public ResultInfo<List<OpenCompanyWorkVO>> openCompanyWorkList(LocalDate startDate, LocalDate endDate) {
        String redisKey = "openapiwork";
        redisUtil.incr(redisKey, 1);
        redisUtil.expire(redisKey, 5, TimeUnit.MINUTES);
        Integer integer = redisUtil.get(redisKey, Integer.class);
        if (integer > openApiWorkNumb) {
            return ResultInfo.error("该接口调用频繁，5分钟后再试");
        }
        List<OpenCompanyWorkVO> companyWorkVOList = new ArrayList<>();
        QueryWrapper<WorkInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(WorkInfo::getCompanyId, 2)
                .eq(WorkInfo::getCourseId, openApiWorkCourse)
                .between(Objects.nonNull(startDate) && Objects.nonNull(endDate), WorkInfo::getWorkTime, startDate + " 00:00:00", endDate + " 23:59:59");
        List<WorkInfo> list = this.list(queryWrapper);
        if (Objects.nonNull(list) && list.size() > 0) {
            list.forEach(v -> {
                OpenCompanyWorkVO openCompanyWorkVO = new OpenCompanyWorkVO();
                openCompanyWorkVO.setListenStatus(v.getListenStatus());
                openCompanyWorkVO.setPhone(StringUtils.isNotEmpty(v.getUserPhone())?AESUtil.decrypt(v.getUserPhone()):"");
                openCompanyWorkVO.setUserName(v.getUserName());
                openCompanyWorkVO.setWorkStatus(v.getWorkStatus());
                openCompanyWorkVO.setWorkTime(v.getWorkTime());
                openCompanyWorkVO.setScore(v.getScore());
                openCompanyWorkVO.setWorkBelongToDate(v.getCourseBeginTime().toLocalDate());
                companyWorkVOList.add(openCompanyWorkVO);
            });
        }
        return ResultInfo.ok(companyWorkVOList);
    }

    @Override
    public WorkInfo getLastWork(Integer userId, Integer courseId, Integer companyId) {
        return baseMapper.getLastWork(userId, courseId, companyId);
    }

    @Override
    public WorkInfoPojo todayWorkInfo(Integer courseId, HeaderUserInfo headerUserInfo) {
        CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(courseId, null);
        log.info("todayWork courseTimetableUnlockDetailVO:{}", JSON.toJSONString(courseTimetableUnlockDetailVO));
        return workInfoPojoMongoService.querySimpleOne(new WorkInfoPojoQueryDTO().setUserId(headerUserInfo.getId()).setCourseId(courseId)
                .setCompanyId(headerUserInfo.getCurrentCompanyId()).setCourseTimetableId(courseTimetableUnlockDetailVO.getId()));
    }

    @Override
    public int workMySQLtoMongo(List<Integer> courseIdList) {
        if (CollUtil.isEmpty(courseIdList)) return -1;
        int count = 0;
        for (Integer courseId : courseIdList) {
            log.info("workMySQLtoMongo 开始,courseId:{}", courseId);
            int pageNum = 0;
            int pageSize = 100;
            Page<WorkInfo> page;
            do {
                pageNum++;
                log.info("开始新增，当前页数:{}", pageNum);
                page = page(new Page<>(pageNum, pageSize), new LambdaQueryWrapper<WorkInfo>()
                        .eq(WorkInfo::getCourseId, courseId));
                if (CollUtil.isEmpty(page.getRecords())) {
                    log.info("未查询到作业,courseId:{}", courseId);
                    break;
                }
                List<Integer> workIdList = page.getRecords().stream().map(WorkInfo::getId).collect(Collectors.toList());
                Map<Integer, WorkInfoPojo> pojoMap = workInfoPojoMongoService.queryByWorkIdList(workIdList)
                        .stream().collect(Collectors.toMap(WorkInfoPojo::getWorkId, Function.identity()));
                List<WorkInfoPojo> insertList = page.getRecords().stream().filter(workInfo -> pojoMap.get(workInfo.getId()) == null)
                        .map(workInfo -> {
                            WorkInfoPojo workInfoPojo = BeanUtil.copyProperties(workInfo, WorkInfoPojo.class, "serialVersionUID");
                            workInfoPojo.setWorkId(workInfo.getId());
                            return workInfoPojo;
                        })
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(insertList)) {
                    workInfoPojoMongoService.insertAll(insertList);
                }
                count += insertList.size();
                log.info("结束新增，当前页数:{}", pageNum);
            }
            while (page.hasNext());
            log.info("workMySQLtoMongo 结束,courseId:{}", courseId);
        }
        return count;
    }

    public WorkModelVO workModelBuild(Integer workModelId, Integer workModelFlag, String workModelName, List<WorkModelDetail> workModelDetails) {
        WorkModelVO workModelVO = new WorkModelVO();
        workModelVO.setWorkModelId(workModelId);
        workModelVO.setWorkModelName(workModelName);
        workModelVO.setWorkModelFlag(workModelFlag);
        WorkModelFieldVO workModelFieldVO = new WorkModelFieldVO();
        if (CollUtil.isNotEmpty(workModelDetails)) {
            for (int i = 0; i < workModelDetails.size(); i++) {
                if (i == 0) {
                    workModelFieldVO.setWorkContentOne(workModelDetails.get(i).getTitle());
                }
                if (i == 1) {
                    workModelFieldVO.setWorkContentTwo(workModelDetails.get(i).getTitle());
                }

                if (i == 2) {
                    workModelFieldVO.setWorkContentThree(workModelDetails.get(i).getTitle());
                }
                if (i == 3) {
                    workModelFieldVO.setWorkContentFour(workModelDetails.get(i).getTitle());
                }
                if (i == 4) {
                    workModelFieldVO.setWorkContentFive(workModelDetails.get(i).getTitle());
                }
            }
        }
        workModelVO.setFieldVO(workModelFieldVO);
        return workModelVO;
    }

    @Override
    public PageInfoBT<OpenClassesWorkVO> getOpenClassesWork(OpenClessesWorkDTO openClessesWorkDTO) {
        QueryWrapper<WorkInfo> queryWrapper = new QueryWrapper();
        queryWrapper.lambda().eq(WorkInfo::getCourseId, 850)
                .eq(WorkInfo::getClassesNo, 1500682)
                .orderByDesc(WorkInfo::getId);
        IPage<OpenClassesWorkVO> page = this.page(openClessesWorkDTO.getPage(), queryWrapper).convert(v->{
            OpenClassesWorkVO openClassesWorkVO = new OpenClassesWorkVO();
            if (StringUtils.isNotEmpty(v.getContent())){
                List<WorkModelDetailVO> workModelDetailVOS = JSONObject.parseArray(v.getContent(), WorkModelDetailVO.class);
                if (CollectionUtil.isNotEmpty(workModelDetailVOS)) {
                    for (WorkModelDetailVO workModelDetailVO : workModelDetailVOS) {
                        Integer type = workModelDetailVO.getType();
                        String content = workModelDetailVO.getContent();
                        // 1听课类型2打卡
                        if (WorkDetailTypeEnum.LISTEN.getCode().equals(type)) {
                            //听课
                            if (StringUtils.isNotBlank(content) && "1".equals(content)){
                                openClassesWorkVO.setListen(1);
                            }else {
                                openClassesWorkVO.setListen(0);
                            }
                        } else if (WorkDetailTypeEnum.CLOCK.getCode().equals(type)) {
                            if (workModelDetailVO.getId() == 425){
                                //预习
                                openClassesWorkVO.setPreview(StringUtils.isNotBlank(content) && "1".equals(content) ? 1 : 0);
                            }
                            if (workModelDetailVO.getId() == 427){
                                //谎言
                                openClassesWorkVO.setLie(StringUtils.isNotBlank(content) && "1".equals(content) ? 1 : 0);
                            }
                            if (workModelDetailVO.getId() == 428){
                                //抱怨
                                openClassesWorkVO.setComplain(StringUtils.isNotBlank(content) && "1".equals(content) ? 1 : 0);
                            }
                            if (workModelDetailVO.getId() == 429){
                                //复盘
                                openClassesWorkVO.setReplay(StringUtils.isNotBlank(content) && "1".equals(content) ? 1 : 0);
                            }
                        } else if (WorkDetailTypeEnum.TEXT.getCode().equals(type)) {
                            //文本
                            if (workModelDetailVO.getId() == 430){
                                //执行
                                openClassesWorkVO.setNote(StringUtils.isNotBlank(content) ? content : "");
                            }
                        }
                        openClassesWorkVO.setCreateTime(v.getCreateTime());
                        openClassesWorkVO.setWorkTime(v.getWorkTime());
                        openClassesWorkVO.setUserName(v.getUserName());
                        openClassesWorkVO.setUserPhone(getYanZhengVal(v.getUserPhone()) ? v.getUserPhone() : AESUtil.decrypt(v.getUserPhone()));
                    }
                }
            }else {
                openClassesWorkVO.setListen(0);
                openClassesWorkVO.setPreview(0);
                openClassesWorkVO.setLie(0);
                openClassesWorkVO.setComplain(0);
                openClassesWorkVO.setReplay(0);
                openClassesWorkVO.setNote("");
                openClassesWorkVO.setCreateTime(v.getCreateTime());
                openClassesWorkVO.setWorkTime(v.getWorkTime());
                openClassesWorkVO.setUserName(v.getUserName());
                openClassesWorkVO.setUserPhone(getYanZhengVal(v.getUserPhone()) ? v.getUserPhone() : AESUtil.decrypt(v.getUserPhone()));
            }
            return openClassesWorkVO;
        });

        return PageInfoBT.fromPage(page);
    }

    @Override
    public CurWeekWorkVO curWeekWork(CurWeekWorkDTO dto) {
        LocalDate now = LocalDate.now();
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        orderComponent.queryUserRightsCourseByUserIdAndCourseId(dto.getCourseId(), headerUserInfo.getId());
        // 查询目标
        CurWeekWorkVO vo = new CurWeekWorkVO();
        WeekGoal weekGoal = weekGoalService.getOneByDate(dto.getCourseId(), now);
        if (Objects.nonNull(weekGoal)) {
            vo.setGoal(weekGoal.getGoal());
        }
        // 本周一
        LocalDate beginDate = now.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        // 本周五
        LocalDate endDate = beginDate.plusDays(4);
        // 获取作业信息
        Map<LocalDate, CttByCourseIdBeginDateEndDateVO> cttMap = courseComponent.cttByCourseIdBeginDateEndDate(dto.getCourseId(), beginDate, endDate);
        List<Integer> cttList = cttMap.values().stream().map(CttByCourseIdBeginDateEndDateVO::getId).collect(Collectors.toList());
        if (CollUtil.isEmpty(cttList)) {
            return vo;
        }
        // 已保存的作业模板信息
        Map<Integer, String> promptMap = promptMap(dto.getCourseId(), cttList);
        String defaultPrompt = getDefaultPrompt(dto.getCourseId(), cttList);
        List<WorkInfoPojo> workInfoList = workInfoPojoMongoService.weekWork(dto.getCourseId(), headerUserInfo.getId(), cttList);
        Map<LocalDate, WorkInfoPojo> workInfoMap = workInfoList.stream().collect(Collectors.toMap(w -> w.getCourseBeginTime().toLocalDate(), Function.identity()));
        Map<Integer, WorkTeacherReply> replyMap = workTeacherReplyService.listByWorkIdList(workInfoList.stream().map(WorkInfoPojo::getId).collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(WorkTeacherReply::getWorkId, Function.identity()));
        vo.setCurDate(now);
        List<WeekWorkItemVO> list = new ArrayList<>();
        vo.setItemList(list);
        packageWeekItemVOList(list, beginDate, endDate, cttMap, workInfoMap, replyMap, promptMap, defaultPrompt);
        return vo;
    }

    @Override
    public Integer recordThink(RecordThinkDTO dto) {
        WorkCardInfoDTO workCardInfoDTO = new WorkCardInfoDTO();
        workCardInfoDTO.setCourseId(dto.getCourseId());
        workCardInfoDTO.setCourseTimeTableId(dto.getCourseTimetableId());
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        UserRightsCourse userRightsCourse = orderComponent.queryUserRightsCourseByUserIdAndCourseId(dto.getCourseId(), headerUserInfo.getId());
        headerUserInfo.setCurrentCompanyId(userRightsCourse.getCompanyId());
        CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(dto.getCourseId(), dto.getCourseTimetableId());
        log.info("recordThink courseTimetableUnlockDetailVO:{}", JSON.toJSONString(courseTimetableUnlockDetailVO));
        if (Objects.isNull(courseTimetableUnlockDetailVO)) {
            log.info("recordThink 课程表信息未查询到-退出");
            throw new BusinessException("课程表信息未查询到");
        }
        CourseConfig courseConfig = courseComponent.courseConfigDetail(headerUserInfo.getCurrentCompanyId(), dto.getCourseId());
        log.info("recordThink courseConfig:{}", JSON.toJSONString(courseConfig));
        // 补交作业
        if (Objects.nonNull(courseConfig) && Objects.nonNull(dto.getCourseTimetableId()) && YesOrNoEnum.YES.getCode().equals(courseConfig.getMakeUpWork()) && LocalDateTime.now().isAfter(courseTimetableUnlockDetailVO.getWorkEndTime())) {
            if (!(LocalDateTime.now().isAfter(courseTimetableUnlockDetailVO.getWorkBeginTime()) && LocalDateTime.now().isBefore(courseTimetableUnlockDetailVO.getWorkEndTime().plusDays(courseConfig.getMakeUpWorkBeginWeek())))) {
                log.info("recordThink 当前时间:" + LocalDateTime.now() + ",提交作业开始时间:" + courseTimetableUnlockDetailVO.getWorkBeginTime() + ",提交作业结束时间:" + courseTimetableUnlockDetailVO.getWorkBeginTime().plusDays(courseConfig.getMakeUpWorkBeginWeek()) + ",不在作业提交时间范围内-退出.");
                throw new BusinessException("不在提交时间内");
            }
        } else {
            // 正常交作业
            if (!(LocalDateTime.now().isAfter(courseTimetableUnlockDetailVO.getWorkBeginTime()) && LocalDateTime.now().isBefore(courseTimetableUnlockDetailVO.getWorkEndTime()))) {
                log.info("recordThink 当前时间:" + LocalDateTime.now() + ",提交作业开始时间:" + courseTimetableUnlockDetailVO.getWorkBeginTime() + ",提交作业结束时间:" + courseTimetableUnlockDetailVO.getWorkEndTime() + ",不在作业提交时间范围内-退出.");
                throw new BusinessException("不在提交时间内");
            }
        }
        // 今日作业
        WorkCardInfoVO workCardInfoVO = workService.todayWork(workCardInfoDTO, headerUserInfo);
        if (Objects.equals(workCardInfoVO.getWorkStatus(), 1)) {
            throw new BusinessException("内容已提交");
        }
        DoWorkMsgDTO doWorkDTO = new DoWorkMsgDTO();
        doWorkDTO.setCourseId(dto.getCourseId());
        doWorkDTO.setCourseTimeTableId(dto.getCourseTimetableId());
        doWorkDTO.setUserId(headerUserInfo.getId());
        doWorkDTO.setUserName(headerUserInfo.getUserFullName());
        doWorkDTO.setAvatar(headerUserInfo.getAvatar());
        doWorkDTO.setPhone(headerUserInfo.getPhone());
        doWorkDTO.setTag(WorkKafkaMessageEnum.DO_WORK.getCode());
        List<DoWorkDetailDTO> list = workCardInfoVO.getWorkModelDetails().stream().map(i -> {
            DoWorkDetailDTO doWorkDetailDTO = BeanUtil.copyProperties(i, DoWorkDetailDTO.class);
            if (WorkDetailTypeEnum.TEXT.getCode().equals(i.getType())) {
                doWorkDetailDTO.setContent(dto.getContent());
            }
            return doWorkDetailDTO;
        }).collect(Collectors.toList());
        doWorkDTO.setWorkDetails(list);
        // 提交作业
        workService.syncDoWork(doWorkDTO);
        return 1;
    }

    @Override
    public PageInfoBT<HistoryWeekWorkItemVO> historyWeekWork(HistoryWeekWorkDTO dto) {
        LocalDate now = LocalDate.now();
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        LocalDate beginWeek = now.minusWeeks(4L * dto.getPageNum());
        LocalDate beginDate = beginWeek.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        LocalDate endDate = beginWeek.plusWeeks(3L).with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)).plusDays(4);
        // 课件信息
        Map<LocalDate, CttByCourseIdBeginDateEndDateVO> cttMap = courseComponent.cttByCourseIdBeginDateEndDate(dto.getCourseId(), beginDate, endDate);
        if (CollUtil.isEmpty(cttMap)) {
            return PageInfoBT.noData();
        }
        List<Integer> cttList = cttMap.values().stream().map(CttByCourseIdBeginDateEndDateVO::getId).collect(Collectors.toList());
        // 已保存的作业模板信息
        Map<Integer, String> promptMap = promptMap(dto.getCourseId(), cttList);
        String defaultPrompt = getDefaultPrompt(dto.getCourseId(), cttList);
        List<WeekGoal> weekGoalList = weekGoalService.listByBeginDateEndDate(dto.getCourseId(), beginDate, endDate);
        // 目标
        Map<LocalDate, String> goalMap = goalMap(weekGoalList);
        // 作业信息
        List<WorkInfoPojo> workInfoList = workInfoPojoMongoService.weekWork(dto.getCourseId(), headerUserInfo.getId(), cttList);
        Map<LocalDate, WorkInfoPojo> workInfoMap = workInfoList.stream().collect(Collectors.toMap(w -> w.getCourseBeginTime().toLocalDate(), Function.identity()));
        Map<Integer, WorkTeacherReply> replyMap = workTeacherReplyService.listByWorkIdList(workInfoList.stream().map(WorkInfoPojo::getId).collect(Collectors.toList()))
                .stream().collect(Collectors.toMap(WorkTeacherReply::getWorkId, reply -> reply,
                        (first, second) -> second));
        List<HistoryWeekWorkItemVO> historyItemList = new ArrayList<>();
        for (LocalDate i = endDate; i.isAfter(beginDate); i = i.minusWeeks(1)) {
            HistoryWeekWorkItemVO historyItemVO = new HistoryWeekWorkItemVO();
            historyItemVO.setGoal(goalMap.get(i));
            List<WeekWorkItemVO> itemList = new ArrayList<>();
            historyItemVO.setItemList(itemList);
            LocalDate monday = i.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
            LocalDate friday = monday.plusDays(4);
            packageWeekItemVOList(itemList, monday, friday, cttMap, workInfoMap, replyMap, promptMap, defaultPrompt);
            if (CollUtil.isNotEmpty(itemList)) {
                historyItemList.add(historyItemVO);
            }
        }
        PageInfoBT<HistoryWeekWorkItemVO> pageInfoBT = new PageInfoBT<>();
        pageInfoBT.setCurrent(Long.valueOf(dto.getPageNum()));
        pageInfoBT.setSize(Long.valueOf(dto.getPageSize()));
        pageInfoBT.setRecords(historyItemList);
        pageInfoBT.setTotal(1000L);
        return pageInfoBT;
    }

    private static Map<LocalDate, String> goalMap(List<WeekGoal> weekGoalList) {
        Map<LocalDate, String> goalMap = new HashMap<>();
        for (WeekGoal weekGoal : weekGoalList) {
            for (LocalDate i = weekGoal.getWeekBeginDate(); i.isAfter(weekGoal.getWeekEndDate()); i = i.plusDays(1)) {
                goalMap.put(i, weekGoal.getGoal());
            }
        }
        return goalMap;
    }

    private String getDefaultPrompt(Integer courseId, List<Integer> cttList) {
        CourseTimetableUnlockDetailVO courseTimetableUnlockDetailVO = courseComponent.courseTimetableUnlockDetail(courseId, cttList.get(0));
        if (Objects.isNull(courseTimetableUnlockDetailVO)) {
            return "";
        }
        WorkModelDetail workModelDetail = workModelDetailMapper.selectOne(new LambdaQueryWrapper<WorkModelDetail>()
                .eq(WorkModelDetail::getWorkModelId, courseTimetableUnlockDetailVO.getWorkModelId())
                .eq(WorkModelDetail::getType, WorkDetailTypeEnum.TEXT.getCode())
                .eq(WorkModelDetail::getDeleted, 1)
                .last("limit 1"));
        if (Objects.nonNull(workModelDetail)) {
            return workModelDetail.getPrompt();
        }
        return null;
    }

    private Map<Integer, String> promptMap(Integer courseId, List<Integer> cttList) {
        Map<Integer, List<CourseTimetableModelDetail>> cttDetailMap = courseTimetableModelDetailService.list(new LambdaQueryWrapper<CourseTimetableModelDetail>()
                        .eq(CourseTimetableModelDetail::getCourseId, courseId)
                        .in(CourseTimetableModelDetail::getCourseTimetableId, cttList))
                .stream().collect(Collectors.groupingBy(CourseTimetableModelDetail::getCourseTimetableId));
        return cttDetailMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> {
            CourseTimetableModelDetail courseTimetableModelDetail = entry.getValue().stream().filter(i -> WorkDetailTypeEnum.TEXT.getCode().equals(i.getType()))
                    .findAny().orElse(null);
            if (Objects.nonNull(courseTimetableModelDetail)) {
                return courseTimetableModelDetail.getPrompt();
            }
            return "";
        }));
    }

    @Override
    public Integer updateThink(RecordThinkDTO dto) {
        HeaderUserInfo headerUserInfo = HeaderUserInfoUtil.get();
        WorkInfo workInfo = getOne(new LambdaQueryWrapper<WorkInfo>()
                .eq(WorkInfo::getCourseId, dto.getCourseId())
                .eq(WorkInfo::getCourseTimetableId, dto.getCourseTimetableId())
                .eq(WorkInfo::getUserId, headerUserInfo.getId())
                .last("limit 1")
        );
        if (Objects.isNull(workInfo)) {
            throw new BusinessException("作业不存在");
        }
        if (Objects.equals(workInfo.getWorkStatus(), 0)) {
            return recordThink(dto);
        }
        if (Objects.equals(workInfo.getWorkStatus(), 1) && StrUtil.isBlank(workInfo.getContent())) {
            throw new BusinessException("内容为空，无法修改");
        }
        List<WorkModelDetailVO> contentList = JSON.parseArray(workInfo.getContent(), WorkModelDetailVO.class);
        contentList.stream().filter(v -> WorkDetailTypeEnum.TEXT.getCode().equals(v.getType()))
                .findAny().ifPresent(v -> {
                    v.setContent(dto.getContent());
                });
        workInfo.setContent(JSON.toJSONString(contentList));
        updateById(workInfo);
        workInfoPojoMongoService.saveOrUpdate(workInfo);
        return 1;
    }

    private void packageWeekItemVOList(List<WeekWorkItemVO> itemList, LocalDate beginDate, LocalDate endDate, Map<LocalDate, CttByCourseIdBeginDateEndDateVO> cttMap,
                                       Map<LocalDate, WorkInfoPojo> workInfoMap, Map<Integer, WorkTeacherReply> replyMap, Map<Integer, String> promptMap, String defaultPrompt) {
        for (LocalDate i = beginDate; i.isBefore(endDate) || i.isEqual(endDate); i = i.plusDays(1)) {
            WeekWorkItemVO item = new WeekWorkItemVO();
            item.setBelongDate(i);
            CttByCourseIdBeginDateEndDateVO cttByCourseIdBeginDateEndDateVO = cttMap.get(i);
            if (Objects.isNull(cttByCourseIdBeginDateEndDateVO) || Objects.isNull(cttByCourseIdBeginDateEndDateVO.getWorkBeginTime())) {
                continue;
            }
            if (Objects.nonNull(cttByCourseIdBeginDateEndDateVO.getWorkBeginTime())) {
                item.setCourseTimetableId(cttByCourseIdBeginDateEndDateVO.getId());
                item.setWorkBeginTime(cttByCourseIdBeginDateEndDateVO.getWorkBeginTime());
            }
            WorkInfoPojo workInfoPojo = workInfoMap.get(i);
            if (Objects.nonNull(workInfoPojo)) {
                item.setWorkStatus(workInfoPojo.getWorkStatus());
                setContentForCurWeekWork(workInfoPojo, item);
                WorkTeacherReply workTeacherReply = replyMap.get(workInfoPojo.getId());
                if (Objects.nonNull(workTeacherReply)) {
                    item.setReplyName(workTeacherReply.getUserName());
                    item.setReplyContent(workTeacherReply.getTeacherContent());
                }
            }
            if (StrUtil.isBlank(item.getPrompt())) {
                item.setPrompt(promptMap.get(cttByCourseIdBeginDateEndDateVO.getId()));
            }
            if (StrUtil.isBlank(item.getPrompt())) {
                item.setPrompt(defaultPrompt);
            }
            itemList.add(item);
        }
    }

    private static void setContentForCurWeekWork(WorkInfoPojo workInfoPojo, WeekWorkItemVO item) {
        if (Objects.equals(workInfoPojo.getWorkStatus(), 1)) {
            if (StrUtil.isNotBlank(workInfoPojo.getContent())) {
                List<WorkModelDetailVO> contentDetailList = JSON.parseArray(workInfoPojo.getContent(), WorkModelDetailVO.class);
                contentDetailList.stream().filter(v -> WorkDetailTypeEnum.TEXT.getCode().equals(v.getType()))
                        .findAny().ifPresent(v -> {
                            item.setPrompt(v.getPrompt());
                            item.setContent(v.getContent());
                        });
            }
        }
    }

    @Value("${spring.datasource.url:}")
    private String url;
    @Value("${spring.datasource.username:}")
    private String user;
    @Value("${spring.datasource.password:}")
    private String password;
    /**
     * 手动替换手机号加密
     * */
    @Override
    public String replacePhone() {

//        String url = "************************************************************************************************************************************************";
//        String user = "sibuqu_dev";
//        String password = "b4o8pmtjoWTeJdB2RK5k";
        int total = 0;
        try {
            Connection connection = DriverManager.getConnection(url, user, password);
            Statement statement = connection.createStatement();
            ResultSet resultSet = statement.executeQuery("SELECT COUNT(*) FROM work_info");

            if (resultSet.next()) {
                total = resultSet.getInt(1);
            }

            int batchSize = 500;
            int totalPages = (int) Math.ceil((double) total / batchSize);

            for (int page = 1; page <= totalPages; page++) {
                int offset = (page - 1) * batchSize;
                // 使用 LIMIT 和 OFFSET 对数据进行分页查询

                ResultSet resultSet1 = statement.executeQuery("SELECT id,classes_teacher_phone FROM work_info LIMIT " + offset + " , " + batchSize * page);

                while (resultSet1.next()) {
                    // 处理每一条数据
                    int id = resultSet1.getInt("id");
                    String phone = resultSet1.getString("classes_teacher_phone");
                    // 进行其他处理
                    if (com.alibaba.nacos.common.utils.StringUtils.isNotEmpty(phone) && getYanZhengVal(phone)){
//                        String encryptPhone = AESUtil.encrypt(phone);
                        try {
                            WorkInfo workInfo = new WorkInfo();
                            workInfo.setClassesTeacherPhone(phone);
                            workInfo.setId(id);
                            this.baseMapper.updateById(workInfo);
                        }catch (Exception e){
                            log.info("更新手机号异常：" + e);
                        }

                    }
                }
            }
            resultSet.close();
            statement.close();
            connection.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "OK";
    }

    private Boolean getYanZhengVal(String str){
        String regex = "\\d+";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(str);
        if (matcher.matches()){
            //纯数字
            return true;
        }else {
            return false;
        }
    }
}
