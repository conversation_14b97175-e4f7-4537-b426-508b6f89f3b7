package com.sibuqu.work.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.work.dto.AddWorkTeacherReplyDTO;
import com.sibuqu.work.dto.WorkInfoListDTO;
import com.sibuqu.work.dto.WorkReplyPageDTO;
import com.sibuqu.work.entity.WorkTeacherReply;
import com.sibuqu.work.vo.admin.WorkTeacherReplyVO;

import java.util.List;

/**
 * <p>
 * 老师回复表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022年5月18日
 */
public interface WorkTeacherReplyService extends IService<WorkTeacherReply> {
    /**
     * 删除老师的回复
     * @param id
     * @return
     */
    public Boolean replayDelete(Integer id);

    List<WorkTeacherReply> queryByCourseId(Integer courseId);

    boolean workHasReply(Integer workId, Integer replyUserType);

    List<WorkTeacherReply> listByWorkIdList(List<Integer> workIdList);

    PageInfoBT<WorkTeacherReplyVO> workReplyPage(WorkReplyPageDTO dto);

    List<WorkTeacherReplyVO> workReplyPageExport(WorkReplyPageDTO dto);

    Integer workReplyHideShow(Integer id);

    Integer addWorkReply(AddWorkTeacherReplyDTO dto);
}
