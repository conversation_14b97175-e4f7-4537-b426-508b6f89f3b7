package com.sibuqu.work.feignclient.client;

import com.sibuqu.activity.vo.ebook.HeartReadListVO;
import com.sibuqu.base.common.result.ResultInfo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

@FeignClient(value = "sibuqu-activity-api")
public interface ActivityFeignClient {

    @ApiOperation("内部-心得列表-读书记录信息")
    @PostMapping("/appactivity/activity-ebook-chapter/heartReadList")
    ResultInfo<Map<Long, HeartReadListVO>> heartReadList(@RequestBody @Validated List<Long> readRecordIdList);

}
