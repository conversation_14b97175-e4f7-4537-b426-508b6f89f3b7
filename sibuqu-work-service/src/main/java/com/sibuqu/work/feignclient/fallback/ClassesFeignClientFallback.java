package com.sibuqu.work.feignclient.fallback;

import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.classes.dto.ClassesUserSearchDTO;
import com.sibuqu.classes.dto.CourseClassTeamSearchDTO;
import com.sibuqu.classes.dto.GetSimpleClassInfoDTO;
import com.sibuqu.classes.dto.InnerClassesInfoDTO;
import com.sibuqu.classes.dto.InnerClassesUserInfoListDTO;
import com.sibuqu.classes.dto.InnerMySimpleClassesDetailDTO;
import com.sibuqu.classes.dto.MemberListByCourseIdClassesNoTeamIdDTO;
import com.sibuqu.classes.dto.QueryByCourseIdUserIdListDTO;
import com.sibuqu.classes.dto.IsInTribeDTO;
import com.sibuqu.classes.vo.ClassTeacherVO;
import com.sibuqu.classes.vo.ClassesMemberInfoListVO;
import com.sibuqu.classes.vo.ClassesTeamDetailVO;
import com.sibuqu.classes.vo.GetSimpleClassInfoVO;
import com.sibuqu.classes.vo.InnerClassesInfoVO;
import com.sibuqu.classes.vo.InnerClassesUserInfoListVO;
import com.sibuqu.classes.vo.MemberListByCourseIdClassesNoTeamIdVO;
import com.sibuqu.classes.vo.MySimpleClassesDetailVO;
import com.sibuqu.classes.vo.QueryByCourseIdUserIdListVO;
import com.sibuqu.classes.vo.SimpleClassInfoVO;
import com.sibuqu.work.feignclient.client.ClassesFeignClient;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.sibuqu.base.common.result.ResultInfo.error;
import static com.sibuqu.base.common.result.ResultInfo.ok;

@Slf4j
@Component
public class ClassesFeignClientFallback implements FallbackFactory<ClassesFeignClient> {

    @Override
    public ClassesFeignClient create(Throwable throwable) {
        log.error(throwable.getMessage(), throwable);
        return new ClassesFeignClient(){
            @Override
            public ResultInfo<ClassesTeamDetailVO> getClassesTeamInfoByUser(CourseClassTeamSearchDTO detailDTO) {
                log.error("触发熔断..............下游班级服务接口不可用",throwable);
                return ok();
            }

            @Override
            public ResultInfo<List<ClassesMemberInfoListVO>> getUserByClassOrTeam(ClassesUserSearchDTO classUserSearchDTO) {
                log.error("触发熔断..............下游班级服务接口不可用",throwable);
                return ok(new ArrayList<>());
            }

            @Override
            public PageInfoBT<ClassesMemberInfoListVO> getUserByClassOrTeamPage(ClassesUserSearchDTO classUserSearchDTO) {
                return PageInfoBT.from(0,new ArrayList<>());
            }

            @Override
            public ResultInfo<ClassesTeamDetailVO> listenGetClassesTeamInfoByUser(CourseClassTeamSearchDTO detailDTO) {
                log.error("触发熔断..............下游班级服务接口不可用",throwable);
                throw new RuntimeException("触发熔断..............下游班级服务接口不可用");
            }

            @Override
            public List<ClassTeacherVO> getClassTeachers(String teacherName, Integer courseId, Integer companyId) {
                log.error("触发熔断..............下游班级服务接口不可用",throwable);
                return new ArrayList<>();
            }

            @Override
            public ResultInfo<List<GetSimpleClassInfoVO>> getSimpleClassInfo(GetSimpleClassInfoDTO dto) {
                log.error("触发熔断..............下游班级服务接口不可用",throwable);
                return ok(new ArrayList<>());
            }

            @Override
            public ResultInfo<Map<Integer, SimpleClassInfoVO>> simpleInfoByIdList(List<Integer> idList) {
                log.error("触发熔断.....simpleInfoByIdList.........下游班级服务接口不可用",throwable);
                return error();
            }

            @Override
            public ResultInfo<MySimpleClassesDetailVO> innerMySimpleClassesDetail(InnerMySimpleClassesDetailDTO dto) {
                log.error("触发熔断.....innerMySimpleClassesDetail.........下游班级服务接口不可用",throwable);
                return null;
            }

            @Override
            public ResultInfo<List<InnerClassesUserInfoListVO>> innerClassesUserInfoList(InnerClassesUserInfoListDTO dto) {
                log.error("触发熔断.....innerClassesUserInfoList.........下游班级服务接口不可用",throwable);
                return null;
            }

            @Override
            public ResultInfo<List<QueryByCourseIdUserIdListVO>> queryByCourseIdUserIdList(QueryByCourseIdUserIdListDTO dto) {
                log.info("触发熔断.........queryByCourseIdUserIdList.....下游班级服务接口不可用");
                return null;
            }

            @Override
            public ResultInfo<InnerClassesInfoVO> innerClassesInfo(InnerClassesInfoDTO dto) {
                log.info("触发熔断.........innerClassesInfo.....下游班级服务接口不可用");
                return null;
            }

            @Override
            public ResultInfo<List<MemberListByCourseIdClassesNoTeamIdVO>> memberListByCourseIdClassesNoTeamId(MemberListByCourseIdClassesNoTeamIdDTO dto) {
                log.info("触发熔断.........memberListByCourseIdClassesNoTeamId.....下游班级服务接口不可用");
                return null;
            }

            @Override
            public ResultInfo<Integer> memberCountByClassesId(Integer classesId) {
                log.info("触发熔断.........memberCountByClassesId.....下游班级服务接口不可用");
                return null;
            }

            @Override
            public ResultInfo<Boolean> isInTribe(IsInTribeDTO dto) {
                log.info("触发熔断.........isInTribe.....下游班级服务接口不可用");
                return ok(false);
            }

            @Override
            public ResultInfo<List<Integer>> getAllLeaderId(Integer courseId) {
                log.info("触发熔断.........getAllLeaderId.....下游班级服务接口不可用");
                return null;
            }

            @Override
            public ResultInfo<List<Integer>> getAllTeamLeaderId(Integer courseId) {
                log.info("触发熔断.........getAllTeamLeaderId.....下游班级服务接口不可用");
                return null;
            }

        };

    }
}
