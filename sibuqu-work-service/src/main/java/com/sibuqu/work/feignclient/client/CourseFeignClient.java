package com.sibuqu.work.feignclient.client;

import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.course.dto.courseconfig.CourseConfigDetailDTO;
import com.sibuqu.course.dto.courseinfo.CourseDetailDTO;
import com.sibuqu.course.dto.courseinfo.CourseInfoDTO;
import com.sibuqu.course.dto.courseinfo.CourseWorkModelDTO;
import com.sibuqu.course.dto.coursetimetable.*;
import com.sibuqu.course.dto.goodsinfo.GoodsInfoDetailDTO;
import com.sibuqu.course.dto.personal.app.CttByCourseIdBeginDateEndDateDTO;
import com.sibuqu.course.entity.CourseConfig;
import com.sibuqu.course.entity.CourseInfo;
import com.sibuqu.course.entity.CourseTimetable;
import com.sibuqu.course.vo.courseinfo.CourseInfoVO;
import com.sibuqu.course.vo.courseinfo.CourseLevelWorkModelVO;
import com.sibuqu.course.vo.coursetimetable.*;
import com.sibuqu.course.vo.goodsinfo.CourseGoodsDetailVO;
import com.sibuqu.course.vo.goodsinfo.GoodsIdListByCourseIdListVO;
import com.sibuqu.course.vo.goodsinfo.GoodsInfoDetailVO;
import com.sibuqu.course.vo.goodsinfo.InnoCourseGoodsDetailVO;
import com.sibuqu.course.vo.personal.app.CttByCourseIdBeginDateEndDateVO;
import com.sibuqu.work.feignclient.fallback.CourseFeignClientFallback;
import com.sibuqu.work.feignclient.vo.RetResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> @date 2021/10/13 16:36
 * @description 课程服务Feign接口
 */
@FeignClient(value = "sibuqu-course-api",fallbackFactory = CourseFeignClientFallback.class)
public interface CourseFeignClient {

    @PostMapping(value = "/appcourse/course-timetable/courseTimetableUnlockDetail")
    ResultInfo<CourseTimetableUnlockDetailVO> courseTimetableUnlockDetail(CourseTimetableDetailDTO detailDTO);

    @PostMapping(value = "/appcourse/course-timetable/getCourseHistoryTimetable")
    ResultInfo<List<CourseTimetableHistoryVO>> getCourseHistoryTimetable(CourseTimetableSearchDTO courseTimetableSearchDTO);

    @PostMapping(value = "/appcourse/course/courseConfigDetail")
    ResultInfo<CourseConfig> courseConfigDetail(CourseConfigDetailDTO courseConfigDetailDTO);

    @PostMapping(value = "/appcourse/course-timetable/getCourseHistoryTimetablePage")
    ResultInfo<PageInfoBT<CourseTimetable>> getCourseHistoryTimetablePage(CourseTimetableSearchDTO courseTimetableSearchDTO);

    @PostMapping(value = "/appcourse/course-timetable/inner/getCourseFutureTimetable")
    ResultInfo<List<Integer>> courTimeTableIdsByWorkModelId(CourseTimetableFutureSearchDTO searchDTO);

   // @ApiOperation(value = "内部服务-课程表详情", notes = "内部服务-课程表详情")
    @PostMapping("/appcourse/course-timetable/inner/courseTimetableDetail")
    ResultInfo<CourseTimetable> courseTimetableDetail(CourseTimetableSearchOneDTO detailDTO);
   // @ApiOperation(value = "内部调用-课程时间表id查课程表信息集合", notes = "内部调用-课程时间表id查课程表信息集合")
    @PostMapping("/appcourse/course-timetable/inner/getCourseTimetableSimpleByCourseTimetableIds")
    ResultInfo<List<CourseTimetableSimpleListVO>> getCourseTimetableSimpleByCourseTimetableIds(CourseTimetableListDTO searchDTO);

    @PostMapping(value = "/appcourse/course/courseConfigDetail")
    RetResult<CourseConfig> listenCourseConfigDetail(CourseConfigDetailDTO courseConfigDetailDTO);

    @PostMapping(value = "/appcourse/course/courseGoodsDetail")
    ResultInfo<CourseGoodsDetailVO> courseGoodsDetail(@RequestBody @Validated CourseDetailDTO detailDTO);

    @PostMapping("/appcourse/course/courseInfo")
    CourseInfo courseInfoById(CourseInfoDTO courseInfoDTO);

    @PostMapping("/appcourse/course-timetable/getMakeUpWorkCourseTimetableList")
    List<CourseTimetable> getMakeUpWorkCourseTimetableList(GetMakeUpWorkCourseTimetableDTO dto);

    @ApiOperation(value = "内部调用-获取补作业课程表包含不可完成的")
    @PostMapping("/appcourse/course-timetable/getMakeUpWorkCttList")
    List<GetMakeUpWorkCttListVO> getMakeUpWorkCttList(@RequestBody GetMakeUpWorkCttListDTO dto);

    @GetMapping("/appcourse/course-timetable/courseTimetablesByResourceTitle")
    List<CourseTimetable> courseTimetablesByResourceTitle(@RequestParam("courseId") String courseId,@RequestParam("resourceTitle") String resourceTitle);

    @GetMapping("/appcourse/course/getAllTogetherCourseIds")
    List<Integer> getAllTogetherCourseIds();

    @ApiOperation(value ="内部服务-课程等级作业模板", notes = "内部服务-课程等级作业模板")
    @PostMapping("/appcourse/course/courseLevelWorkModel")
    ResultInfo<List<CourseLevelWorkModelVO>> courseLevelWorkModel(@RequestBody @Validated CourseWorkModelDTO dto);

    @ApiOperation("内部调用-根据课程id和日期查询课件id")
    @PostMapping("/appcourse/course-timetable/queryByCourseIdAndDate")
    CourseTimetable queryByCourseIdAndDate(@RequestBody @Validated QueryByCourseIdAndDateDTO dto);

    @ApiOperation("内部服务-根据课程id列表获取商品id列表")
    @PostMapping("/appcourse/course/goodsIdListByCourseIdList")
    List<GoodsIdListByCourseIdListVO> goodsIdListByCourseIdList(@RequestBody List<Integer> courseIdList);

    @ApiOperation(value = "内部服务-批量查询作业时间内的课件", notes = "内部服务-批量查询作业时间内的课件")
    @PostMapping("/appcourse/course-timetable/inner/batchGetWorkingCourseTimetables")
    ResultInfo<List<CourseTimetableHistoryVO>> batchGetWorkingCourseTimetables(@RequestBody @Validated CourseTimetableWorkingBatchSearchDTO dto);
    @PostMapping(value = "/appcourse/goods_info/inner/goodsInfoDetail")
    ResultInfo<GoodsInfoDetailVO> innerGoodsInfoDetail(GoodsInfoDetailDTO searchDTO);
    @PostMapping(value = "/appcourse/unlock/inner/save")
    ResultInfo<Boolean> unlockByUidAndRid(CourseResourceUnlockUserDTO dto);

    @ApiOperation(value = "根据课程 id 开始结束日期查询课件列表")
    @PostMapping("/appcourse/course-timetable/cttByCourseIdBeginDateEndDate")
    ResultInfo<Map<LocalDate, CttByCourseIdBeginDateEndDateVO>> cttByCourseIdBeginDateEndDate(@RequestBody @Validated CttByCourseIdBeginDateEndDateDTO dto);

    @ApiOperation(value = "内部调用-批量查询用户所选课程学习方案")
    @PostMapping("/appcourse/course-timetable/batchGetUserPlanSchemeOption")
    ResultInfo<Map<Integer, String>> batchGetUserPlanSchemeOption(@RequestBody @Validated CourseStudyPlanUserBatchSearchDTO dto);

    @ApiOperation(value = "内部调用-批量查询课件归属课程学习方案")
    @PostMapping("/appcourse/course-timetable/batchGetTimetablePlanScheme")
    ResultInfo<Map<Integer, String>> batchGetTimetablePlanScheme(@RequestBody @Validated CourseStudyPlanTimetableBatchSearchDTO searchDTO);

    @ApiOperation(value = "内部调用-根据分组id列表查询分组名称")
    @PostMapping("/appcourse/personal/app/course/queryByCoursewareGroupIdList")
    ResultInfo<Map<Integer, String>> queryByCoursewareGroupIdList(@RequestBody @Validated List<Integer> coursewareGroupIdList);

    @ApiOperation("内部调用-根据课程id和日期查询课件id")
    @PostMapping("/appcourse/personal/app/course/courseTimetableInfo")
    ResultInfo<CourseTimetableInfoVO> courseTimetableInfo(@RequestBody @Validated CourseTimetableInfoDTO dto);

    @ApiOperation(value = "内部服务-战略学课程商品信息（临时用）", notes = "内部服务-战略学课程商品信息（临时用）")
    @PostMapping("/appcourse/course/inner/innoCourseInfo")
    ResultInfo<InnoCourseGoodsDetailVO> innoCourseInfo();
}

