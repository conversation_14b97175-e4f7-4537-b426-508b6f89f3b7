package com.sibuqu.work.feignclient.client;

import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.user.dto.classs.ClassMemberUserInfoDTO;
import com.sibuqu.user.dto.company.BatchGetCompanySimpleDTO;
import com.sibuqu.user.dto.course.CxktResDateQueryListDTO;
import com.sibuqu.user.dto.course.QueryCommitWorkUserIdDTO;
import com.sibuqu.user.dto.user.UserInfoCompanySearchDTO;
import com.sibuqu.user.dto.user.UserInfoEntityListSearchDTO;
import com.sibuqu.user.dto.user.UserInfoEntitySearchDTO;
import com.sibuqu.user.dto.user.UserInfoListSearchByPhoneDTO;
import com.sibuqu.user.dto.user.UserInfoListSearchByUserIdDTO;
import com.sibuqu.user.dto.user.UserInfoSearchByUserIdDTO;
import com.sibuqu.user.dto.user.UserInfoSearchDTO;
import com.sibuqu.user.vo.api.UserInfoVO;
import com.sibuqu.user.vo.classs.ClassMemberUserInfoVO;
import com.sibuqu.user.vo.company.BatchGetCompanySimpleVO;
import com.sibuqu.user.vo.course.CxktResDateListVO;
import com.sibuqu.user.vo.user.UserInfoCompanySearchVO;
import com.sibuqu.user.vo.user.UserInfoSearchSimpleVO;
import com.sibuqu.work.feignclient.fallback.UserFeignClientFallback;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(value = "sibuqu-user-api", fallbackFactory = UserFeignClientFallback.class)
public interface UserFeignClient {

    @PostMapping(value = "/appuser/user/search/userByUserIdCompanyId")
    ResultInfo<UserInfoCompanySearchVO> userByUserIdCompanyId(UserInfoCompanySearchDTO userInfoCompanySearchDTO);

    @PostMapping(value = "/appuser/user/search/userByUserIdCompanyId")
    ResultInfo<UserInfoCompanySearchVO> listenUserByUserIdCompanyId(UserInfoCompanySearchDTO userInfoCompanySearchDTO);


    @PostMapping(value = "/appuser/user/search/userSimpleByUserId")
    ResultInfo<UserInfoSearchSimpleVO> userSimpleByUserId(UserInfoSearchByUserIdDTO UserInfoSearchByUserIdDTO);
    @PostMapping(value = "/appuser/user/search/userSimpleListByUserIds")
    ResultInfo<List<UserInfoSearchSimpleVO>> userSimpleListByUserIds(UserInfoListSearchByUserIdDTO userInfoListSearchByUserIdDTO);

    @PostMapping(value = "/appuser/cxktResDate/selectCxtResDateByIds")
    ResultInfo<List<CxktResDateListVO>> selectCxtResDateByIds(CxktResDateQueryListDTO userInfoListSearchByUserIdDTO);


    /**@ApiOperation(value = "根据用户实体查询用户信息",notes = "根据用户实体查询用户信息")*/
    @PostMapping("/appuser/user/search/userByEntity")
   ResultInfo<UserInfoVO> userByEntity(UserInfoEntitySearchDTO userInfoSearchDto);


    /**@ApiOperation(value = "根据用户实体查询用户集合信息",notes = "根据用户实体查询用户集合信息")*/
    @PostMapping("/appuser/user/search/userByEntityList")
    ResultInfo<List<UserInfoVO>> userByEntityList(UserInfoEntityListSearchDTO userInfoSearchDto);

    /**@ApiOperation(value = "[后台管理系统]获取课程下班级的用户信息",notes = "[后台管理系统]获取课程下班级的用户信息")*/
    @PostMapping("/appuser/class/member/search/classMemberUserByEntity")
    ResultInfo<List<ClassMemberUserInfoVO>> classMemberUserByEntity(ClassMemberUserInfoDTO dto);

    @PostMapping("/appuser/classHworkInfo/queryCommitWorkUserId")
    @ApiOperation("查询已提交作业的用户id")
    ResultInfo<List<Integer>> queryCommitWorkUserId(@RequestBody QueryCommitWorkUserIdDTO dto);
    /**@ApiOperation(value = "[后台管理系统]根据课程ID获取课件信息",notes = "[后台管理系统]根据课程ID获取课件信息")*/
    @PostMapping(value = "/appuser/cxktResDate/queryCxktResDataByCourseId")
    ResultInfo<List<CxktResDateListVO>> queryCxktResDataByCourseId(CxktResDateQueryListDTO dto);
    /**@ApiOperation(value = "根据手机号集合查询用户信息",notes = "根据手机号集合查询用户信息")*/
    @PostMapping(value = "/appuser/user/search/userListByPhone")
    ResultInfo<List<UserInfoVO>> userListByPhone(UserInfoListSearchByPhoneDTO dto);

    @ApiOperation(value = "内部调用-企业 id 列表查询企业名称")
    @PostMapping("/appuser/company-info/inner/queryCompanyNameById")
    ResultInfo<Map<Integer,String>> queryCompanyNameById(@RequestBody @Validated List<Integer> idList);

    @ApiOperation(value = "内部调用-企业用户 id 列表查询企业内用户真实名称")
    @PostMapping("/appuser/company-user-info/inner/queryUserCompanyNameById")
    ResultInfo<Map<Integer, String>> queryUserCompanyNameById(@RequestParam("companyId") Integer companyId, @RequestParam("userIdList") List<Integer> userIdList);

    @ApiOperation(value = "内部服务-根据用户 id 列表查询用户昵称")
    @PostMapping("/appuser/user/userFullNameByIdList")
    ResultInfo<Map<Integer, String>> userFullNameByIdList(@RequestBody List<Integer> idList);

    @PostMapping(value = "/appuser/company-info/inner/batchGetCompanyCode")
    @ApiOperation(value = "[内部调用]批量获取企业基本信息", notes = "[内部调用]批量获取企业编码")
    List<BatchGetCompanySimpleVO> batchGetCompanySimple(
            @RequestBody @Validated BatchGetCompanySimpleDTO batchGetCompanyCodeDTO);

    @PostMapping(value = "/appuser/user/search/userByPhone")
    ResultInfo<UserInfoVO> selectValidUserByPhone(UserInfoSearchDTO userInfoSearchDto);

}
