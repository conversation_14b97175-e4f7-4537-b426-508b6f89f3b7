package com.sibuqu.work.feignclient.fallback;

import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.bidata.dto.CourseIdListByExcludeDTO;
import com.sibuqu.bidata.dto.GetPlayNumMapDTO;
import com.sibuqu.bidata.dto.UserDataLabelSearchDTO;
import com.sibuqu.bidata.dto.UserDoneActionInfoDTO;
import com.sibuqu.bidata.entity.UserDataLabel;
import com.sibuqu.bidata.entity.UserInfoEnriched;
import com.sibuqu.work.feignclient.client.BidataFeignClient;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class BidataFeignClientFallback implements FallbackFactory<BidataFeignClient> {
    @Override
    public BidataFeignClient create(Throwable throwable) {
        log.error(throwable.getMessage(), throwable);
        return new BidataFeignClient() {
            @Override
            public ResultInfo<Boolean> addUserDoneAction(UserDoneActionInfoDTO userDoneActionInfoDTO) {
                return null;
            }

            @Override
            public ResultInfo<Boolean> updateDoneAction(UserDoneActionInfoDTO userDoneActionInfoDTO) {
                return null;
            }
        };
    }
}
