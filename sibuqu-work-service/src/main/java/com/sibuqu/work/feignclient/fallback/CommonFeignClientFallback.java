package com.sibuqu.work.feignclient.fallback;

import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.common.dto.common.CommonLabelListQueryDTO;
import com.sibuqu.common.dto.common.InteractCommentAddDTO;
import com.sibuqu.common.dto.common.SearchCommentAndLikeTotalDTO;
import com.sibuqu.common.dto.content.GetContentListByDataIdSubDataIdDTO;
import com.sibuqu.common.vo.common.CommonLabelPageVO;
import com.sibuqu.common.vo.common.SearchCommentAndLikeTotalVO;
import com.sibuqu.common.vo.content.GetContentListByDataIdSubDataIdVO;
import com.sibuqu.work.dto.AppWorkCommentSearchDTO;
import com.sibuqu.work.feignclient.client.CommonFeignClient;
import com.sibuqu.work.vo.api.AppWorkCommentLikeListVO;
import com.sibuqu.work.vo.api.AppWorkCommentListVO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.sibuqu.base.common.result.ResultInfo.ok;

@Slf4j
@Component
public class CommonFeignClientFallback implements FallbackFactory<CommonFeignClient> {
    @Override
    public CommonFeignClient create(Throwable throwable) {
        return new CommonFeignClient(){
            @Override
            public ResultInfo sendComment(InteractCommentAddDTO commentAddDTO) {
                log.error("触发熔断..............下游公共服务接口不可用",throwable);
                return ok();
            }

            @Override
            public ResultInfo<Map<Integer, List<AppWorkCommentListVO>>> batchSearchCommentList(AppWorkCommentSearchDTO searchDTO) {
                log.error("触发熔断..............下游公共服务接口不可用",throwable);
                return ok();
            }

            @Override
            public ResultInfo<Map<Integer, List<AppWorkCommentLikeListVO>>> batchSearchLikeList(AppWorkCommentSearchDTO searchDTO) {
                log.error("触发熔断..............下游公共服务接口不可用",throwable);
                return ok();
            }

            @Override
            public List<SearchCommentAndLikeTotalVO> searchCommentAndLikeTotal(SearchCommentAndLikeTotalDTO dto) {
                log.error("触发熔断..............下游公共服务接口不可用",throwable);
                return new ArrayList<>();
            }

            @Override
            public ResultInfo<List<CommonLabelPageVO>> listCommonLabelByGoodId(CommonLabelListQueryDTO queryDTO) {
                log.error("触发熔断..............下游公共服务接口不可用",throwable);
                return ok();
            }

            @Override
            public ResultInfo<List<GetContentListByDataIdSubDataIdVO>> getContentListByDataIdSubDataId(GetContentListByDataIdSubDataIdDTO dto) {
                log.error("触发熔断..............下游公共服务接口不可用",throwable);
                return ok();
            }
        };
    }
}
