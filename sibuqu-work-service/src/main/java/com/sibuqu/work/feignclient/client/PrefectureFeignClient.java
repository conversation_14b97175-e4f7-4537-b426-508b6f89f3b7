package com.sibuqu.work.feignclient.client;


import com.sibuqu.prefecture.dto.WorkContentAsyncDTO;
import com.sibuqu.work.feignclient.fallback.PrefectureFeignClientFallback;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "sibuqu-prefecture-api",fallbackFactory = PrefectureFeignClientFallback.class)
public interface PrefectureFeignClient {


    @ApiOperation("服务内部调用-> 内容同步")
    @PostMapping("/appPrefecture/contentInfo/workContentAsync")
    void workContentAsyncPrefecture(@RequestBody WorkContentAsyncDTO workContentAsyncDTO);
}
