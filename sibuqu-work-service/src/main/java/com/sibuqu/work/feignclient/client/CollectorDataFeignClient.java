package com.sibuqu.work.feignclient.client;

import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.work.feignclient.fallback.CollectorDataFeignClientFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "sibuqu-data-collector", fallbackFactory = CollectorDataFeignClientFallback.class)
public interface CollectorDataFeignClient {

    /**
     * 根据id查询一致化信息
     *
     * @param id 表单 id
     * @return 响应结果
     */
    @RequestMapping("/data/query")
    ResultInfo query(@RequestParam(value = "id") String id);

}