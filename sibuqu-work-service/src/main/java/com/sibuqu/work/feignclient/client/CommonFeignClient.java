package com.sibuqu.work.feignclient.client;

import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.common.dto.common.CommonLabelListQueryDTO;
import com.sibuqu.common.dto.common.InteractCommentAddDTO;
import com.sibuqu.common.dto.common.SearchCommentAndLikeTotalDTO;
import com.sibuqu.common.vo.common.CommonLabelPageVO;
import com.sibuqu.common.vo.common.SearchCommentAndLikeTotalVO;
import com.sibuqu.common.dto.content.GetContentListByDataIdSubDataIdDTO;
import com.sibuqu.common.vo.content.GetContentListByDataIdSubDataIdVO;
import com.sibuqu.work.dto.AppWorkCommentSearchDTO;
import com.sibuqu.work.feignclient.fallback.CommonFeignClientFallback;
import com.sibuqu.work.vo.api.AppWorkCommentLikeListVO;
import com.sibuqu.work.vo.api.AppWorkCommentListVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> @date 2021/10/13 16:36
 * @description 课程服务Feign接口
 */
@FeignClient(value = "sibuqu-common-api",fallbackFactory = CommonFeignClientFallback.class)
public interface CommonFeignClient {

    @PostMapping(value = "/appcommon/interact/sendComment")
    ResultInfo sendComment(InteractCommentAddDTO commentAddDTO);

    @PostMapping(value = "/appcommon/interact/batchSearchCommentList")
    ResultInfo<Map<Integer, List<AppWorkCommentListVO>>> batchSearchCommentList(AppWorkCommentSearchDTO searchDTO);

    @PostMapping(value = "/appcommon/interact/batchSearchLikeList")
    ResultInfo<Map<Integer, List<AppWorkCommentLikeListVO>>> batchSearchLikeList(AppWorkCommentSearchDTO searchDTO);

    @ApiOperation(value = "根据关联的数据idList,查看评论数和点赞数")
    @PostMapping("/appcommon/interact/searchCommentAndLikeTotal")
    List<SearchCommentAndLikeTotalVO> searchCommentAndLikeTotal(@RequestBody @Validated SearchCommentAndLikeTotalDTO dto);
    @ApiOperation(value = "内部服务-根据商品id获取标签列表", notes = "内部服务-根据商品id获取标签列表")
    @PostMapping("/appcommon/common-label/listCommonLabelByGoodId")
    ResultInfo<List<CommonLabelPageVO>> listCommonLabelByGoodId(@RequestBody CommonLabelListQueryDTO queryDTO);

    @ApiOperation("内部调用-根据 dataIdSubDataId 获取内容列表")
    @PostMapping("/appcommon/common_content/getContentListByDataIdSubDataId")
    ResultInfo<List<GetContentListByDataIdSubDataIdVO>> getContentListByDataIdSubDataId(@RequestBody @Validated GetContentListByDataIdSubDataIdDTO dto);


}
