package com.sibuqu.work.feignclient.fallback;

import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.work.feignclient.client.CollectorDataFeignClient;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CollectorDataFeignClientFallback implements FallbackFactory<CollectorDataFeignClient> {
    @Override
    public CollectorDataFeignClient create(Throwable throwable) {

        return new CollectorDataFeignClient() {

            @Override
            public ResultInfo query(String id) {
                log.info("CollectorData 连接失败 query id:{},error:{}", id, throwable.getMessage(), throwable);
                return new ResultInfo();
            }
        };
    }
}