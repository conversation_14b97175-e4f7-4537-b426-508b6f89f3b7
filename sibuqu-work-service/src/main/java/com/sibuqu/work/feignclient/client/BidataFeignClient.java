package com.sibuqu.work.feignclient.client;

import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.bidata.dto.CourseIdListByExcludeDTO;
import com.sibuqu.bidata.dto.GetPlayNumMapDTO;
import com.sibuqu.bidata.dto.UserDataLabelSearchDTO;
import com.sibuqu.bidata.dto.UserDoneActionInfoDTO;
import com.sibuqu.bidata.entity.UserDataLabel;
import com.sibuqu.bidata.entity.UserInfoEnriched;
import com.sibuqu.work.feignclient.fallback.BidataFeignClientFallback;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

@FeignClient(name = "sibuqu-bidata-api", fallbackFactory = BidataFeignClientFallback.class)
public interface BidataFeignClient {

    @ApiOperation(value = "内部接口-批量获取用户数据标签", notes = "内部接口-批量获取用户数据标签")
    @PostMapping("/appbidata/userDoneAction/inner/add")
    ResultInfo<Boolean> addUserDoneAction(@RequestBody UserDoneActionInfoDTO userDoneActionInfoDTO);

    @ApiOperation(value = "内部接口-添加用户完成动作", notes = "内部接口-添加用户完成动作")
    @PostMapping("/inner/update")
    ResultInfo<Boolean> updateDoneAction(@RequestBody UserDoneActionInfoDTO userDoneActionInfoDTO);
}
