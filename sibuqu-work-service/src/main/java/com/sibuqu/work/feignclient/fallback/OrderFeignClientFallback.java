package com.sibuqu.work.feignclient.fallback;

import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.order.dto.auth.CheckUserHasAuthDTO;
import com.sibuqu.order.dto.auth.CountByCourseAndCompanyDTO;
import com.sibuqu.order.dto.auth.CountByUserAndCompanyDTO;
import com.sibuqu.order.dto.auth.GetBelongCompanyIdDTO;
import com.sibuqu.order.dto.auth.LegalRightCourseDTO;
import com.sibuqu.order.dto.trade.UserRightCourseInnerQueryDTO;
import com.sibuqu.order.entity.auth.LegalRightCourse;
import com.sibuqu.order.entity.auth.UserRightsCourse;
import com.sibuqu.work.feignclient.client.OrderFeignClient;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class OrderFeignClientFallback implements FallbackFactory<OrderFeignClient> {

    @Override
    public OrderFeignClient create(Throwable throwable) {
        return new OrderFeignClient() {

            @Override
            public Integer getCountByUserAndCompany(CountByUserAndCompanyDTO countByUserAndCompanyDTO) {
                log.error("触发熔断.........查询用户课程数.....下游订单服务接口不可用",throwable);
                return 0;
            }

            @Override
            public UserRightsCourse queryUserRightsCourseByUserIdAndCourseId(UserRightCourseInnerQueryDTO userRightCourseInnerQueryDTO) {
                log.error("触发熔断.........queryUserRightsCourseByUserIdAndCourseId.....下游订单服务接口不可用",throwable);
                return null;
            }

            @Override
            public ResultInfo<List<LegalRightCourse>> rightList(LegalRightCourseDTO searchDTO) {
                log.error("触发熔断.........rightList.....下游订单服务接口不可用",throwable);
                return ResultInfo.ok(new ArrayList<>());
            }

            @Override
            public Integer getBelongCompanyId(GetBelongCompanyIdDTO getBelongCompanyIdDTO) {
                log.error("触发熔断.........getBelongCompanyId.....下游订单服务接口不可用",throwable);
                return -1;
            }

            @Override
            public ResultInfo<Boolean> checkUserHasAuth(CheckUserHasAuthDTO dto) {
                log.error("触发熔断.........checkUserHasAuth.....下游订单服务接口不可用",throwable);
                return null;
            }
        };
    }
}
