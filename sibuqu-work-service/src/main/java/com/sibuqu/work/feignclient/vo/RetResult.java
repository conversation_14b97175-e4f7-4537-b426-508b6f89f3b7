//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.sibuqu.work.feignclient.vo;

import java.io.Serializable;

public class RetResult<T> implements Serializable {
    public int code = 0;
    private String msg;
    private T data;
    private long t;
    private boolean success;

    public int getCode() {
        return this.code;
    }

    public RetResult<T> setCode(int code) {
        this.code = code;
        this.success = code == 0;
        return this;
    }

    public String getMsg() {
        return this.msg;
    }

    public RetResult<T> setMsg(String msg) {
        this.msg = msg;
        return this;
    }

    public T getData() {
        return this.data;
    }

    public RetResult<T> setData(T data) {
        this.data = data;
        return this;
    }

    public RetResult(int code) {
        this.code = code;
        this.success = code == 0;
        this.t = System.currentTimeMillis();
    }

    public static RetResult succ() {
        return new RetResult(0);
    }

    public static RetResult result(int code) {
        return code == 0 ? succ() : error("ERROR");
    }

    public static <T> RetResult<T> error(String msg) {
        RetResult<T> res = new RetResult(-1);
        res.success = false;
        res.msg = msg;
        return res;
    }

    public RetResult(int code, T model) {
        this.code = code;
        this.data = model;
        this.success = code == 0;
        this.t = System.currentTimeMillis();
    }

    public static <T> RetResult<T> succ(T model) {
        RetResult<T> ret = new RetResult(0, "成功", model);
        ret.setSuccess(true);
        return ret;
    }

    public RetResult(int code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public boolean isSuccess() {
        return this.success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }



    public RetResult() {
    }
}
