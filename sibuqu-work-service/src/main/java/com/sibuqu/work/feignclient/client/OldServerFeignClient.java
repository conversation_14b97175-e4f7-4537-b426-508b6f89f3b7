package com.sibuqu.work.feignclient.client;

import com.sibuqu.classes.dto.CourseClassTeamSearchDTO;
import com.sibuqu.classes.vo.ClassesTeamDetailVO;
import com.sibuqu.work.dto.WorkRemindDTO;
import com.sibuqu.work.vo.api.AdditionalInfoVO;
import com.sibuqu.work.vo.api.JumpPageWorkRemindParamVO;
import com.sibuqu.work.vo.api.UserFlagInfoVO;
import com.sibuqu.work.webclient.model.dto.AdditionalInfoDTO;
import com.sibuqu.work.webclient.model.dto.ClassesTeamInfoByUserIdsDTO;
import com.sibuqu.work.webclient.model.dto.UserByClassOrTeamDTO;
import com.sibuqu.work.webclient.model.vo.UserPhonesDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

@FeignClient(name = "sibuqu-oldserver-api")
public interface OldServerFeignClient {


    @PostMapping("/cloud/additionalInfo")
    AdditionalInfoVO additionalInfo(AdditionalInfoDTO additionalInfoDTO);

    /**
     * 获取指定用户的班级小组信息
     */
    @PostMapping("/cloud/getClassesTeamInfoByUser")
    ClassesTeamDetailVO getClassesTeamInfoByUser(CourseClassTeamSearchDTO courseClassTeamSearchDTO);

    /**
     * 获取所有班级或小组成员信息
     */
    @PostMapping("/cloud/getUserByClassOrTeam")
    List<UserFlagInfoVO> getUserByClassOrTeam(UserByClassOrTeamDTO userByClassOrTeamDTO);

    @PostMapping("/cloud/getUserInfoByPhones")
    List<UserFlagInfoVO> getUserInfoByPhones(UserPhonesDTO userPhone);

    @PostMapping("/cloud/getClassesTeamInfoByUserIds")
    List<UserFlagInfoVO> getClassesTeamInfoByUserIds(ClassesTeamInfoByUserIdsDTO classesTeamInfoByUserIdsDTO);

    @GetMapping("/cloud/isTeacher/{courseId}/{userId}")
    Boolean isTeacher(@PathVariable("courseId") Integer courseId, @PathVariable("userId") Integer userId);

    @PostMapping("/api/v1/classGroup/jumpPageWorkRemindParam")
    JumpPageWorkRemindParamVO jumpPageWorkRemindParam(WorkRemindDTO dto);

    @GetMapping("/cloud/getClassPersonNum/{classNo}")
    Integer getClassPersonNum(@PathVariable("classNo") Integer classesNo);

}