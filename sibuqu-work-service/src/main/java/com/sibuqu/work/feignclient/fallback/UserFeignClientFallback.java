package com.sibuqu.work.feignclient.fallback;

import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.user.dto.classs.ClassMemberUserInfoDTO;
import com.sibuqu.user.dto.company.BatchGetCompanySimpleDTO;
import com.sibuqu.user.dto.course.CxktResDateQueryListDTO;
import com.sibuqu.user.dto.course.QueryCommitWorkUserIdDTO;
import com.sibuqu.user.dto.user.UserInfoCompanySearchDTO;
import com.sibuqu.user.dto.user.UserInfoEntityListSearchDTO;
import com.sibuqu.user.dto.user.UserInfoEntitySearchDTO;
import com.sibuqu.user.dto.user.UserInfoListSearchByPhoneDTO;
import com.sibuqu.user.dto.user.UserInfoListSearchByUserIdDTO;
import com.sibuqu.user.dto.user.UserInfoSearchByUserIdDTO;
import com.sibuqu.user.dto.user.UserInfoSearchDTO;
import com.sibuqu.user.vo.api.UserInfoVO;
import com.sibuqu.user.vo.classs.ClassMemberUserInfoVO;
import com.sibuqu.user.vo.company.BatchGetCompanySimpleVO;
import com.sibuqu.user.vo.course.CxktResDateListVO;
import com.sibuqu.user.vo.user.UserInfoCompanySearchVO;
import com.sibuqu.user.vo.user.UserInfoSearchSimpleVO;
import com.sibuqu.work.feignclient.client.UserFeignClient;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.sibuqu.base.common.result.ResultInfo.error;
import static com.sibuqu.base.common.result.ResultInfo.ok;

@Slf4j
@Component
public class UserFeignClientFallback implements FallbackFactory<UserFeignClient> {
    @Override
    public UserFeignClient create(Throwable throwable) {
        return new UserFeignClient() {
            @Override
            public ResultInfo<UserInfoCompanySearchVO> userByUserIdCompanyId(UserInfoCompanySearchDTO userInfoCompanySearchDTO) {
                log.error("触发熔断..............下游用户服务接口不可用",throwable);
                return ok();
            }

            @Override
            public ResultInfo<UserInfoCompanySearchVO> listenUserByUserIdCompanyId(UserInfoCompanySearchDTO userInfoCompanySearchDTO) {
                log.error("触发熔断listenUserByUserIdCompanyId..............下游用户服务接口不可用",throwable);
                throw new RuntimeException("触发熔断listenUserByUserIdCompanyId..............下游用户服务接口不可用");
            }

            @Override
            public ResultInfo<UserInfoSearchSimpleVO> userSimpleByUserId(UserInfoSearchByUserIdDTO UserInfoSearchByUserIdDTO) {
                log.error("触发熔断.....根据用户userId，获取用户信息.........下游用户服务接口不可用",throwable);
                return ok();
            }

            @Override
            public ResultInfo<List<UserInfoSearchSimpleVO>> userSimpleListByUserIds(UserInfoListSearchByUserIdDTO userInfoListSearchByUserIdDTO) {
                log.error("触发熔断.....根据用户userId集合，获取用户信息.........下游用户服务接口不可用",throwable);
                return ok();
            }

            @Override
            public ResultInfo<List<CxktResDateListVO>> selectCxtResDateByIds(CxktResDateQueryListDTO userInfoListSearchByUserIdDTO) {
                log.error("触发熔断....获取课程上课时间表..........下游用户服务接口不可用",throwable);
                return ok();
            }

            @Override
            public ResultInfo<UserInfoVO> userByEntity(UserInfoEntitySearchDTO userInfoSearchDto) {
                log.error("触发熔断....实体获取用户信息..........下游用户服务接口不可用",throwable);
                return ok();
            }

            @Override
            public ResultInfo<List<UserInfoVO>> userByEntityList(UserInfoEntityListSearchDTO userInfoSearchDto) {
                log.error("触发熔断....实体集合获取用户信息..........下游用户服务接口不可用",throwable);
                return ok();
            }

            @Override
            public ResultInfo<List<ClassMemberUserInfoVO>> classMemberUserByEntity(ClassMemberUserInfoDTO dto) {
                log.error("触发熔断....[后台管理系统]获取课程下班级的用户信息..........下游用户服务接口不可用",throwable);
                return ok();
            }

            @Override
            public ResultInfo<List<Integer>> queryCommitWorkUserId(QueryCommitWorkUserIdDTO dto) {
                log.error("触发熔断....user工程 查询已提交作业的用户id..........下游用户服务接口不可用",throwable);
                return ok();            }

            @Override
            public ResultInfo<List<CxktResDateListVO>> queryCxktResDataByCourseId(CxktResDateQueryListDTO dto) {
                log.error("触发熔断....[后台管理系统]根据课程ID查询课件信息..........下游用户服务接口不可用",throwable);
                return ok();
            }

            @Override
            public ResultInfo<List<UserInfoVO>> userListByPhone(UserInfoListSearchByPhoneDTO dto) {
                log.error("触发熔断....[内部调用]根据手机号集合查询用户信息..........下游用户服务接口不可用",throwable);
                return ok();
            }

            @Override
            public ResultInfo<Map<Integer, String>> queryCompanyNameById(List<Integer> idList) {
                log.error("触发熔断....queryCompanyNameById..........下游用户服务接口不可用",throwable);
                return error();
            }

            @Override
            public ResultInfo<Map<Integer, String>> queryUserCompanyNameById(Integer companyId, List<Integer> userIdList) {
                log.error("触发熔断...queryUserCompanyNameById........下游用户服务接口不可用",throwable);
                return error();
            }

            @Override
            public ResultInfo<Map<Integer, String>> userFullNameByIdList(List<Integer> idList) {
                log.error("触发熔断...userFullNameByIdList........下游用户服务接口不可用",throwable);
                return error();
            }

            @Override
            public List<BatchGetCompanySimpleVO> batchGetCompanySimple(BatchGetCompanySimpleDTO batchGetCompanyCodeDTO) {
                log.error("触发熔断...userFullNameByIdList........下游用户服务接口不可用",throwable);
                return new ArrayList<>();
            }

            @Override
            public ResultInfo<UserInfoVO> selectValidUserByPhone(UserInfoSearchDTO userInfoSearchDto) {
                log.error("触发熔断...selectValidUserByPhone........下游用户服务接口不可用",throwable);
                return null;
            }

        };
    }
}
