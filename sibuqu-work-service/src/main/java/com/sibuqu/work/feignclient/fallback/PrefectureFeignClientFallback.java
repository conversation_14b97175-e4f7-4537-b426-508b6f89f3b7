package com.sibuqu.work.feignclient.fallback;

import com.sibuqu.prefecture.dto.WorkContentAsyncDTO;
import com.sibuqu.work.feignclient.client.PrefectureFeignClient;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class PrefectureFeignClientFallback implements FallbackFactory<PrefectureFeignClient> {

    @Override
    public PrefectureFeignClient create(Throwable throwable) {
        return new PrefectureFeignClient() {
            @Override
            public void workContentAsyncPrefecture(WorkContentAsyncDTO workContentAsyncDTO) {
                log.error("触发熔断....发送消息..........下游prefecture服务接口不可用",throwable);
            }
        };
    }
}
