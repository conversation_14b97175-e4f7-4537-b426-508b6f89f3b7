package com.sibuqu.work.feignclient.client;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.classes.dto.ClassesUserSearchDTO;
import com.sibuqu.classes.dto.CourseClassTeamSearchDTO;
import com.sibuqu.classes.dto.GetSimpleClassInfoDTO;
import com.sibuqu.classes.dto.InnerClassesInfoDTO;
import com.sibuqu.classes.dto.InnerClassesUserInfoListDTO;
import com.sibuqu.classes.dto.InnerMySimpleClassesDetailDTO;
import com.sibuqu.classes.dto.MemberListByCourseIdClassesNoTeamIdDTO;
import com.sibuqu.classes.dto.QueryByCourseIdUserIdListDTO;
import com.sibuqu.classes.dto.IsInTribeDTO;
import com.sibuqu.work.dto.CheckUserIsTeamLeaderDTO;
import com.sibuqu.work.dto.QueryTribeUserRoleDTO;
import com.sibuqu.classes.vo.ClassTeacherVO;
import com.sibuqu.classes.vo.ClassesMemberInfoListVO;
import com.sibuqu.classes.vo.ClassesTeamDetailVO;
import com.sibuqu.classes.vo.GetSimpleClassInfoVO;
import com.sibuqu.classes.vo.InnerClassesInfoVO;
import com.sibuqu.classes.vo.InnerClassesUserInfoListVO;
import com.sibuqu.classes.vo.MemberListByCourseIdClassesNoTeamIdVO;
import com.sibuqu.classes.vo.MySimpleClassesDetailVO;
import com.sibuqu.classes.vo.QueryByCourseIdUserIdListVO;
import com.sibuqu.classes.vo.SimpleClassInfoVO;
import com.sibuqu.work.feignclient.fallback.ClassesFeignClientFallback;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@FeignClient(value = "sibuqu-classes-api", fallbackFactory = ClassesFeignClientFallback.class)
public interface ClassesFeignClient {

    @PostMapping(value = "/appclasses/classes-member/getClassesTeamInfoByUser")
    ResultInfo<ClassesTeamDetailVO> getClassesTeamInfoByUser(CourseClassTeamSearchDTO detailDTO);

    @PostMapping(value = "/appclasses/classes-member/getUserByClassOrTeam")
    ResultInfo<List<ClassesMemberInfoListVO>> getUserByClassOrTeam(ClassesUserSearchDTO classUserSearchDTO);

    @PostMapping(value = "/appclasses/classes-member/getUserByClassOrTeamPage")
    PageInfoBT<ClassesMemberInfoListVO> getUserByClassOrTeamPage(ClassesUserSearchDTO classUserSearchDTO);

    @PostMapping(value = "/appclasses/classes-member/getClassesTeamInfoByUser")
    ResultInfo<ClassesTeamDetailVO> listenGetClassesTeamInfoByUser(CourseClassTeamSearchDTO detailDTO);

    @GetMapping("/getClassTeachers")
    List<ClassTeacherVO> getClassTeachers(@RequestParam("teacherName") String teacherName, @RequestParam("courseId") Integer courseId, @RequestParam("companyId") Integer companyId);

    @ApiOperation(value = "内部服务接口-简单班级信息")
    @PostMapping("/appclasses/classes-member/getSimpleClassInfo")
    ResultInfo<List<GetSimpleClassInfoVO>> getSimpleClassInfo(@RequestBody @Validated GetSimpleClassInfoDTO dto);

    @ApiOperation(value = "根据班级 id 列表查询简单信息")
    @PostMapping("/appclasses/classes/simpleInfoByIdList")
    ResultInfo<Map<Integer, SimpleClassInfoVO>> simpleInfoByIdList(@RequestBody List<Integer> idList);

    @ApiOperation(value = "内部接口-我的班级简单详情")
    @PostMapping("/appclasses/app/v2/innerMySimpleClassesDetail")
    ResultInfo<MySimpleClassesDetailVO> innerMySimpleClassesDetail(@RequestBody @Validated InnerMySimpleClassesDetailDTO dto);

    @ApiOperation("内部接口-根据班级no查询班级人员信息")
    @PostMapping("/appclasses/app/classes-member/v2/innerClassesUserInfoList")
    ResultInfo<List<InnerClassesUserInfoListVO>> innerClassesUserInfoList(@RequestBody @Validated InnerClassesUserInfoListDTO dto);

    @ApiOperation("根据课程id和用户id列表查询")
    @PostMapping("/appclasses/app/classes-member/v2/queryByCourseIdUserIdList")
    ResultInfo<List<QueryByCourseIdUserIdListVO>> queryByCourseIdUserIdList(@RequestBody @Validated QueryByCourseIdUserIdListDTO dto);

    @ApiOperation("内部服务-班级详情")
    @PostMapping("/appclasses/app/v2/innerClassesInfo")
    ResultInfo<InnerClassesInfoVO> innerClassesInfo(@RequestBody @Validated InnerClassesInfoDTO dto);

    @ApiOperation("根据课程id班级no小组id查询班级成员信息")
    @PostMapping("/appclasses/app/classes-member/v2/memberListByCourseIdClassesNoTeamId")
    ResultInfo<List<MemberListByCourseIdClassesNoTeamIdVO>> memberListByCourseIdClassesNoTeamId(@RequestBody @Validated MemberListByCourseIdClassesNoTeamIdDTO dto);

    @ApiOperation("根据班级id查询班级成员数量")
    @PostMapping("/appclasses/app/classes-member/v2/memberCountByClassesId")
    ResultInfo<Integer> memberCountByClassesId(@RequestParam("classesId") Integer classesId);

    @ApiOperation("用户是否在部落里")
    @PostMapping("/appclasses/tribe/isInTribe")
    ResultInfo<Boolean> isInTribe(@RequestBody @Validated IsInTribeDTO dto);

    @ApiOperation("查询所有的组长id")
    @PostMapping("/appclasses/tribe/getAllLeaderId")
    ResultInfo<List<Integer>> getAllLeaderId(@RequestParam("courseId") Integer courseId);

    @ApiOperation("查询所有的团队负责人 id")
    @PostMapping("/appclasses/team/getAllTeamLeaderId")
    ResultInfo<List<Integer>> getAllTeamLeaderId(@RequestParam("courseId") Integer courseId);

    @ApiOperation("查询用户是否是团队负责人身份")
    @PostMapping("/appclasses/team/checkUserIsTeamLeader")
    ResultInfo<Map<Integer,Boolean>> checkUserIsTeamLeader(@RequestBody @Validated CheckUserIsTeamLeaderDTO dto);

    @ApiOperation("查询人员小组内身份")
    @PostMapping("/appclasses/tribe/queryTribeUserRole")
    ResultInfo<Map<Integer,Integer>> queryTribeUserRole(@RequestBody @Validated QueryTribeUserRoleDTO dto);

}
