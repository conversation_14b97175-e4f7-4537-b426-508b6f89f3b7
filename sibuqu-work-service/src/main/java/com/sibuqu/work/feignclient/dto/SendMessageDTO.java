package com.sibuqu.work.feignclient.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SendMessageDTO implements Serializable {

    @ApiModelProperty(value = "消息类型: 1 班主任催作业 2 班主任与学员联系 默认是1 便于后续消息模版化",dataType = "Integer")
    private Integer type = 1;

    @ApiModelProperty(value = "userIds 用户集合",dataType = "Integer")
    private List<Integer> userIds;
}
