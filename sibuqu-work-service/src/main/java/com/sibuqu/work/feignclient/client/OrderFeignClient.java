package com.sibuqu.work.feignclient.client;

import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.order.dto.auth.CheckUserHasAuthDTO;
import com.sibuqu.order.dto.auth.CountByCourseAndCompanyDTO;
import com.sibuqu.order.dto.auth.CountByUserAndCompanyDTO;
import com.sibuqu.order.dto.auth.GetBelongCompanyIdDTO;
import com.sibuqu.order.dto.auth.LegalRightCourseDTO;
import com.sibuqu.order.dto.trade.UserRightCourseInnerQueryDTO;
import com.sibuqu.order.entity.auth.LegalRightCourse;
import com.sibuqu.order.entity.auth.UserRightsCourse;
import com.sibuqu.work.feignclient.fallback.OrderFeignClientFallback;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(value = "sibuqu-order-api",fallbackFactory = OrderFeignClientFallback.class)
public interface OrderFeignClient {


    @ApiOperation(value = "内部接口-根据用户和企业获取权限数量",notes = "内部接口-根据用户和企业获取权限数量")
    @PostMapping("/apporder/user-rights-course/interior/getCountByUserAndCompany")
    Integer getCountByUserAndCompany(CountByUserAndCompanyDTO countByUserAndCompanyDTO);

    @ApiOperation(value = "[内部服务接口]-通过用户ID和课程ID查询权限信息", notes = "[内部服务接口]-通过用户ID和课程ID查询权限信息")
    @PostMapping(value = "/apporder/user-rights-course/interior/queryUserRightsCourseByUserIdAndCourseId")
    UserRightsCourse queryUserRightsCourseByUserIdAndCourseId(@RequestBody UserRightCourseInnerQueryDTO userRightCourseInnerQueryDTO);

    @PostMapping(value = "/apporder/user-rights-course/interior/rightList")
    ResultInfo<List<LegalRightCourse>> rightList(LegalRightCourseDTO searchDTO);

    @ApiOperation(value = "内部接口-获取企业id",notes = "内部接口-获取企业id")
    @PostMapping("/apporder/user-rights-course/interior/getBelongCompanyId")
    Integer getBelongCompanyId(@RequestBody GetBelongCompanyIdDTO getBelongCompanyIdDTO);

    @ApiOperation(value = "内部接口-校验用户是否有课程权限包括vip(单个商品)",notes = "内部接口-校验用户是否有课程权限包括vip(单个商品)")
    @PostMapping("/apporder/user-rights-course/interior/checkUserHasAuth")
    ResultInfo<Boolean> checkUserHasAuth(@RequestBody CheckUserHasAuthDTO dto);

}
