package com.sibuqu.work.feignclient.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * (CourseInfo)实体类
 *
 * <AUTHOR>
 * @since 2020-08-31 15:01:12
 */

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class CourseInfoVO {
    /**
     * 课程ID(course_id)
     */
    //@TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 课程目录ID
     */
    private Integer courseCatalogId;
    /**
     * 课程目录名称
     */
    private String courseCatalogName;
    /**
     * 课程主标题
     */
    private String courseTitle;
    /**
     * 课程类型1:时限课程 1普通课程
     */
    private Integer courseType;
    /**
     * 课程副标题
     */
    private String courseSubtitle;
    /**
     * 课程封面图
     */
    private String courseCoverUrl;
    /**
     * 课程缩略图
     */
    private String courseImgUrl;
    /**
     * 收费类别(0免费1收费),起标签作用
     */
    private String courseChargeType;
    /**
     * 学习天数
     */
    private Integer courseStudyDay;
    /**
     * 课件总数(1天会有多个)
     */
    private Integer courseCount;
    /**
     * 课程简介
     */
    private String courseSummary;
    /**
     * 课程详情
     */
    private String courseDesc;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 操作时间
     */
    private LocalDateTime updateTime;
    /**
     * 0禁用1启用，2上架，3下架，4预告中，5直播中，6直播结束，9删除
     */
    private String recStatus;

    /**
     * 课程状态（0未开课 1开课中 2已结课）
     */
    private Integer courseState;

    /**
     * 创建人id
     */
    private String createUserId;
    /**
     * 操作人id
     */
    private String updateUserId;

    /**
     * 课程学习开始时间
     */

    private LocalDate courseStartTime;
    /**
     * 课程学习结束时间
     */
    private LocalDate courseEndTime;

    /**
     * 悬浮窗状态 0隐藏 1显示
     */
    private String suspensionStatus;

    /**
     * 悬浮名称
     */
    private String suspensionName;

    /**
     * 悬浮跳转类型 1是自定义链接 2课件  3直播
     */
    private String suspensionType;

    /**
     * 悬浮窗跳转内容  课件和直播时是id
     */
    private String suspensionContent;


    //@ApiModelProperty(value = "音频模板: 1:标准模板 2:无列表模板 3:音频列表模板")
    /**
     * 音频模板: 1:标准模板 2:无列表模板 3:音频列表模板
     */
    private Integer  audioTemplate;

    private Integer workModelId;
}
