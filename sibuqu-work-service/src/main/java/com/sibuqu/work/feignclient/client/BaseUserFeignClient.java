package com.sibuqu.work.feignclient.client;

import com.sibuqu.base.common.result.ResultInfo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "sibuqu-baseuser-api")
public interface BaseUserFeignClient {

    @ApiOperation("内部接口-根据姓名查询用户id")
    @PostMapping("/appbaseuser/app/info/innoSelectUserIdByName")
    ResultInfo<Integer> innoSelectUserIdByName(@RequestBody String userFullName);

}
