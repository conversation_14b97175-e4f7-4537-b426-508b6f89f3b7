package com.sibuqu.work.feignclient.client;

import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.baseuser.dto.app.InnerUserInnoSaveDTO;
import com.sibuqu.baseuser.vo.inner.InnoUserInfoListVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

@FeignClient(value = "sibuqu-baseuser-api")
public interface BaseUserFeignClient {

    @ApiOperation("内部接口-根据姓名查询用户id")
    @PostMapping("/appbaseuser/app/info/innoSelectUserIdByName")
    ResultInfo<Integer> innoSelectUserIdByName(@RequestBody String userFullName);

    @ApiOperation("内部接口-根据手机号查询用户ID")
    @PostMapping("/appbaseuser/app/info/getUserIdByPhone")
    ResultInfo<Integer> getUserIdByPhone(@RequestBody String phone);

    @ApiOperation("内部接口-根据用户姓名查询用户ID列表")
    @PostMapping("/appbaseuser/app/info/getUserIdListByName")
    ResultInfo<List<Integer>> getUserIdListByName(@RequestBody String userName);

    @ApiOperation("内部接口-根据用户 id 列表查询用户昵称")
    @PostMapping("/appbaseuser/app/info/innoUserFullNameByUserIdList")
    ResultInfo<Map<Integer, String>> innoUserFullNameByUserIdList(@RequestBody List<Integer> idList);

    @ApiOperation(value = "内部接口-用户的个人信息修改")
    @PostMapping("/appbaseuser/app/info/inner/personalInfo/save")
    ResultInfo<Boolean> personalInfoSave(@RequestBody @Validated InnerUserInnoSaveDTO saveDTO);

    @ApiOperation("内部接口-inno用户的个人信息列表查询")
    @PostMapping("/appbaseuser/app/info/innoUserInfoList")
    ResultInfo<Map<Integer, InnoUserInfoListVO>> innoUserInfoList(@RequestBody List<Integer> idList);

    @ApiOperation("内部接口-根据用户 id 列表查询用户手机号")
    @PostMapping("/appbaseuser/app/info/userPhoneByUserIdList")
    ResultInfo<Map<Integer, String>> userPhoneByUserIdList(@RequestBody List<Integer> idList);

}
