package com.sibuqu.work.feignclient.fallback;

import com.alibaba.fastjson.JSON;
import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.course.dto.courseconfig.CourseConfigDetailDTO;
import com.sibuqu.course.dto.courseinfo.CourseDetailDTO;
import com.sibuqu.course.dto.courseinfo.CourseInfoDTO;
import com.sibuqu.course.dto.courseinfo.CourseWorkModelDTO;
import com.sibuqu.course.dto.coursetimetable.*;
import com.sibuqu.course.dto.goodsinfo.GoodsInfoDetailDTO;
import com.sibuqu.course.dto.personal.app.CttByCourseIdBeginDateEndDateDTO;
import com.sibuqu.course.entity.CourseConfig;
import com.sibuqu.course.entity.CourseInfo;
import com.sibuqu.course.entity.CourseTimetable;
import com.sibuqu.course.vo.courseinfo.CourseInfoVO;
import com.sibuqu.course.vo.courseinfo.CourseLevelWorkModelVO;
import com.sibuqu.course.vo.coursetimetable.*;
import com.sibuqu.course.vo.goodsinfo.CourseGoodsDetailVO;
import com.sibuqu.course.vo.goodsinfo.GoodsIdListByCourseIdListVO;
import com.sibuqu.course.vo.goodsinfo.GoodsInfoDetailVO;
import com.sibuqu.course.vo.goodsinfo.InnoCourseGoodsDetailVO;
import com.sibuqu.course.vo.personal.app.CttByCourseIdBeginDateEndDateVO;
import com.sibuqu.work.feignclient.client.CourseFeignClient;
import com.sibuqu.work.feignclient.vo.RetResult;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.sibuqu.base.common.result.ResultInfo.ok;

@Slf4j
@Component
public class CourseFeignClientFallback implements FallbackFactory<CourseFeignClient> {
    @Override
    public CourseFeignClient create(Throwable throwable) {
        return new CourseFeignClient(){

            @Override
            public ResultInfo<CourseTimetableUnlockDetailVO> courseTimetableUnlockDetail(CourseTimetableDetailDTO detailDTO) {
                log.error("触发熔断..............下游课程服务接口不可用",throwable);
                return ok();
            }

            @Override
            public ResultInfo<PageInfoBT<CourseTimetable>> getCourseHistoryTimetablePage(CourseTimetableSearchDTO courseTimetableSearchDTO){
                log.error("触发熔断..............下游课程服务接口不可用",throwable);
                return ok();
            }

            @Override
            public ResultInfo<List<Integer>> courTimeTableIdsByWorkModelId(CourseTimetableFutureSearchDTO searchDTO) {
                log.error("触发熔断..............下游课程服务接口不可用",throwable);
                return ok();
            }

            @Override
            public ResultInfo<CourseTimetable> courseTimetableDetail(CourseTimetableSearchOneDTO detailDTO) {
                log.error("触发熔断..............下游课程服务接口不可用",throwable);
                return ok();
            }

            @Override
            public ResultInfo<List<CourseTimetableSimpleListVO>> getCourseTimetableSimpleByCourseTimetableIds(CourseTimetableListDTO searchDTO) {
                log.error("触发熔断..............下游课程服务接口不可用",throwable);
                return null;
            }

            @Override
            public RetResult<CourseConfig> listenCourseConfigDetail(CourseConfigDetailDTO courseConfigDetailDTO) {
                log.error("听课打卡触发熔断  listenCourseConfigDetail..............下游课程服务接口不可用",throwable);
                throw new RuntimeException("课程服务异常");
            }

            @Override
            public ResultInfo<CourseGoodsDetailVO> courseGoodsDetail(CourseDetailDTO detailDTO) {
                log.error("听课打卡触发熔断  listenCourseConfigDetail..............下游课程服务接口不可用", throwable);
                throw new RuntimeException("课程服务异常");
            }

            @Override
            public CourseInfo courseInfoById(CourseInfoDTO courseInfoDTO) {
                log.error("触发熔断..............下游课程服务接口不可用",throwable);
                return null;
            }

            @Override
            public List<CourseTimetable> getMakeUpWorkCourseTimetableList(GetMakeUpWorkCourseTimetableDTO dto) {
                return new ArrayList<>();
            }

            @Override
            public List<GetMakeUpWorkCttListVO> getMakeUpWorkCttList(GetMakeUpWorkCttListDTO dto) {
                log.error("触发熔断.......getMakeUpWorkCttList.......下游课程服务接口不可用",throwable);
                return Collections.emptyList();
            }

            @Override
            public List<CourseTimetable> courseTimetablesByResourceTitle(String courseId, String resourceTitle) {
                log.error("触发熔断..............下游课程服务接口不可用",throwable);
                return new ArrayList<>();
            }

            @Override
            public List<Integer> getAllTogetherCourseIds() {
                return new ArrayList<>();
            }

            @Override
            public ResultInfo<List<CourseLevelWorkModelVO>> courseLevelWorkModel(CourseWorkModelDTO dto) {
                log.error("触发熔断..............下游课程服务接口不可用",throwable);
                return new ResultInfo<>();
            }

            @Override
            public CourseTimetable queryByCourseIdAndDate(QueryByCourseIdAndDateDTO dto) {
                log.error("触发熔断.....queryByCourseIdAndDate.........下游课程服务接口不可用",throwable);
                return new CourseTimetable();
            }

            @Override
            public List<GoodsIdListByCourseIdListVO> goodsIdListByCourseIdList(List<Integer> courseIdList) {
                log.error("触发熔断.....goodsIdListByCourseIdList.........下游课程服务接口不可用",throwable);
                return new ArrayList<>();
            }

            @Override
            public ResultInfo<List<CourseTimetableHistoryVO>> batchGetWorkingCourseTimetables(CourseTimetableWorkingBatchSearchDTO dto) {
                log.error("触发熔断.....batchGetWorkingCourseTimetables.........下游课程服务接口不可用",throwable);
                return ResultInfo.ok(new ArrayList<>());
            }

            @Override
            public ResultInfo<GoodsInfoDetailVO> innerGoodsInfoDetail(GoodsInfoDetailDTO searchDTO) {
                log.error("触发熔断.....innerGoodsInfoDetail.........下游课程服务接口不可用",throwable);
                return ResultInfo.ok();
            }

            @Override
            public ResultInfo<Boolean> unlockByUidAndRid(CourseResourceUnlockUserDTO dto) {
                log.error("触发熔断.....unlockByUidAndRid.........下游课程服务接口不可用",throwable);
                return ResultInfo.ok();
            }

            @Override
            public ResultInfo<Map<LocalDate, CttByCourseIdBeginDateEndDateVO>> cttByCourseIdBeginDateEndDate(CttByCourseIdBeginDateEndDateDTO dto) {
                log.error("触发熔断.....cttByCourseIdBeginDateEndDate.........下游课程服务接口不可用 dto:{}", JSON.toJSONString(dto));
                return null;
            }

            @Override
            public ResultInfo<Map<Integer, String>> batchGetUserPlanSchemeOption(CourseStudyPlanUserBatchSearchDTO dto) {
                log.error("触发熔断.....batchGetUserPlanSchemeOption.........下游课程服务接口不可用 dto:{}", JSON.toJSONString(dto));
                return null;
            }

            @Override
            public ResultInfo<Map<Integer, String>> batchGetTimetablePlanScheme(CourseStudyPlanTimetableBatchSearchDTO searchDTO) {
                log.error("触发熔断.....batchGetTimetablePlanScheme.........下游课程服务接口不可用 dto:{}", JSON.toJSONString(searchDTO));
                return null;
            }

            @Override
            public ResultInfo<Map<Integer, String>> queryByCoursewareGroupIdList(List<Integer> coursewareGroupIdList) {
                log.error("触发熔断.....queryByCoursewareGroupIdList.........下游课程服务接口不可用 dto:{}", JSON.toJSONString(coursewareGroupIdList));
                return null;
            }

            @Override
            public ResultInfo<CourseTimetableInfoVO> courseTimetableInfo(CourseTimetableInfoDTO dto) {
                log.error("触发熔断.....courseTimetableInfo.........下游课程服务接口不可用 dto:{}", JSON.toJSONString(dto));
                return null;
            }

            @Override
            public ResultInfo<InnoCourseGoodsDetailVO> innoCourseInfo() {
                return null;
            }


            @Override
            public ResultInfo<CourseConfig> courseConfigDetail(CourseConfigDetailDTO courseConfigDetailDTO){
                log.error("触发熔断..............下游课程服务接口不可用",throwable);
                return ok();
            }

            @Override
            public ResultInfo<List<CourseTimetableHistoryVO>> getCourseHistoryTimetable(CourseTimetableSearchDTO courseTimetableSearchDTO) {
                log.error("触发熔断..............下游课程服务接口不可用",throwable);

                return ok(new ArrayList<>());
            }

        };
    }
}
