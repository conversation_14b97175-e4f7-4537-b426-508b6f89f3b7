package com.sibuqu.work.kafka.consumer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.sibuqu.base.common.enums.IsDeleteEnum;
import com.sibuqu.course.vo.courseinfo.CourseLevelWorkModelVO;
import com.sibuqu.starter.logging.common.utils.LogUtils;
import com.sibuqu.work.bo.WorkLevelBO;
import com.sibuqu.work.componet.CollectorDataComponent;
import com.sibuqu.work.componet.CourseComponent;
import com.sibuqu.work.entity.WorkUserModel;
import com.sibuqu.work.service.WorkUserModelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
@RefreshScope
public class KafkaConsumer {

    @Autowired
    private CollectorDataComponent collectorDataComponent;

    @Autowired
    private WorkUserModelService workUserModelService;

    @Autowired
    private CourseComponent courseComponent;

    @KafkaListener(topics = "#{'${kafka.topic.workLevel:dataCollector-demo}'.split(',')}", groupId = "${kafka.groupId.workLevel:work-by-dataCollector-demo}")
    public void workLevel(String kafkaResult) {
        log.info("workLevel 收到消息:{}", kafkaResult);
        if (StrUtil.isBlank(kafkaResult)) {
            log.info("workLevel 消息为空");
            return;
        }
        try {
            WorkLevelBO bo = JSON.parseObject(kafkaResult, WorkLevelBO.class);
            Integer courseId = getWorkLevelTemplateIdList(bo.getTemplateId());
            log.info("workLevel courseId:{}", courseId);
            if (Objects.isNull(courseId)) {
                log.info("workLevel 不是专门获取作业模板等级的表单,不处理");
                return;
            }
            if (StrUtil.isBlank(bo.getId())) {
                log.info("workLevel bo.getId为空");
                return;
            }
            String level = collectorDataComponent.queryWorkLevel(bo.getId());
            LocalDateTime now = LocalDateTime.now();
            List<CourseLevelWorkModelVO> courseConfigWorkModelList = courseComponent.courseLevelWorkModel(courseId);
            if (CollUtil.isEmpty(courseConfigWorkModelList)) {
                log.info("workLevel courseConfigWorkModelList为空,courseId:{}", courseId);
                return;
            }
            for (CourseLevelWorkModelVO courseLevelWorkModelVO : courseConfigWorkModelList) {
                if (Objects.isNull(courseLevelWorkModelVO)) {
                    continue;
                }
                if (Objects.equals(courseLevelWorkModelVO.getLevel(), level)) {
                    WorkUserModel workUserModel = workUserModelService.getByUserIdAndCourseId(bo.getUserId(), courseId);
                    if (Objects.isNull(workUserModel) || IsDeleteEnum.YES.getCode().equals(workUserModel.getDeleteFlag())) {
                        workUserModel = new WorkUserModel();
                        workUserModel.setUserId(bo.getUserId());
                        workUserModel.setCourseId(courseId);
                        workUserModel.setLevel(level);
                        workUserModel.setWorkModelId(courseLevelWorkModelVO.getWorkModelId());
                        workUserModel.setWorkModelName(courseLevelWorkModelVO.getWorkModelName());
                        workUserModel.setCreateUserId(999999);
                        workUserModel.setCreateUserName("消息消费");
                        workUserModel.setCreateTime(now);
                        workUserModel.setUpdateUserId(999999);
                        workUserModel.setUpdateUserName("消息消费");
                        workUserModel.setUpdateTime(now);
                        workUserModelService.save(workUserModel);
                    } else {
                        workUserModel.setWorkModelId(courseLevelWorkModelVO.getWorkModelId());
                        workUserModel.setWorkModelName(courseLevelWorkModelVO.getWorkModelName());
                        workUserModel.setUpdateUserId(999999);
                        workUserModel.setUpdateUserName("消息消费");
                        workUserModel.setUpdateTime(now);
                        workUserModelService.updateById(workUserModel);
                    }
                }
            }
        } catch (Exception e) {
            LogUtils.error("字符串转换错误,字符串:{},报错:{}", kafkaResult, e.getMessage(), e);
        }
    }

    /**
     * 根据表单模板 id 获取课程 id
     */
    private Integer getWorkLevelTemplateIdList(String templateId) {
        switch (templateId) {
            // 阳明心学
            case "ntpl20231119223449wefnjdhc":
            case "ntpl20231121175059bclqcpie":
                return 548;
            // 道德经
            case "ntpl20231119123219bsrisftt":
            case "ntpl20231121174823khulqagg":
                return 549;
            // 测试环境 644
            case "ntpl20231216170540wcwdiboy":
                return 644;
            // 阳明心学
            case "ntpl20240614084614nklihynx":
            case "ntpl20240614084555uzdsistj":
            case "ntpl20240729163836fkvsgysg":
                return 641;
            // 道德经
            case "ntpl20240614084619iwbkxkul":
            case "ntpl20240614084601shpnuiyg":
            case "ntpl20240729163830dosljwvf":
                return 642;
            default:
                return null;
        }
    }

}
