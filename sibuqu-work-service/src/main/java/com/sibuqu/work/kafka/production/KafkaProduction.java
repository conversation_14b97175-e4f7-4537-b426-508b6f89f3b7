package com.sibuqu.work.kafka.production;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.inno.user.bo.UserPointsAddBO;
import com.sibuqu.common.bo.CommonKafkaMsgBO;
import com.sibuqu.common.dto.common.CommonPushMessageDTO;
import com.sibuqu.work.commom.constants.KafkaConstants;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class KafkaProduction {
    private final KafkaTemplate kafkaTemplate;

    @Value("${kafka.topic.common}")
    private String commonTopic;

    @Value("${kafka.topic.user_points_add}")
    private String userPointsAddTopic;

    @Async("kafkaExecutor")
    public void send(String topic,String key, JSONObject jsonObject) {
        kafkaTemplate.send(topic, key,jsonObject);
    }

    public void send(String topic, String value) {
        log.info("send kafka String msg, topic:{}, value:{}", topic, value);
        kafkaTemplate.send(topic,value);
    }

    public void send(String topic, Object value) {
        log.info("send kafka Object msg, topic:{}, value:{}", topic, value);
        kafkaTemplate.send(topic,value);
    }

    /**
     * 触发操作推送极光通知-kafka消息通知
     * @param pushMessageDTO
     */
    public void sendCommonPushSysMsg(CommonPushMessageDTO pushMessageDTO){
        CommonKafkaMsgBO commonKafkaMsgBO = new CommonKafkaMsgBO();
        commonKafkaMsgBO.setTag(KafkaConstants.PUSH_SYS_MSG_TAG);
        commonKafkaMsgBO.setCommonPushMessageDTO(pushMessageDTO);
        log.info("sendCommonPushSysMsg, bo:{}", JSON.toJSONString(commonKafkaMsgBO));
        kafkaTemplate.send(commonTopic, JSONObject.toJSON(commonKafkaMsgBO));
    }

    public void send(UserPointsAddBO userPointsAddBO){
        log.info("userPointsAddBO, bo:{}", JSON.toJSONString(userPointsAddBO));
        kafkaTemplate.send(userPointsAddTopic, JSONObject.toJSON(userPointsAddBO));
    }

}
