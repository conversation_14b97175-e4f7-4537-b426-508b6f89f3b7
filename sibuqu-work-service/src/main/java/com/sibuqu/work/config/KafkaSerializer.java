package com.sibuqu.work.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.SneakyThrows;
import org.apache.kafka.common.serialization.Serializer;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

public class KafkaSerializer implements Serializer {
    private static final String DATEFORMAT = "yyyy-MM-dd";
    private static final String DATETIMEFORMAT = "yyyy-MM-dd HH:mm:ss";

    @Override
    public void configure(Map map, boolean b) {

    }

    @SneakyThrows
    @Override
    public byte[] serialize(String s, Object o) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.registerModule(new Jdk8Module());
        new LocalDateSerializer(DateTimeFormatter.ofPattern(DATEFORMAT));
        new LocalDateDeserializer(DateTimeFormatter.ofPattern(DATEFORMAT));

        SimpleModule simpleModule = new SimpleModule();
        simpleModule.addSerializer(new LocalDateTimeSerializer(DateTimeFormatter.ofPattern(DATETIMEFORMAT)));
        simpleModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(DateTimeFormatter.ofPattern(DATETIMEFORMAT)));
        objectMapper.registerModule(simpleModule);
        return objectMapper.writeValueAsBytes(o);
    }

    @Override
    public void close() {

    }
}
