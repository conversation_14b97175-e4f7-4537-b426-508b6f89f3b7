package com.sibuqu.work.componet;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.base.common.utils.TryCatchUtil;
import com.sibuqu.base.common.exception.BusinessException;
import com.sibuqu.order.dto.auth.CheckUserHasAuthDTO;
import com.sibuqu.order.dto.auth.CountByCourseAndCompanyDTO;
import com.sibuqu.order.dto.auth.CountByUserAndCompanyDTO;
import com.sibuqu.order.dto.auth.GetBelongCompanyIdDTO;
import com.sibuqu.order.dto.auth.LegalRightCourseDTO;
import com.sibuqu.order.dto.trade.UserRightCourseInnerQueryDTO;
import com.sibuqu.order.entity.auth.LegalRightCourse;
import com.sibuqu.order.entity.auth.UserRightsCourse;
import com.sibuqu.work.feignclient.client.OrderFeignClient;
import com.sibuqu.work.webclient.client.OrderWebClient;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class OrderComponent {
    private final OrderWebClient orderWebClient;
    private final OrderFeignClient orderFeignClient;
    public Integer workModelIdByCourseId(Integer courseId){
        return orderWebClient.workModelIdByCourseId(courseId);
    }
    

    public List<Integer> courTimeTableIdsByWorkModelId(Integer modelId) {
        List<Integer> courTimeTableIds = orderWebClient.courTimeTableIdsByWorkModelId(modelId);
        String jsonObject = JSON.toJSONString(courTimeTableIds);
        return JSONObject.parseArray(jsonObject, Integer.class);
    }

    public Integer getCountByCourseAndCompany(Integer userId,Integer companyId){
        CountByUserAndCompanyDTO countByUserAndCompanyDTO = new CountByUserAndCompanyDTO();
        countByUserAndCompanyDTO.setCompanyId(companyId);
        countByUserAndCompanyDTO.setUserId(userId);
        return orderFeignClient.getCountByUserAndCompany(countByUserAndCompanyDTO);
    }

    @ApiOperation(value = "[内部服务接口]-通过用户ID和课程ID查询权限信息", notes = "[内部服务接口]-通过用户ID和课程ID查询权限信息")
    public UserRightsCourse queryUserRightsCourseByUserIdAndCourseId(Integer courseId, Integer userId) {
        boolean hasAuth = checkUserHasAuth(userId, courseId);
        if (!hasAuth) {
            throw new BusinessException("2003-暂无课程权限");
        }
        UserRightCourseInnerQueryDTO userRightCourseInnerQueryDTO = new UserRightCourseInnerQueryDTO();
        userRightCourseInnerQueryDTO.setCourseId(courseId);
        userRightCourseInnerQueryDTO.setUserId(userId);
        log.info("userRightCourseInnerQueryDTO:{}", JSON.toJSONString(userRightCourseInnerQueryDTO));
        UserRightsCourse userRightsCourse = orderFeignClient.queryUserRightsCourseByUserIdAndCourseId(userRightCourseInnerQueryDTO);
        log.info("userRightsCourse:{}", JSON.toJSONString(userRightsCourse));
        if (userRightsCourse == null) {
            userRightsCourse = new UserRightsCourse();
            userRightsCourse.setCompanyId(-1);
        }
        return userRightsCourse;
    }

    /**
     * 获取学员有权限的课
     */
    public List<LegalRightCourse> rightList(Integer userId) {
        LegalRightCourseDTO searchDTO = new LegalRightCourseDTO();
        searchDTO.setUserId(userId);
        log.info("rightList begin userId:{}", userId);
        List<LegalRightCourse> list = TryCatchUtil.getAndUnpackResultInfo(() -> orderFeignClient.rightList(searchDTO), ArrayList::new);
        log.info("rightList end");
        return list;
    }

    @ApiOperation(value = "内部接口-获取企业id", notes = "内部接口-获取企业id")
    public Integer getBelongCompanyId(Integer userId, Integer courseId) {
        GetBelongCompanyIdDTO getBelongCompanyIdDTO = new GetBelongCompanyIdDTO();
        getBelongCompanyIdDTO.setUserId(userId);
        getBelongCompanyIdDTO.setCourseId(courseId);
        log.info("getBelongCompanyId getBelongCompanyIdDTO:{}", JSON.toJSONString(getBelongCompanyIdDTO));
        Integer companyId = TryCatchUtil.get(() -> orderFeignClient.getBelongCompanyId(getBelongCompanyIdDTO), () -> -1);
        log.info("getBelongCompanyId companyId:{}", companyId);
        return companyId;
    }

    @ApiOperation(value = "内部接口-校验用户是否有课程权限包括vip(单个商品)")
    public boolean checkUserHasAuth(Integer userId, Integer courseId) {
        CheckUserHasAuthDTO dto = new CheckUserHasAuthDTO();
        dto.setUserId(userId);
        dto.setCourseId(courseId);
        log.info("checkUserHasAuth dto:{}", JSON.toJSONString(dto));
        if (Objects.equals(dto.getCourseId(), 0)) {
            log.info("checkUserHasAuth courseId is 0.");
            return false;
        }
        Boolean hasAuth = TryCatchUtil.getAndUnpackResultInfo(() -> orderFeignClient.checkUserHasAuth(dto));
        log.info("checkUserHasAuth hasAuth:{}", hasAuth);
        return hasAuth;
    }

}
