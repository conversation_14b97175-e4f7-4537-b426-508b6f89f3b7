package com.sibuqu.work.componet;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.base.common.utils.TryCatchUtil;
import com.sibuqu.common.dto.common.CommonLabelListQueryDTO;
import com.sibuqu.common.dto.common.InteractCommentAddDTO;
import com.sibuqu.common.dto.common.SearchCommentAndLikeTotalDTO;
import com.sibuqu.common.dto.content.GetContentListByDataIdSubDataIdDTO;
import com.sibuqu.common.vo.common.CommonLabelPageVO;
import com.sibuqu.common.vo.common.SearchCommentAndLikeTotalVO;
import com.sibuqu.common.vo.content.GetContentListByDataIdSubDataIdVO;
import com.sibuqu.work.dto.AppWorkCommentSearchDTO;
import com.sibuqu.work.feignclient.client.CommonFeignClient;
import com.sibuqu.work.vo.api.AppWorkCommentLikeListVO;
import com.sibuqu.work.vo.api.AppWorkCommentListVO;
import com.sibuqu.work.vo.api.CommonLabelVO;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class CommentComponent {
    private final CommonFeignClient commonFeignClient;

    @Async("sendCommentExecutor")
    public void sendComment(String content, Integer classesId, Integer userId, String userName, Integer dateId,String avatar,String phone,Integer companyId){
        log.info("同步评论start");
        InteractCommentAddDTO interactCommentAddDTO = new InteractCommentAddDTO();
        interactCommentAddDTO.setCommentType(1);
        interactCommentAddDTO.setContent(content);
        interactCommentAddDTO.setSendType(1);
        interactCommentAddDTO.setClassesId(classesId);
        interactCommentAddDTO.setDataId(dateId);
        interactCommentAddDTO.setParentId(0);
        interactCommentAddDTO.setUserId(userId);
        interactCommentAddDTO.setUserName(userName);
        interactCommentAddDTO.setUserAvatar(avatar);
        interactCommentAddDTO.setUserPhone(phone);
        interactCommentAddDTO.setCompanyId(companyId);
        ResultInfo resultInfo = commonFeignClient.sendComment(interactCommentAddDTO);
        log.info(resultInfo.getCode()+resultInfo.getMsg());
        log.info("同步评论end");
    }

    public Map<Integer, List<AppWorkCommentListVO>> batchSearchCommentList(List<Integer> workIds, Integer userId){
        Map<Integer, List<AppWorkCommentListVO>> result = new HashMap<>();
        AppWorkCommentSearchDTO searchDTO = new AppWorkCommentSearchDTO();
        searchDTO.setWorkIds(workIds);
        searchDTO.setUserId(userId);
        result = commonFeignClient.batchSearchCommentList(searchDTO).getData();
        return result;
    }

    public Map<Integer, List<AppWorkCommentLikeListVO>> batchSearchLikeList(List<Integer> workIds, Integer userId){
        Map<Integer, List<AppWorkCommentLikeListVO>> result = new HashMap<>();
        AppWorkCommentSearchDTO searchDTO = new AppWorkCommentSearchDTO();
        searchDTO.setWorkIds(workIds);
        searchDTO.setUserId(userId);
        result = commonFeignClient.batchSearchLikeList(searchDTO).getData();
        return result;
    }

    @ApiOperation(value = "根据关联的数据idList,查看评论数和点赞数")
    public Map<Integer, SearchCommentAndLikeTotalVO> searchCommentAndLikeTotal(SearchCommentAndLikeTotalDTO dto) {
        log.info("searchCommentAndLikeTotal start");
        List<SearchCommentAndLikeTotalVO> searchCommentAndLikeTotalVOList = TryCatchUtil.get(() -> commonFeignClient.searchCommentAndLikeTotal(dto));
        if (CollUtil.isEmpty(searchCommentAndLikeTotalVOList)) {
            return new HashMap<>();
        }
        log.info("searchCommentAndLikeTotal end");
        return searchCommentAndLikeTotalVOList.stream().collect(Collectors.toMap(SearchCommentAndLikeTotalVO::getDataId, Function.identity(), (k1, k2) -> k1));
    }
    @ApiOperation(value = "根据商品id获取标签列表")
    public List<CommonLabelPageVO> listCommonLabelByGoodId(Integer goodsId, List<Integer> labelTypes) {
        log.info("listCommonLabelByGoodId in:{},{}", goodsId,labelTypes);
        CommonLabelListQueryDTO queryDTO = new CommonLabelListQueryDTO();
        queryDTO.setGoodsId(goodsId);
        queryDTO.setLabelTypes(labelTypes);

        ResultInfo<List<CommonLabelPageVO>> commonLabelPageVO = TryCatchUtil.get(
                () -> commonFeignClient.listCommonLabelByGoodId(queryDTO));
        if (CollectionUtil.isEmpty(commonLabelPageVO.getData())) {
            return new ArrayList<>();
        }
        log.info("listCommonLabelByGoodId out:{}", JSON.toJSONString(commonLabelPageVO.getData()));

        return commonLabelPageVO.getData();
    }

    public List<GetContentListByDataIdSubDataIdVO> getContentListByDataIdSubDataId(Integer dataType,Long dataId, Long subDataId,Integer userId) {
        GetContentListByDataIdSubDataIdDTO dto = new GetContentListByDataIdSubDataIdDTO();
        dto.setDataType(dataType);
        dto.setDataId(dataId);
        dto.setSubDataId(subDataId); 
        dto.setUserId(userId);
        log.info("getContentListByDataIdSubDataId dto:{}", JSON.toJSONString(dto));
        List<GetContentListByDataIdSubDataIdVO> result = TryCatchUtil.getAndUnpackResultInfo(() -> commonFeignClient.getContentListByDataIdSubDataId(dto));
        log.info("getContentListByDataIdSubDataId end");
        return result;
    }

}
