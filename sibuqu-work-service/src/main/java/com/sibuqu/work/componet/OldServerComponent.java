package com.sibuqu.work.componet;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sibuqu.classes.dto.CourseClassTeamSearchDTO;
import com.sibuqu.classes.vo.ClassesTeamDetailVO;
import com.sibuqu.work.dto.WorkRemindDTO;
import com.sibuqu.work.feignclient.client.OldServerFeignClient;
import com.sibuqu.work.util.AESUtil;
import com.sibuqu.work.vo.api.AdditionalInfoVO;
import com.sibuqu.work.vo.api.JumpPageWorkRemindParamVO;
import com.sibuqu.work.vo.api.UserFlagInfoVO;
import com.sibuqu.work.webclient.model.dto.AdditionalInfoDTO;
import com.sibuqu.work.webclient.model.dto.ClassesTeamInfoByUserIdsDTO;
import com.sibuqu.work.webclient.model.dto.UserByClassOrTeamDTO;
import com.sibuqu.work.webclient.model.vo.UserPhonesDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Component
public class OldServerComponent {

    @Autowired
    private OldServerFeignClient oldServerFeignClient;

    public AdditionalInfoVO additionalInfo(Integer courseId, LocalDate belongDate){
        AdditionalInfoDTO additionalInfoDTO = new AdditionalInfoDTO();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        additionalInfoDTO.setCourseId(courseId);
        additionalInfoDTO.setBelongDate(belongDate.format(dateTimeFormatter));
        return oldServerFeignClient.additionalInfo(additionalInfoDTO);
    }

    public ClassesTeamDetailVO getClassesTeamInfoByUser(Integer companyId, Integer courseId, Integer userId) {
        log.info("getClassesTeamInfoByUser begin companyId:{},courseId:{},userId:{}",companyId,courseId,userId);
        CourseClassTeamSearchDTO courseClassTeamSearchDTO = new CourseClassTeamSearchDTO();
        courseClassTeamSearchDTO.setCompanyId(companyId);
        courseClassTeamSearchDTO.setUserId(userId);
        courseClassTeamSearchDTO.setCourseId(courseId);
        ClassesTeamDetailVO classesTeamInfoByUser = oldServerFeignClient.getClassesTeamInfoByUser(courseClassTeamSearchDTO);
        log.info("getClassesTeamInfoByUser end companyId:{},courseId:{},userId:{}",companyId,courseId,userId);
        return classesTeamInfoByUser;
    }

    public List<UserFlagInfoVO> getUserByClassOrTeam(Integer classesNo, Integer teamId) {
        log.info("getUserByClassOrTeam begin classesNo:{},teamId:{}",classesNo,teamId);
        UserByClassOrTeamDTO userByClassOrTeamDTO = new UserByClassOrTeamDTO();
        userByClassOrTeamDTO.setClassesNo(classesNo);
        userByClassOrTeamDTO.setTeamId(Objects.nonNull(teamId) && !teamId.equals(9) ? teamId : null);
        List<UserFlagInfoVO> userByClassOrTeam = oldServerFeignClient.getUserByClassOrTeam(userByClassOrTeamDTO);
        String jsonObject = JSON.toJSONString(userByClassOrTeam);
        List<UserFlagInfoVO> userInfoVOS = JSONObject.parseArray(jsonObject, UserFlagInfoVO.class);
        log.info("getUserByClassOrTeam end classesNo:{},teamId:{}",classesNo,teamId);
        return userInfoVOS;
    }

    public List<UserFlagInfoVO> getUserInfoByPhones(List<String> userPhone) {
        if (CollUtil.isEmpty(userPhone)) {
            log.info("getUserInfoByPhones userPhone is empty.");
            return new ArrayList<>();
        }
        UserPhonesDTO userPhonesDTO = new UserPhonesDTO();
        userPhonesDTO.setUserPhone(userPhone);
        List<UserFlagInfoVO> userInfoByPhones = oldServerFeignClient.getUserInfoByPhones(userPhonesDTO);
        log.info(JSON.toJSONString(userInfoByPhones));
        if (userInfoByPhones.size() < 1){
            return new ArrayList<>();
        }
        String jsonObject = JSON.toJSONString(userInfoByPhones);
        List<UserFlagInfoVO> userInfoVOS = JSONObject.parseArray(jsonObject, UserFlagInfoVO.class);
        return userInfoVOS;
    }

    public List<UserFlagInfoVO> getClassesTeamInfoByUserIds(Integer courseId, List<Integer> userIds) {
        if (courseId == null && CollUtil.isEmpty(userIds)) {
            log.info("getClassesTeamInfoByUserIds courseId is null or userIds is empty.");
            return new ArrayList<>();
        }
        ClassesTeamInfoByUserIdsDTO classesTeamInfoByUserIdsDTO = new ClassesTeamInfoByUserIdsDTO();
        classesTeamInfoByUserIdsDTO.setUserIds(userIds);
        classesTeamInfoByUserIdsDTO.setCourseId(courseId);
        List<UserFlagInfoVO> classesTeamInfoByUserIds = oldServerFeignClient.getClassesTeamInfoByUserIds(classesTeamInfoByUserIdsDTO);
        log.info(JSON.toJSONString(classesTeamInfoByUserIds));
        if (classesTeamInfoByUserIds.size() < 1){
            return new ArrayList<>();

        }
        String jsonObject = JSON.toJSONString(classesTeamInfoByUserIds);
        List<UserFlagInfoVO> userInfoVOS = JSONObject.parseArray(jsonObject, UserFlagInfoVO.class);

        return userInfoVOS;

    }

    public Boolean isTeacher(Integer courseId,Integer userId) {
        return oldServerFeignClient.isTeacher(courseId,userId);
    }

    public JumpPageWorkRemindParamVO jumpPageWorkRemindParam(WorkRemindDTO dto) {
        return oldServerFeignClient.jumpPageWorkRemindParam(dto);
    }

    public Integer getClassPersonNum(Integer classesNo) {
        return oldServerFeignClient.getClassPersonNum(classesNo);
    }

}
