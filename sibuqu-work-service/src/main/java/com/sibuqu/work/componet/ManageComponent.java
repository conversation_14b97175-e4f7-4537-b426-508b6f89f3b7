package com.sibuqu.work.componet;

import com.alibaba.fastjson.JSON;
import com.sibuqu.work.feignclient.vo.CourseInfoVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;


/**
 * <AUTHOR>
 * @date 2021/6/3-下午5:22
 */
@Slf4j
@RefreshScope
@Service("ManageComponent")
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class ManageComponent {
    private final RestTemplate restTemplate;
    @Value("${com.zlzgy.tetralogy.manage.url:http://zlzgy-tetralogy-manage}")
    private String manageApi;

    public CourseInfoVO getCourseInfo(Integer courseId) {
        String url="/time/course/getCourseInfo?courseId="+courseId;
        long startDate = System.currentTimeMillis();
        log.info("作业查询课程下的作业后台跨库进行获取课程相关信息....ManageComponent.getCourseInfo#manageUrl:{},:courseId= {}-----调用开始时间--startDate:{}---",manageApi+url,courseId,startDate);
        CourseInfoVO vo =new  CourseInfoVO ();
        try {
             vo = restTemplate.getForObject(manageApi+url,CourseInfoVO.class);
            long endDate = System.currentTimeMillis();
            log.info("----返回信息----作业查询课程下的作业后台跨库进行获取课程相关信息....ManageComponent.getCourseInfo#manageUrl:{},:courseId= {}-----调用开始时间--startDate:{}--调用结束时间:endDate:{}---返回的信息：{}"
                    ,manageApi+url,courseId,
                    startDate,endDate,JSON.toJSONString(vo));
            return vo;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return  vo;
    }

}
