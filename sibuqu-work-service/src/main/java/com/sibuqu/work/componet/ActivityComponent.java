package com.sibuqu.work.componet;

import cn.hutool.core.collection.CollUtil;
import com.sibuqu.activity.vo.ebook.HeartReadListVO;
import com.sibuqu.base.common.utils.TryCatchUtil;
import com.sibuqu.work.feignclient.client.ActivityFeignClient;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class ActivityComponent {

    @Autowired
    private ActivityFeignClient activityFeignClient;

    @ApiOperation("内部-心得列表-读书记录信息")
    public Map<Long, HeartReadListVO> heartReadList(List<Long> readRecordIdList) {
        log.info("heartReadList begin,readRecordIdList:{}", readRecordIdList);
        if (CollUtil.isEmpty(readRecordIdList)) {
            return new HashMap<>();
        }
        Map<Long, HeartReadListVO> map = TryCatchUtil.getAndUnpackResultInfo(() -> activityFeignClient.heartReadList(readRecordIdList), HashMap::new);
        log.info("heartReadList end");
        return map;
    }

}
