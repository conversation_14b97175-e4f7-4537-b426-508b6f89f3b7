package com.sibuqu.work.componet;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.work.feignclient.client.CollectorDataFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Component
public class CollectorDataComponent {

    @Autowired
    private CollectorDataFeignClient collectorDataFeignClient;

    /**
     * 根据数据id查询一致化信息
     */
    public String queryWorkLevel(String id) {
        ResultInfo resultInfo = collectorDataFeignClient.query(id);
        log.info("queryWorkLevel resultInfo:{}", JSON.toJSONString(resultInfo));
        try {
            return Optional.ofNullable(resultInfo)
                    .map(ResultInfo::getData)
                    .map(data -> JSON.parseArray((String) ((Map<String, Object>) data).get("valueList"), JSONObject.class))
                    .map(list -> {
                        String level = "a";
                        for (JSONObject value : list) {
                            if (((String) value.get("label")).contains("学习分级")) {
                                JSONObject valueJSONObject = JSONObject.parseObject((String) value.get("value"));
                                Integer valueValue = (Integer) valueJSONObject.get("value");
                                if (Objects.equals(valueValue, 1)) {
                                    level = "a";
                                } else if (Objects.equals(valueValue, 2)) {
                                    level = "b";
                                } else if (Objects.equals(valueValue, 3)) {
                                    level = "c";
                                }
                                break;
                            }
                        }
                        return level;
                    })
                    .orElse("a");
        } catch (Exception e) {
            log.error("queryWorkLevel error:{}", e.getMessage());
            return "a";
        }
    }


}
