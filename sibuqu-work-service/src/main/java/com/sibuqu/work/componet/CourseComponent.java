package com.sibuqu.work.componet;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.base.common.utils.TryCatchUtil;
import com.sibuqu.course.dto.courseconfig.CourseConfigDetailDTO;
import com.sibuqu.course.dto.courseinfo.CourseDetailDTO;
import com.sibuqu.course.dto.courseinfo.CourseInfoDTO;
import com.sibuqu.course.dto.courseinfo.CourseWorkModelDTO;
import com.sibuqu.course.dto.coursetimetable.*;
import com.sibuqu.course.dto.goodsinfo.GoodsInfoDetailDTO;
import com.sibuqu.course.dto.personal.app.CttByCourseIdBeginDateEndDateDTO;
import com.sibuqu.course.entity.CourseConfig;
import com.sibuqu.course.entity.CourseInfo;
import com.sibuqu.course.entity.CourseTimetable;
import com.sibuqu.course.vo.courseinfo.CourseLevelWorkModelVO;
import com.sibuqu.course.vo.coursetimetable.*;
import com.sibuqu.course.vo.goodsinfo.CourseGoodsDetailVO;
import com.sibuqu.course.vo.goodsinfo.GoodsIdListByCourseIdListVO;
import com.sibuqu.course.vo.goodsinfo.GoodsInfoDetailVO;
import com.sibuqu.course.vo.goodsinfo.InnoCourseGoodsDetailVO;
import com.sibuqu.course.vo.personal.app.CttByCourseIdBeginDateEndDateVO;
import com.sibuqu.starter.logging.common.utils.LogUtils;
import com.sibuqu.work.feignclient.client.CourseFeignClient;
import com.sibuqu.work.webclient.model.dto.CourseClassTeamDTO;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Component
public class CourseComponent {

    @Autowired
    private CourseFeignClient courseFeignClient;

    /**
     * 根据课程id 和课程表id获取 课程表信息
     *
     * @param courseId
     * @param courseTimeTableId
     * @return
     */
    public CourseTimetableUnlockDetailVO courseTimetableUnlockDetail(Integer courseId, Integer courseTimeTableId) {
        CourseTimetableDetailDTO dto = new CourseTimetableDetailDTO();
        dto.setCourseId(courseId);
        dto.setId(courseTimeTableId);
        log.info("courseTimetableUnlockDetail dto:{}", JSON.toJSONString(dto));
        CourseTimetableUnlockDetailVO resultInfo = TryCatchUtil.getAndUnpackResultInfo(() -> courseFeignClient.courseTimetableUnlockDetail(dto));
        log.info("courseTimetableUnlockDetail end vo:{}", JSON.toJSONString(resultInfo));
        return resultInfo;
    }

    public CourseTimetableUnlockDetailVO courseTimetableUnlockDetail(Integer courseId, Integer courseTimeTableId, Integer userId) {
        CourseTimetableDetailDTO dto = new CourseTimetableDetailDTO();
        dto.setCourseId(courseId);
        dto.setId(courseTimeTableId);
        dto.setUserId(userId);
        log.info("courseTimetableUnlockDetail dto:{}", JSON.toJSONString(dto));
        CourseTimetableUnlockDetailVO resultInfo = TryCatchUtil.getAndUnpackResultInfo(() -> courseFeignClient.courseTimetableUnlockDetail(dto));
        log.info("courseTimetableUnlockDetail end vo:{}", JSON.toJSONString(resultInfo));
        return resultInfo;
    }

    public CourseTimetableUnlockDetailVO courseTimetableUnlockDetail(Integer courseId, Integer courseTimeTableId, String planSchemeOption) {
        CourseTimetableDetailDTO dto = new CourseTimetableDetailDTO();
        dto.setCourseId(courseId);
        dto.setId(courseTimeTableId);
        dto.setPlanSchemeOption(planSchemeOption);
        log.info("courseTimetableUnlockDetail dto:{}", JSON.toJSONString(dto));
        CourseTimetableUnlockDetailVO resultInfo = TryCatchUtil.getAndUnpackResultInfo(() -> courseFeignClient.courseTimetableUnlockDetail(dto));
        log.info("courseTimetableUnlockDetail end vo:{}", JSON.toJSONString(resultInfo));
        return resultInfo;
    }

    /**
     * 获取历史课程表
     *
     * @param courseId
     * @param courseId
     * @return
     */
    public List<CourseTimetableHistoryVO> getCourseHistoryTimetable(Integer courseId) {
        CourseTimetableSearchDTO courseTimetableSearchDTO = new CourseTimetableSearchDTO();
        courseTimetableSearchDTO.setCourseId(courseId);
        courseTimetableSearchDTO.setSortMode(2);
        log.info("getCourseHistoryTimetable begin courseId:{}", courseId);
        ResultInfo<List<CourseTimetableHistoryVO>> resultInfo = courseFeignClient.getCourseHistoryTimetable(courseTimetableSearchDTO);
        log.info("getCourseHistoryTimetable end");
        if (resultInfo.getCode().equals(0)) {
            return resultInfo.getData();
        } else {
            return new ArrayList<>();
        }
    }

    public CourseConfig courseConfigDetail(Integer companyId, Integer courseId) {
        CourseConfigDetailDTO courseConfigDetailDTO = new CourseConfigDetailDTO();
        courseConfigDetailDTO.setCompanyId(companyId);
        courseConfigDetailDTO.setCourseId(courseId);
        log.info("courseConfigDetail begin dto:{}", JSON.toJSONString(courseConfigDetailDTO));
        CourseConfig vo = courseFeignClient.courseConfigDetail(courseConfigDetailDTO).getData();
        log.info("courseConfigDetail end vo:{}", JSON.toJSONString(vo));
        return vo;
    }

    public PageInfoBT<CourseTimetable> getCourseHistoryTimetablePage(CourseClassTeamDTO courseClassTeamDTO, int pageSize, int pageNum) {
        CourseTimetableSearchDTO courseTimetableSearchDTO = new CourseTimetableSearchDTO();
        courseTimetableSearchDTO.setPageSize(pageSize);
        courseTimetableSearchDTO.setPageNum(pageNum);
        courseTimetableSearchDTO.setSortMode(courseClassTeamDTO.getSortMode());
        courseTimetableSearchDTO.setCourseId(courseClassTeamDTO.getCourseId());
        log.info("getCourseHistoryTimetablePage begin dto:{}", JSON.toJSONString(courseTimetableSearchDTO));
        PageInfoBT<CourseTimetable> page = courseFeignClient.getCourseHistoryTimetablePage(courseTimetableSearchDTO).getData();
        log.info("getCourseHistoryTimetablePage end");
        return page;
    }

    public List<Integer> courTimeTableIdsByWorkModelId(Integer workModelId) {
        if (Objects.isNull(workModelId)) {
            return new ArrayList<>();
        }
        CourseTimetableFutureSearchDTO courseTimetableFutureSearchDTO = new CourseTimetableFutureSearchDTO();
        courseTimetableFutureSearchDTO.setWorkModelId(workModelId);
        log.info("courTimeTableIdsByWorkModelId begin workModelId:{}", workModelId);
        return courseFeignClient.courTimeTableIdsByWorkModelId(courseTimetableFutureSearchDTO).getData();
    }


    /**
     * 课程时间表id查课程表信息详情
     *
     * @param courseTimetableId
     */
    public CourseTimetable getCourseTimetableDetail(Integer courseTimetableId) {
        CourseTimetableSearchOneDTO courseTimetableSearchDTO = new CourseTimetableSearchOneDTO();
        courseTimetableSearchDTO.setId(courseTimetableId);
        log.info("getCourseTimetableDetail begin courseTimetableSearchDTO:{}", JSON.toJSONString(courseTimetableSearchDTO));
        ResultInfo<CourseTimetable> resultInfo = courseFeignClient.courseTimetableDetail(courseTimetableSearchDTO);
        if (resultInfo.getCode().equals(0)) {
            return resultInfo.getData();
        } else {
            return null;
        }
    }

    /**
     * 课程时间表id查课程表信息集合
     *
     * @param courseTimetableIds
     */
    public List<CourseTimetableSimpleListVO> getCourseTimetableSimpleByCourseTimetableIds(List<Integer> courseTimetableIds) {
        CourseTimetableListDTO courseTimetableSearchDTO = new CourseTimetableListDTO();
        courseTimetableSearchDTO.setCourseTimetableIds(courseTimetableIds);
        LogUtils.info("--------------课程时间表id查课程表信息集合----入参：{}", courseTimetableSearchDTO);
        ResultInfo<List<CourseTimetableSimpleListVO>> resultInfo = courseFeignClient.getCourseTimetableSimpleByCourseTimetableIds(courseTimetableSearchDTO);
        LogUtils.info("--------------课程时间表id查课程表信息集合---返回---返回参数:{}---入参：{}", resultInfo, courseTimetableSearchDTO);
        if (resultInfo.getCode().equals(0)) {
            return resultInfo.getData();
        } else {
            return new ArrayList<>();
        }
    }

    public Integer getSkuIdByCourseId(Integer courseId) {
        CourseDetailDTO dto = new CourseDetailDTO();
        dto.setCompanyId(-1);
        dto.setId(courseId);
        log.info("getSkuIdByCourseId begin dto:{}", JSON.toJSONString(dto));
        return Optional.ofNullable(courseFeignClient.courseGoodsDetail(dto)).map(ResultInfo::getData)
                .map(CourseGoodsDetailVO::getGoodsId).orElse(-1);
    }

    public CourseInfo courseInfoById(Integer courseId) {
        CourseInfoDTO courseInfoDTO = new CourseInfoDTO();
        courseInfoDTO.setCourseId(courseId);
        log.info("courseInfoById begin courseId:{}", courseId);
        return courseFeignClient.courseInfoById(courseInfoDTO);
    }

    public List<CourseTimetable> getMakeUpWorkCourseTimetableList(Integer courseId){
        GetMakeUpWorkCourseTimetableDTO courseTimetableDTO = new GetMakeUpWorkCourseTimetableDTO();
        courseTimetableDTO.setCourseId(courseId);
        return courseFeignClient.getMakeUpWorkCourseTimetableList(courseTimetableDTO);
    }

    @ApiOperation(value = "内部调用-获取补作业课程表包含不可完成的")
    public List<GetMakeUpWorkCttListVO> getMakeUpWorkCttList(Integer courseId, Integer userId) {
        GetMakeUpWorkCttListDTO courseTimetableDTO = new GetMakeUpWorkCttListDTO();
        courseTimetableDTO.setCourseId(courseId);
        courseTimetableDTO.setUserId(userId);
        log.info("getMakeUpWorkCttList courseTimetableDTO:{}", JSON.toJSONString(courseTimetableDTO));
        List<GetMakeUpWorkCttListVO> list = TryCatchUtil.get(() -> courseFeignClient.getMakeUpWorkCttList(courseTimetableDTO));
        log.info("getMakeUpWorkCttList end");
        return list;
    }

    public List<CourseTimetable> getCourseTimetableListByName(String courseId, String resourceTitle) {
        return courseFeignClient.courseTimetablesByResourceTitle(courseId,resourceTitle);
    }

    public List<Integer> getAllTogetherCourseIds() {
        return courseFeignClient.getAllTogetherCourseIds();
    }

    @ApiOperation(value = "内部服务-课程等级作业模板", notes = "内部服务-课程等级作业模板")
    public List<CourseLevelWorkModelVO> courseLevelWorkModel(Integer courseId) {
        CourseWorkModelDTO dto = new CourseWorkModelDTO();
        dto.setCourseId(courseId);
        List<CourseLevelWorkModelVO> list = courseFeignClient.courseLevelWorkModel(dto).getData();
        log.info("courseLevelWorkModel list:{}", JSON.toJSONString(list));
        return list;
    }

    @ApiOperation("内部调用-根据课程id和日期查询课件id")
    public CourseTimetable queryByCourseIdAndDate(@RequestBody @Validated QueryByCourseIdAndDateDTO dto) {
        log.info("queryByCourseIdAndDate,dto:{}", JSON.toJSONString(dto));
        CourseTimetable courseTimetable = courseFeignClient.queryByCourseIdAndDate(dto);
        log.info("queryByCourseIdAndDate end,courseTimetable:{}", JSON.toJSONString(courseTimetable));
        return courseTimetable;
    }

    @ApiOperation("内部服务-根据课程id列表获取商品id列表")
    @PostMapping("/appcourse/course/goodsIdListByCourseIdList")
    public List<GoodsIdListByCourseIdListVO> goodsIdListByCourseIdList(@RequestBody List<Integer> courseIdList) {
        log.info("goodsIdListByCourseIdList courseIdList:{}", JSON.toJSONString(courseIdList));
        if (CollUtil.isEmpty(courseIdList)) {
            return new ArrayList<>();
        }
        List<GoodsIdListByCourseIdListVO> list = courseFeignClient.goodsIdListByCourseIdList(courseIdList);
        log.info("goodsIdListByCourseIdList end");
        return list;
    }

    @ApiOperation(value = "内部服务-批量查询作业时间内的课件")
    public List<CourseTimetableHistoryVO> batchGetWorkingCourseTimetables(List<Integer> courseIdList) {
        log.info("batchGetWorkingCourseTimetables begin, courseIdList:{}", courseIdList);
        if (CollUtil.isEmpty(courseIdList)) {
            return new ArrayList<>();
        }
        CourseTimetableWorkingBatchSearchDTO dto = new CourseTimetableWorkingBatchSearchDTO();
        dto.setCourseIds(courseIdList);
        List<CourseTimetableHistoryVO> list = TryCatchUtil.getAndUnpackResultInfo(() -> courseFeignClient.batchGetWorkingCourseTimetables(dto), ArrayList::new);
        log.info("batchGetWorkingCourseTimetables end");
        return list;
    }
    public GoodsInfoDetailVO innerGoodsInfoDetail(Integer goodsId) {
        GoodsInfoDetailDTO goodsInfoDetailDTO = new GoodsInfoDetailDTO();
        goodsInfoDetailDTO.setGoodsId(goodsId);
        LogUtils.info("内部调用商品详情入参：{}", JSON.toJSONString(goodsInfoDetailDTO));
        GoodsInfoDetailVO data = courseFeignClient.innerGoodsInfoDetail(goodsInfoDetailDTO).getData();
        LogUtils.info("内部调用商品详情出参：{}", JSON.toJSONString(data));
        return data;
    }

    public ResultInfo<Boolean> unlockByUidAndRid(CourseResourceUnlockUserDTO dto){
        return courseFeignClient.unlockByUidAndRid(dto);
    }

    @ApiOperation(value = "根据课程 id 开始结束日期查询课件列表")
    public Map<LocalDate, CttByCourseIdBeginDateEndDateVO> cttByCourseIdBeginDateEndDate(Integer courseId, LocalDate beginDate, LocalDate endDate) {
        CttByCourseIdBeginDateEndDateDTO dto = new CttByCourseIdBeginDateEndDateDTO();
        dto.setCourseId(courseId);
        dto.setBeginDate(beginDate);
        dto.setEndDate(endDate);
        log.info("cttByCourseIdBeginDateEndDate dto:{}", JSON.toJSONString(dto));
        Map<LocalDate, CttByCourseIdBeginDateEndDateVO> map = TryCatchUtil.getAndUnpackResultInfo(() -> courseFeignClient.cttByCourseIdBeginDateEndDate(dto), HashMap::new);
        log.info("cttByCourseIdBeginDateEndDate end");
        return map;
    }

    @ApiOperation(value = "内部调用-批量查询用户所选课程学习方案")
    public Map<Integer, String> batchGetUserPlanSchemeOption(Integer courseTimetableId, List<Integer> userIds) {
        CourseStudyPlanUserBatchSearchDTO dto = new CourseStudyPlanUserBatchSearchDTO();
        dto.setCourseTimetableId(courseTimetableId);
        dto.setUserIds(userIds);
        log.info("batchGetUserPlanSchemeOption dto:{}", JSON.toJSONString(dto));
        Map<Integer, String> map = TryCatchUtil.getAndUnpackResultInfo(() -> courseFeignClient.batchGetUserPlanSchemeOption(dto), HashMap::new);
        log.info("batchGetUserPlanSchemeOption end");
        return map;
    }

    @ApiOperation(value = "内部调用-批量查询课件归属课程学习方案")
    public Map<Integer, String> batchGetTimetablePlanScheme(Integer courseId, Integer userId, List<Integer> courseTimetableIds) {
        CourseStudyPlanTimetableBatchSearchDTO searchDTO = new CourseStudyPlanTimetableBatchSearchDTO();
        searchDTO.setUserId(userId);
        searchDTO.setCourseId(courseId);
        searchDTO.setCourseTimetableIds(courseTimetableIds);
        log.info("batchGetTimetablePlanScheme searchDTO:{}", JSON.toJSONString(searchDTO));
        Map<Integer, String> map = TryCatchUtil.getAndUnpackResultInfo(() -> courseFeignClient.batchGetTimetablePlanScheme(searchDTO), HashMap::new);
        log.info("batchGetTimetablePlanScheme end");
        return map;
    }

    @ApiOperation(value = "内部调用-根据分组id列表查询分组名称")
    public Map<Integer, String> queryByCoursewareGroupIdList(@RequestBody @Validated List<Integer> coursewareGroupIdList) {
        log.info("queryByCoursewareGroupIdList begin:{}", JSON.toJSONString(coursewareGroupIdList));
        Map<Integer, String> map = TryCatchUtil.getAndUnpackResultInfo(() -> courseFeignClient.queryByCoursewareGroupIdList(coursewareGroupIdList), HashMap::new);
        log.info("queryByCoursewareGroupIdList end");
        return map;
    }

    @ApiOperation("内部调用-根据课程id和日期查询课件id")
    public CourseTimetableInfoVO courseTimetableInfo(@RequestBody @Validated CourseTimetableInfoDTO dto){
        log.info("queryByCoursewareGroupIdList begin:{}", JSON.toJSONString(dto));
        CourseTimetableInfoVO andUnpackResultInfo = TryCatchUtil.getAndUnpackResultInfo(() -> courseFeignClient.courseTimetableInfo(dto));
        log.info("queryByCoursewareGroupIdList end,{}", JSON.toJSONString(andUnpackResultInfo));

        return andUnpackResultInfo;
    }

    public InnoCourseGoodsDetailVO innoCourseInfo(){
        InnoCourseGoodsDetailVO innoCourseGoodsDetailVO = TryCatchUtil.getAndUnpackResultInfo(() -> courseFeignClient.innoCourseInfo());
        log.info("innoCourseInfo end,{}", JSON.toJSONString(innoCourseGoodsDetailVO));
        return innoCourseGoodsDetailVO;
    }
}
