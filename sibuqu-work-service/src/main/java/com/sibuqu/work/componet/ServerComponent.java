package com.sibuqu.work.componet;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sibuqu.classes.dto.CourseClassTeamSearchDTO;
import com.sibuqu.classes.vo.ClassesTeamDetailVO;
import com.sibuqu.course.dto.coursetimetable.CourseTimetableDetailDTO;
import com.sibuqu.course.vo.coursetimetable.CourseTimetableUnlockDetailVO;
import com.sibuqu.work.bo.CourseTimetableUnlockDetailBO;
import com.sibuqu.work.dto.WorkRemindDTO;
import com.sibuqu.work.vo.api.AdditionalInfoVO;
import com.sibuqu.work.vo.api.JumpPageWorkRemindParamVO;
import com.sibuqu.work.vo.api.UserFlagInfoVO;
import com.sibuqu.work.webclient.client.ServerWebClient;
import com.sibuqu.work.webclient.model.dto.AdditionalInfoDTO;
import com.sibuqu.work.webclient.model.dto.ClassesTeamInfoByUserIdsDTO;
import com.sibuqu.work.webclient.model.dto.UserByClassOrTeamDTO;
import com.sibuqu.work.webclient.model.vo.UserPhonesDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ServerComponent {

}
