package com.sibuqu.work.componet;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.sibuqu.base.common.exception.BusinessException;
import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.base.common.result.BaseResultCode;
import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.base.common.utils.TryCatchUtil;
import com.sibuqu.classes.dto.ClassesUserSearchDTO;
import com.sibuqu.classes.dto.CourseClassTeamSearchDTO;
import com.sibuqu.classes.dto.GetSimpleClassInfoDTO;
import com.sibuqu.classes.dto.InnerClassesInfoDTO;
import com.sibuqu.classes.dto.InnerClassesUserInfoListDTO;
import com.sibuqu.classes.dto.InnerMySimpleClassesDetailDTO;
import com.sibuqu.classes.dto.MemberListByCourseIdClassesNoTeamIdDTO;
import com.sibuqu.classes.dto.QueryByCourseIdUserIdListDTO;
import com.sibuqu.classes.dto.IsInTribeDTO;
import com.sibuqu.work.dto.CheckUserIsTeamLeaderDTO;
import com.sibuqu.work.dto.QueryTribeUserRoleDTO;
import com.sibuqu.classes.vo.ClassTeacherVO;
import com.sibuqu.classes.vo.ClassesMemberInfoListVO;
import com.sibuqu.classes.vo.ClassesTeamDetailVO;
import com.sibuqu.classes.vo.GetSimpleClassInfoVO;
import com.sibuqu.classes.vo.InnerClassesInfoVO;
import com.sibuqu.classes.vo.InnerClassesUserInfoListVO;
import com.sibuqu.classes.vo.MemberListByCourseIdClassesNoTeamIdVO;
import com.sibuqu.classes.vo.MySimpleClassesDetailVO;
import com.sibuqu.classes.vo.QueryByCourseIdUserIdListVO;
import com.sibuqu.classes.vo.SimpleClassInfoVO;
import com.sibuqu.work.feignclient.client.ClassesFeignClient;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ClassesComponent {
    private final ClassesFeignClient classesFeignClient;

    /**
     * 根据企业id 课程id 用户ID 获取班级小组
     *
     * @param companyId
     * @param courseId
     * @param userId
     * @return
     */
    public ClassesTeamDetailVO getClassesTeamInfoByUser(Integer companyId, Integer courseId, Integer userId) {
        log.info("getClassesTeamInfoByUser begin companyId:{},courseId:{},userId:{}", companyId, courseId, userId);
        CourseClassTeamSearchDTO courseClassTeamSearchDTO = new CourseClassTeamSearchDTO();
        courseClassTeamSearchDTO.setCompanyId(companyId);
        courseClassTeamSearchDTO.setUserId(userId);
        courseClassTeamSearchDTO.setCourseId(courseId);
        ResultInfo<ClassesTeamDetailVO> resultInfo = classesFeignClient.getClassesTeamInfoByUser(courseClassTeamSearchDTO);
        log.info("getClassesTeamInfoByUser end");
        if (resultInfo.getCode().equals(0)) {
            return resultInfo.getData();
        } else {
            return null;
        }

    }

    public ClassesTeamDetailVO listenGetClassesTeamInfoByUser(Integer companyId, Integer courseId, Integer userId) {
        CourseClassTeamSearchDTO courseClassTeamSearchDTO = new CourseClassTeamSearchDTO();
        courseClassTeamSearchDTO.setCompanyId(companyId);
        courseClassTeamSearchDTO.setUserId(userId);
        courseClassTeamSearchDTO.setCourseId(courseId);
        ResultInfo<ClassesTeamDetailVO> resultInfo = classesFeignClient.listenGetClassesTeamInfoByUser(courseClassTeamSearchDTO);
        if (resultInfo.getCode().equals(0)) {
            return resultInfo.getData();
        } else {
            return null;
        }

    }

    /**
     * 根据班级id 小组 返回用户信息
     *
     * @param companyId
     * @param classesId
     * @param teamId
     * @return
     */
    public List<ClassesMemberInfoListVO> getUserByClassOrTeam(Integer companyId, Integer classesId, Integer teamId) {
        ClassesUserSearchDTO classesUserSearchDTO = new ClassesUserSearchDTO();
        classesUserSearchDTO.setCompanyId(companyId);
        classesUserSearchDTO.setClassesId(classesId);
        classesUserSearchDTO.setTeamId(teamId);
        ResultInfo<List<ClassesMemberInfoListVO>> resultInfo = classesFeignClient.getUserByClassOrTeam(classesUserSearchDTO);
        if (resultInfo.getCode().equals(0)) {
            return resultInfo.getData();
        } else {
            return new ArrayList();
        }

    }

    public PageInfoBT<ClassesMemberInfoListVO> getUserByClassOrTeamPage(Integer pageSize, Integer pageNum, Integer companyId, Integer classesId, Integer teamId, Integer courseId) {
        ClassesUserSearchDTO classesUserSearchDTO = new ClassesUserSearchDTO();
        classesUserSearchDTO.setCompanyId(companyId);
        classesUserSearchDTO.setClassesId(classesId);
        classesUserSearchDTO.setTeamId(teamId);
        classesUserSearchDTO.setPageSize(pageSize);
        classesUserSearchDTO.setPageNum(pageNum);
        classesUserSearchDTO.setCourseId(courseId);
        return classesFeignClient.getUserByClassOrTeamPage(classesUserSearchDTO);

    }

    public List<ClassTeacherVO> getClassTeachers(String teacherName, Integer courseId, Integer companyId) {
        return classesFeignClient.getClassTeachers(teacherName, courseId, companyId);
    }

    @ApiOperation(value = "内部服务接口-简单班级信息")
    public List<GetSimpleClassInfoVO> getSimpleClassInfo(Integer companyId, Integer courseId, List<Integer> classesIdList) {
        log.info("getSimpleClassInfo 请求开始 companyId:{},courseId:{},classesIdList:{}", companyId, courseId, classesIdList);
        GetSimpleClassInfoDTO dto = new GetSimpleClassInfoDTO();
        dto.setCompanyId(companyId);
        dto.setCourseId(courseId);
        dto.setClassesIdList(classesIdList);
        ResultInfo<List<GetSimpleClassInfoVO>> resultInfo = TryCatchUtil.get(() -> classesFeignClient.getSimpleClassInfo(dto));
        if (!resultInfo.getCode().equals(0)) {
            log.error("获取班级信息失败:{}", JSON.toJSONString(resultInfo));
            return new ArrayList<>();
        }
        log.info("getSimpleClassInfo 请求结束");
        return resultInfo.getData();
    }

    @ApiOperation(value = "根据班级 id 列表查询简单信息")
    public Map<Integer, SimpleClassInfoVO> simpleInfoByIdList(@RequestBody List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            return new HashMap<>();
        }
        log.info("simpleInfoByIdList begin idList:{}", JSON.toJSONString(idList));
        Map<Integer, SimpleClassInfoVO> map = TryCatchUtil.getAndUnpackResultInfo(() -> classesFeignClient.simpleInfoByIdList(idList));
        log.info("simpleInfoByIdList end");
        return map;
    }

    @ApiOperation(value = "内部接口-我的班级简单详情")
    public MySimpleClassesDetailVO innerMySimpleClassesDetail(Integer courseId, Integer userId) {
        InnerMySimpleClassesDetailDTO dto = new InnerMySimpleClassesDetailDTO();
        dto.setCourseId(courseId);
        dto.setUserId(userId);
        log.info("innerMySimpleClassesDetail dto:{}", JSON.toJSONString(dto));
        MySimpleClassesDetailVO vo = TryCatchUtil.getAndUnpackResultInfo(() -> classesFeignClient.innerMySimpleClassesDetail(dto));
        log.info("innerMySimpleClassesDetail end");
        return vo;
    }

    @ApiOperation("内部接口-根据班级no查询班级人员信息")
    public List<InnerClassesUserInfoListVO> innerClassesUserInfoList(Integer companyId, Integer courseId, String classesNo, Integer teamId) {
        InnerClassesUserInfoListDTO dto = new InnerClassesUserInfoListDTO();
        dto.setCompanyId(companyId);
        dto.setCourseId(courseId);
        dto.setClassesNo(classesNo);
        dto.setTeamId(teamId);
        log.info("innerClassesUserInfoList dto:{}", JSON.toJSONString(dto));
        List<InnerClassesUserInfoListVO> list = TryCatchUtil.getAndUnpackResultInfo(() -> classesFeignClient.innerClassesUserInfoList(dto),ArrayList::new);
        log.info("innerClassesUserInfoList end");
        return list;
    }

    @ApiOperation("根据课程id和用户id列表查询")
    public List<QueryByCourseIdUserIdListVO> queryByCourseIdUserIdList(Integer courseId, List<Integer> userIdList) {
        QueryByCourseIdUserIdListDTO dto = new QueryByCourseIdUserIdListDTO();
        dto.setCourseId(courseId);
        dto.setUserIdList(userIdList);
        log.info("queryByCourseIdUserIdList dto:{}", JSON.toJSONString(dto));
        if (CollUtil.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        ResultInfo<List<QueryByCourseIdUserIdListVO>> resultInfo = TryCatchUtil.get(() -> classesFeignClient.queryByCourseIdUserIdList(dto));
        log.info("queryByCourseIdUserIdList resultInfo:{}", JSON.toJSONString(resultInfo));
        if (Objects.isNull(resultInfo)) {
            throw new BusinessException("内部服务器错误");
        }
        if (!BaseResultCode.SUCCESS.getCode().equals(resultInfo.getCode())) {
            throw new BusinessException(resultInfo.getCode(), resultInfo.getMsg());
        } else {
            return resultInfo.getData();
        }
    }

    @ApiOperation("内部服务-班级详情")
    public InnerClassesInfoVO innerClassesInfo(Integer classesId) {
        InnerClassesInfoDTO dto = new InnerClassesInfoDTO();
        dto.setClassesId(classesId);
        log.info("innerClassesInfo dto:{}", JSON.toJSONString(dto));
        InnerClassesInfoVO vo = TryCatchUtil.getAndUnpackResultInfo(() -> classesFeignClient.innerClassesInfo(dto));
        log.info("innerClassesInfo vo:{}", JSON.toJSONString(vo));
        return vo;
    }

    @ApiOperation("根据课程id班级no小组id查询班级成员信息")
    public List<MemberListByCourseIdClassesNoTeamIdVO> memberListByCourseIdClassesNoTeamId(Integer companyId, Integer courseId, String classesNo, Integer teamId) {
        MemberListByCourseIdClassesNoTeamIdDTO dto = new MemberListByCourseIdClassesNoTeamIdDTO();
        dto.setCompanyId(companyId);
        dto.setCourseId(courseId);
        dto.setClassesNo(classesNo);
        dto.setTeamId(teamId);
        dto.setTeamId(Objects.nonNull(teamId) && !teamId.equals(9) ? teamId : null);
        log.info("memberListByCourseIdClassesNoTeamId dto:{}", JSON.toJSONString(dto));
        List<MemberListByCourseIdClassesNoTeamIdVO> list = TryCatchUtil.getAndUnpackResultInfo(() -> classesFeignClient.memberListByCourseIdClassesNoTeamId(dto), ArrayList::new);
        log.info("memberListByCourseIdClassesNoTeamId end");
        return list;
    }

    @ApiOperation("根据班级id查询班级成员数量")
    public Integer memberCountByClassesId(@RequestParam("classesId") Integer classesId) {
        log.info("memberCountByClassesId classesId:{}", classesId);
        Integer count = TryCatchUtil.getAndUnpackResultInfo(() -> classesFeignClient.memberCountByClassesId(classesId), () -> 0);
        log.info("memberCountByClassesId end coont:{}", count);
        return count;
    }

    @ApiOperation("用户是否在部落里")
    public Boolean isInTribe(Integer userId, Integer courseId) {
        IsInTribeDTO dto = new IsInTribeDTO();
        dto.setUserId(userId);
        dto.setCourseId(courseId);
        log.info("isInTribe dto:{}", JSON.toJSONString(dto));
        Boolean isInTribe = TryCatchUtil.getAndUnpackResultInfo(() -> classesFeignClient.isInTribe(dto), () -> false);
        log.info("isInTribe end isInTribe:{}", isInTribe);
        return isInTribe;
    }

    @ApiOperation("查询所有的组长id")
    public List<Integer> getAllLeaderId(Integer courseId) {
        log.info("getAllLeaderId courseId:{}", courseId);
        List<Integer> list = TryCatchUtil.getAndUnpackResultInfo(() -> classesFeignClient.getAllLeaderId(courseId), ArrayList::new);
        log.info("getAllLeaderId end");
        return list;

    }

    @ApiOperation("查询所有的团队负责人 id")
    @PostMapping("/appclasses/team/getAllTeamLeaderId")
    public List<Integer> getAllTeamLeaderId(@RequestParam("courseId") Integer courseId) {
        log.info("getAllTeamLeaderId courseId:{}", courseId);
        List<Integer> list = TryCatchUtil.getAndUnpackResultInfo(() -> classesFeignClient.getAllTeamLeaderId(courseId), ArrayList::new);
        log.info("getAllTeamLeaderId end");
        return list;
    }

    @ApiOperation("查询用户是否是团队负责人身份")
    public Map<Integer, Boolean> checkUserIsTeamLeader(CheckUserIsTeamLeaderDTO dto) {
        log.info("checkUserIsTeamLeader dto:{}", JSON.toJSONString(dto));
        Map<Integer, Boolean> result = TryCatchUtil.getAndUnpackResultInfo(() -> classesFeignClient.checkUserIsTeamLeader(dto), HashMap::new);
        log.info("checkUserIsTeamLeader end result size:{}", result.size());
        return result;
    }

    @ApiOperation("查询人员小组内身份")
    public Map<Integer, Integer> queryTribeUserRole(QueryTribeUserRoleDTO dto) {
        log.info("queryTribeUserRole dto:{}", JSON.toJSONString(dto));
        Map<Integer, Integer> result = TryCatchUtil.getAndUnpackResultInfo(() -> classesFeignClient.queryTribeUserRole(dto), HashMap::new);
        log.info("queryTribeUserRole end result size:{}", result.size());
        return result;
    }

}
