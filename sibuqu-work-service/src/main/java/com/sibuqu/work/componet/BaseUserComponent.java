package com.sibuqu.work.componet;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.sibuqu.base.common.utils.TryCatchUtil;
import com.sibuqu.work.feignclient.client.BaseUserFeignClient;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class BaseUserComponent {
    @Autowired
    private BaseUserFeignClient baseUserFeignClient;

    public Integer innoSelectUserIdByName(String userFullName) {
        log.info("innoSelectUserIdByName userFullName:{}", userFullName);
        Integer userId = TryCatchUtil
                .getAndUnpackResultInfo(() -> baseUserFeignClient.innoSelectUserIdByName(userFullName));
        log.info("innoSelectUserIdByName userId:{}", userId);
        return userId;
    }

    @ApiOperation("内部接口-根据手机号查询用户ID")
    public Integer getUserIdByPhone(String phone) {
        log.info("getUserIdByPhone begin phone:{}", phone);
        if (StrUtil.isBlank(phone) || phone.length() < 8) {
            return null;
        }
        Integer result = TryCatchUtil.getAndUnpackResultInfo(() -> baseUserFeignClient.getUserIdByPhone(phone), () -> null);
        log.info("getUserIdByPhone end, result:{}", result);
        return result;
    }

    @ApiOperation("内部接口-根据用户姓名查询用户ID列表")
    public List<Integer> getUserIdListByName(String userName) {
        log.info("getUserIdListByName begin userName:{}", userName);
        List<Integer> result = TryCatchUtil.getAndUnpackResultInfo(() -> baseUserFeignClient.getUserIdListByName(userName), ArrayList::new);
        log.info("getUserIdListByName end, result size:{}", result.size());
        return result;
    }

    @ApiOperation("内部接口-根据用户 id 列表查询用户昵称")
    public Map<Integer, String> innoUserFullNameByUserIdList(List<Integer> idList) {
        log.info("innoUserFullNameByUserIdList begin idList:{}", JSON.toJSONString(idList));
        Map<Integer, String> result = TryCatchUtil.getAndUnpackResultInfo(() -> baseUserFeignClient.innoUserFullNameByUserIdList(idList), HashMap::new);
        log.info("innoUserFullNameByUserIdList end");
        return result;
    }

    public String innoUserFullNameByUserIdList(Integer userId) {
        return innoUserFullNameByUserIdList(Collections.singletonList(userId)).get(userId);
    }

    @ApiOperation("内部接口-inno用户的个人信息列表查询")
    public Map<Integer, InnoUserInfoListVO> innoUserInfoList(List<Integer> idList) {
        log.info("innoUserInfoList begin dto:{}", JSON.toJSONString(idList));
        Map<Integer, InnoUserInfoListVO> map = TryCatchUtil.getAndUnpackResultInfo(() -> baseUserFeignClient.innoUserInfoList(idList), HashMap::new);
        log.info("innoUserInfoList end");
        return map;
    }

    @ApiOperation("内部接口-inno用户的个人信息列表查询")
    public InnoUserInfoListVO innoUserInfoList(Integer userId) {
        return innoUserInfoList(Collections.singletonList(userId)).get(userId);
    }

    @ApiOperation("内部接口-根据用户 id 列表查询用户手机号")
    public Map<Integer, String> userPhoneByUserIdList(List<Integer> idList) {
        log.info("userPhoneByUserIdList begin idList:{}", JSON.toJSONString(idList));
        Map<Integer, String> map = TryCatchUtil.getAndUnpackResultInfo(() -> baseUserFeignClient.userPhoneByUserIdList(idList), HashMap::new);
        log.info("userPhoneByUserIdList end");
        return map;
    }

    @ApiOperation("内部接口-根据用户 id 列表查询用户手机号")
    public String userPhoneByUserIdList(Integer userId) {
        return userPhoneByUserIdList(Collections.singletonList(userId)).get(userId);
    }

}