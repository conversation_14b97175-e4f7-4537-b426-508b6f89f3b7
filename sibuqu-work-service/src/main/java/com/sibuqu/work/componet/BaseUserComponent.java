package com.sibuqu.work.componet;

import com.sibuqu.base.common.utils.TryCatchUtil;
import com.sibuqu.work.feignclient.client.BaseUserFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class BaseUserComponent {
    @Autowired
    private BaseUserFeignClient baseUserFeignClient;

    public Integer innoSelectUserIdByName(String userFullName) {
        log.info("innoSelectUserIdByName userFullName:{}", userFullName);
        Integer userId = TryCatchUtil
                .getAndUnpackResultInfo(() -> baseUserFeignClient.innoSelectUserIdByName(userFullName));
        log.info("innoSelectUserIdByName userId:{}", userId);
        return userId;
    }

}