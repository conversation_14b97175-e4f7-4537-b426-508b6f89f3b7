package com.sibuqu.work.componet;

import cn.hutool.core.util.StrUtil;
import com.sibuqu.base.common.utils.TryCatchUtil;
import com.sibuqu.work.feignclient.client.BaseUserFeignClient;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class BaseUserComponent {
    @Autowired
    private BaseUserFeignClient baseUserFeignClient;

    public Integer innoSelectUserIdByName(String userFullName) {
        log.info("innoSelectUserIdByName userFullName:{}", userFullName);
        Integer userId = TryCatchUtil
                .getAndUnpackResultInfo(() -> baseUserFeignClient.innoSelectUserIdByName(userFullName));
        log.info("innoSelectUserIdByName userId:{}", userId);
        return userId;
    }

    @ApiOperation("内部接口-根据手机号查询用户ID")
    public Integer getUserIdByPhone(String phone) {
        log.info("getUserIdByPhone begin phone:{}", phone);
        if (StrUtil.isBlank(phone) || phone.length() < 8) {
            return null;
        }
        Integer result = TryCatchUtil.getAndUnpackResultInfo(() -> baseUserFeignClient.getUserIdByPhone(phone), () -> null);
        log.info("getUserIdByPhone end, result:{}", result);
        return result;
    }

    @ApiOperation("内部接口-根据用户姓名查询用户ID列表")
    public List<Integer> getUserIdListByName(String userName) {
        log.info("getUserIdListByName begin userName:{}", userName);
        List<Integer> result = TryCatchUtil.getAndUnpackResultInfo(() -> baseUserFeignClient.getUserIdListByName(userName), ArrayList::new);
        log.info("getUserIdListByName end, result size:{}", result.size());
        return result;
    }

}