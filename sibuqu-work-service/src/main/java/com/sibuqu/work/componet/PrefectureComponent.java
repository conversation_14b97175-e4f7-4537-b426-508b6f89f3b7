package com.sibuqu.work.componet;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sibuqu.base.common.enums.YesOrNoEnum;
import com.sibuqu.common.enums.TeacherReplyEnum;
import com.sibuqu.prefecture.bo.PrefectureContentBO;
import com.sibuqu.prefecture.dto.WorkContentAsyncDTO;
import com.sibuqu.prefecture.enums.PrefectureContentAsyncTypeEnum;
import com.sibuqu.prefecture.enums.PrefectureContentTypeEnum;
import com.sibuqu.work.entity.WorkTeacherReply;
import com.sibuqu.work.enums.ReplyUserTypeEnums;
import com.sibuqu.work.feignclient.client.PrefectureFeignClient;
import com.sibuqu.work.mapper.WorkTeacherReplyMapper;
import com.sibuqu.work.mongo.WorkInfoPojo;
import com.sibuqu.work.vo.admin.WorkModelDetailVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.sibuqu.work.enums.WorkDetailTypeEnum.READ;

@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class PrefectureComponent {
    private final PrefectureFeignClient prefectureFeignClient;
    private final WorkTeacherReplyMapper workTeacherReplyMapper;

    /**
     * 根据企业id 课程id 用户ID 获取班级小组
     *
     * @param workInfo
     * @param contentType
     * @return
     */
    public void workContentAsyncPrefecture(WorkInfoPojo workInfo, Integer contentType) {
        try {
            WorkContentAsyncDTO workContentAsyncDTO = new WorkContentAsyncDTO();
            BeanUtils.copyProperties(workInfo, workContentAsyncDTO);
            workContentAsyncDTO.setPublishTime(workInfo.getWorkTime());
            workContentAsyncDTO.setWorkId(workInfo.getWorkId());
            workContentAsyncDTO.setContentType(contentType);
            workContentAsyncDTO.setUsername(workInfo.getUserName());
            workContentAsyncDTO.setPhone(workInfo.getUserPhone());
            workContentAsyncDTO.setClassesTeacherName(workInfo.getClassesTeacherName());
            workContentAsyncDTO.setClassesNo(Objects.nonNull(workInfo.getClassesNo()) ? Long.valueOf(workInfo.getClassesNo()) : 0);
            workContentAsyncDTO.setRecommendStatus(workInfo.getRecommendStatus());
            // TODO: 2023/12/22 作业同步
            if (StringUtils.isNotBlank(workInfo.getContent())) {
                List<PrefectureContentBO> prefectureContentBOS = new ArrayList<>();
                List<WorkModelDetailVO> workModelDetailVOS = JSONObject.parseArray(workInfo.getContent(), WorkModelDetailVO.class);
                for (WorkModelDetailVO workModelDetailVO : workModelDetailVOS) {
                    if (!YesOrNoEnum.YES.getCode().equals(workModelDetailVO.getShowStatus())) {
                        continue;
                    }
                    if (StrUtil.isBlank(workModelDetailVO.getContent())) {
                        continue;
                    }
                    // 过滤掉读书项
                    if (READ.getCode().equals(workModelDetailVO.getType())) {
                        continue;
                    }
                    PrefectureContentBO prefectureContentBO = new PrefectureContentBO();
                    prefectureContentBO.setId(workModelDetailVO.getId());
                    prefectureContentBO.setContent(workModelDetailVO.getContent());
                    prefectureContentBO.setTitle(workModelDetailVO.getTitle());
                    prefectureContentBO.setType(PrefectureContentTypeEnum.TEXT.getCode());
                    prefectureContentBOS.add(prefectureContentBO);
                }
                if (prefectureContentBOS.size() < 1) {
                    return;
                }
                workContentAsyncDTO.setContents(prefectureContentBOS);
                if (contentType.equals(PrefectureContentAsyncTypeEnum.TEACHER_REPLY.getCode()) || contentType.equals(PrefectureContentAsyncTypeEnum.COMPANY_REPLY.getCode())) {
                    QueryWrapper<WorkTeacherReply> queryWrapper = new QueryWrapper<>();
                    queryWrapper.lambda().eq(WorkTeacherReply::getWorkId, workInfo.getId());
                    if (contentType.equals(PrefectureContentAsyncTypeEnum.TEACHER_REPLY.getCode())) {
                        queryWrapper.lambda().eq(WorkTeacherReply::getReplyUserType, ReplyUserTypeEnums.TEACHER.getCode());
                    } else if (contentType.equals(PrefectureContentAsyncTypeEnum.COMPANY_REPLY.getCode())) {
                        queryWrapper.lambda().eq(WorkTeacherReply::getReplyUserType, ReplyUserTypeEnums.COMPANY_USER.getCode());
                    }
                    List<WorkTeacherReply> workTeacherReplies = workTeacherReplyMapper.selectList(queryWrapper);
                    if (workTeacherReplies.size() > 0) {
                        workContentAsyncDTO.setReplyContents(workTeacherReplies.stream().map(workTeacherReply -> {
                            PrefectureContentBO prefectureContentBO = new PrefectureContentBO();
                            prefectureContentBO.setTitle(workTeacherReply.getUserName());
                            prefectureContentBO.setContent(workTeacherReply.getTeacherContent());
                            prefectureContentBO.setType(workTeacherReply.getTeacherReplyType().equals(TeacherReplyEnum.TEXT.getCode()) ? PrefectureContentTypeEnum.TEXT.getCode() : PrefectureContentTypeEnum.AUDIO.getCode());
                            prefectureContentBO.setMediaSize(workTeacherReply.getAudioSize());
                            prefectureContentBO.setMediaTimeLength(workTeacherReply.getAudioTimeLength());
                            return prefectureContentBO;
                        }).collect(Collectors.toList()));
                    } else {
                        workContentAsyncDTO.setReplyContents(new ArrayList<>());
                    }
                } else {
                    workContentAsyncDTO.setReplyContents(new ArrayList<>());
                }
                prefectureFeignClient.workContentAsyncPrefecture(workContentAsyncDTO);
            }
        } catch (Exception e) {
            log.error("workContentAsyncPrefecture error:{}", e.getMessage(), e);
        }
    }
}
