package com.sibuqu.work.componet;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.base.common.utils.TryCatchUtil;
import com.sibuqu.starter.logging.common.utils.LogUtils;
import com.sibuqu.user.dto.classs.ClassMemberUserInfoDTO;
import com.sibuqu.user.dto.company.BatchGetCompanySimpleDTO;
import com.sibuqu.user.dto.course.CxktResDateQueryListDTO;
import com.sibuqu.user.dto.user.UserInfoCompanySearchDTO;
import com.sibuqu.user.dto.user.UserInfoEntityListSearchDTO;
import com.sibuqu.user.dto.user.UserInfoEntitySearchDTO;
import com.sibuqu.user.dto.user.UserInfoListSearchByPhoneDTO;
import com.sibuqu.user.dto.user.UserInfoListSearchByUserIdDTO;
import com.sibuqu.user.dto.user.UserInfoSearchByUserIdDTO;
import com.sibuqu.user.dto.user.UserInfoSearchDTO;
import com.sibuqu.user.vo.api.UserInfoVO;
import com.sibuqu.user.vo.classs.ClassMemberUserInfoVO;
import com.sibuqu.user.vo.company.BatchGetCompanySimpleVO;
import com.sibuqu.user.vo.course.CxktResDateListVO;
import com.sibuqu.user.vo.user.UserInfoCompanySearchVO;
import com.sibuqu.user.vo.user.UserInfoSearchSimpleVO;
import com.sibuqu.work.feignclient.client.UserFeignClient;
import com.sibuqu.work.util.AESUtil;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor(onConstructor__ = @Autowired)
public class UserComponent {
    private final UserFeignClient userFeignClient;

    public UserInfoCompanySearchVO userByUserIdCompanyId(Integer companyId, Integer userId) {
        UserInfoCompanySearchDTO userInfoCompanySearchDTO = new UserInfoCompanySearchDTO();
        userInfoCompanySearchDTO.setCompanyId(companyId);
        userInfoCompanySearchDTO.setUserId(userId);
        log.info("userByUserIdCompanyId:{}", JSON.toJSONString(userInfoCompanySearchDTO));
        ResultInfo<UserInfoCompanySearchVO> resultInfo = userFeignClient.userByUserIdCompanyId(userInfoCompanySearchDTO);
        if (resultInfo.getCode().equals(0)) {
            if (getYanZhengVal(resultInfo.getData().getPhone())){
                resultInfo.getData().setPhone(AESUtil.encrypt(resultInfo.getData().getPhone()));
            }
            return resultInfo.getData();
        } else {
            return null;
        }
    }

    public UserInfoCompanySearchVO listenUserByUserIdCompanyId(Integer companyId, Integer userId) {
        UserInfoCompanySearchDTO userInfoCompanySearchDTO = new UserInfoCompanySearchDTO();
        userInfoCompanySearchDTO.setCompanyId(companyId);
        userInfoCompanySearchDTO.setUserId(userId);
        ResultInfo<UserInfoCompanySearchVO> resultInfo = userFeignClient.listenUserByUserIdCompanyId(userInfoCompanySearchDTO);
        if (resultInfo.getCode().equals(0)) {
            if (getYanZhengVal(resultInfo.getData().getPhone())){
                resultInfo.getData().setPhone(AESUtil.encrypt(resultInfo.getData().getPhone()));
            }
            return resultInfo.getData();
        } else {
            return null;
        }
    }

    public List<UserInfoSearchSimpleVO> userSimpleListByUserIds(List<Integer> userIds,Integer companyId) {
        UserInfoListSearchByUserIdDTO dto = new UserInfoListSearchByUserIdDTO();
        dto.setUserIds(userIds);
        dto.setCompanyId(companyId);
        ResultInfo<List<UserInfoSearchSimpleVO>> resultInfo = userFeignClient.userSimpleListByUserIds(dto);
        if (resultInfo.getCode().equals(0)) {
            return resultInfo.getData();
        } else {
            return null;
        }
    }

    public UserInfoSearchSimpleVO userSimpleByUserId(Integer userId) {
        UserInfoSearchByUserIdDTO dto = new UserInfoSearchByUserIdDTO();
        dto.setUserId(userId);
        ResultInfo<UserInfoSearchSimpleVO> resultInfo = userFeignClient.userSimpleByUserId(dto);
        if (resultInfo.getCode().equals(0)) {
            if (getYanZhengVal(resultInfo.getData().getPhone())){
                resultInfo.getData().setPhone(AESUtil.encrypt(resultInfo.getData().getPhone()));
            }
            return resultInfo.getData();
        } else {
            return null;
        }
    }

    public List<CxktResDateListVO> selectCxtResDateByIds(List<Integer> ids) {
        CxktResDateQueryListDTO dto = new CxktResDateQueryListDTO();
        dto.setIds(ids);
        ResultInfo<List<CxktResDateListVO>> resultInfo = userFeignClient.selectCxtResDateByIds(dto);
        if (resultInfo.getCode().equals(0)) {
            return resultInfo.getData();
        } else {
            return null;
        }
    }

    public UserInfoVO userByEntity(UserInfoEntitySearchDTO dto) {
        ResultInfo<UserInfoVO> resultInfo = userFeignClient.userByEntity(dto);
        if (resultInfo.getCode().equals(0)) {
            return resultInfo.getData();
        } else {
            return null;
        }
    }

    public List<UserInfoVO> userByEntityList(UserInfoEntityListSearchDTO dto) {
        ResultInfo<List<UserInfoVO>> resultInfo = userFeignClient.userByEntityList(dto);
        if (resultInfo.getCode().equals(0)) {
            return resultInfo.getData();
        } else {
            return null;
        }
    }

    public List<ClassMemberUserInfoVO> classMemberUserByEntity(ClassMemberUserInfoDTO dto) {
        ResultInfo<List<ClassMemberUserInfoVO>> resultInfo = userFeignClient.classMemberUserByEntity(dto);
        if (resultInfo.getCode().equals(0)) {
            return resultInfo.getData();
        } else {
            return null;
        }
    }

    public List<UserInfoVO> userListByPhone(UserInfoListSearchByPhoneDTO dto) {
        if (CollUtil.isNotEmpty(dto.getPhones())) {
            dto.setPhones(dto.getPhones().stream().distinct().collect(Collectors.toList()));
        }
        log.info("userListByPhone:{}", JSON.toJSONString(dto));
        ResultInfo<List<UserInfoVO>> resultInfo = userFeignClient.userListByPhone(dto);
        if (resultInfo.getCode().equals(0)) {
            return resultInfo.getData();
        } else {
            return null;
        }
    }

    @ApiOperation(value = "内部调用-企业 id 列表查询企业名称")
    public Map<Integer, String> queryCompanyNameById(List<Integer> companyIdList) {
        if (CollUtil.isEmpty(companyIdList)) {
            log.info("queryCompanyNameById companyIdList is empty");
            return new HashMap<>();
        }
        log.info("queryCompanyNameById begin");
        Map<Integer, String> map = TryCatchUtil.unpackResultInfo(TryCatchUtil.get(() -> userFeignClient.queryCompanyNameById(companyIdList)));
        log.info("queryCompanyNameById end");
        return map;
    }

    @ApiOperation(value = "内部调用-企业用户 id 列表查询企业内用户真实名称")
    public Map<Integer, String> queryUserCompanyNameById(Integer companyId, List<Integer> userIdList) {
        if (CollUtil.isEmpty(userIdList)) {
            log.info("queryUserCompanyNameById userIdList is empty");
            return new HashMap<>();
        }
        log.info("queryUserCompanyNameById begin companyId:{},userIdList:{}", companyId, userIdList);
        Map<Integer, String> map = TryCatchUtil.unpackResultInfo(TryCatchUtil.get(() -> userFeignClient.queryUserCompanyNameById(companyId, userIdList)));
        log.info("queryUserCompanyNameById end");
        if (map == null) {
            return new HashMap<>();
        }
        return map;
    }

    @ApiOperation(value = "内部服务-根据用户 id 列表查询用户昵称")
    public Map<Integer, String> userFullNameByIdList(@RequestBody List<Integer> idList) {
        if (CollUtil.isEmpty(idList)) {
            log.info("userFullNameByIdList idList is empty");
            return new HashMap<>();
        }
        log.info("userFullNameByIdList begin");
        Map<Integer, String> map = TryCatchUtil.unpackResultInfo(TryCatchUtil.get(() -> userFeignClient.userFullNameByIdList(idList)));
        log.info("userFullNameByIdList end");
        return map;
    }
    public Map<Integer, BatchGetCompanySimpleVO> batchGetCompanySimple(List<Integer> companyIds) {
        BatchGetCompanySimpleDTO batchGetCompanyCodeDTO = new BatchGetCompanySimpleDTO();
        batchGetCompanyCodeDTO.setCompanyId(companyIds);
        LogUtils.info("批量获取企业基本信息入参：{}", JSON.toJSONString(batchGetCompanyCodeDTO));
        List<BatchGetCompanySimpleVO> batchGetCompanySimpleVOS = userFeignClient.batchGetCompanySimple(batchGetCompanyCodeDTO);
        LogUtils.info("批量获取企业基本信息出参：{}", JSON.toJSONString(batchGetCompanySimpleVOS));
        Map<Integer, BatchGetCompanySimpleVO> batchGetCompanySimpleVOMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(batchGetCompanySimpleVOS)) {
            batchGetCompanySimpleVOMap = batchGetCompanySimpleVOS.stream().collect(
                    Collectors.toMap(BatchGetCompanySimpleVO::getCompanyId, Function.identity()));
        }
        return batchGetCompanySimpleVOMap;
    }

    public UserInfoVO selectValidUserByPhone(String phone) {
        UserInfoSearchDTO userInfoSearchDto = new UserInfoSearchDTO();
        userInfoSearchDto.setPhone(phone);
        log.info("selectValidUserByPhone phone:{}", phone);
        UserInfoVO userInfoVO = TryCatchUtil.getAndUnpackResultInfo(() -> userFeignClient.selectValidUserByPhone(userInfoSearchDto));
        log.info("selectValidUserByPhone userInfoVO:{}", userInfoVO);
        return userInfoVO;
    }

    private Boolean getYanZhengVal(String str){
        String regex = "\\d+";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(str);
        if (matcher.matches()){
            //纯数字
            return true;
        }else {
            return false;
        }
    }
}