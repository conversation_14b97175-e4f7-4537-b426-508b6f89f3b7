package com.sibuqu.work.componet;

import com.sibuqu.base.common.result.ResultInfo;
import com.sibuqu.base.common.utils.TryCatchUtil;
import com.sibuqu.bidata.dto.UserDoneActionInfoDTO;
import com.sibuqu.work.feignclient.client.BidataFeignClient;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@Slf4j
@Component
public class BidataComponent {

    @Autowired
    private BidataFeignClient bidataFeignClient;

    public Boolean addUserDoneAction(UserDoneActionInfoDTO userDoneActionInfoDTO){
        return TryCatchUtil.getAndUnpackResultInfo(() -> bidataFeignClient.addUserDoneAction(userDoneActionInfoDTO));
    }

    @ApiOperation(value = "内部接口-添加用户完成动作", notes = "内部接口-添加用户完成动作")
    @PostMapping("/inner/update")
    public Boolean updateDoneAction(UserDoneActionInfoDTO userDoneActionInfoDTO){
        return TryCatchUtil.getAndUnpackResultInfo(() -> bidataFeignClient.updateDoneAction(userDoneActionInfoDTO));
    }

}