package com.sibuqu.work.webclient.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2021/7/15-上午9:52
 */
@Data
@Builder
public class PerSonMyWorkVO {

    @ApiModelProperty(value = "课件id")
    private Integer courseTimetableId;

    private Integer year;

    @ApiModelProperty("所属日期")
    private LocalDate belongDate;

    @ApiModelProperty("所属日期 -月日")
    private String monthWithDay;

    @ApiModelProperty("听课状态 1已听课 0未听课")
    private Integer listenStatus;

    @ApiModelProperty("作业状态 1已提交 0未提交")
    private Integer doWorkStatus;

    @ApiModelProperty("分数")
    private Integer score;

    @ApiModelProperty("用户标识0新学员1老学员")
    private Boolean userFlag;

    @ApiModelProperty("作业模版标识（0-统一配置作业模版 1-区分老学员配置作业模版 2-分等级作业模板）")
    private Integer workModelFlag;

    @ApiModelProperty(value = "作业模板等级 a b c")
    private String level;

}
