package com.sibuqu.work.webclient.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/12/9-下午8:30
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ClassGroupRoleVO {
    /**
     * 是否有班级群
     */
    @ApiModelProperty("是否有班级群")
    private Boolean haveClassGroup = false;

    /**
     * haveClassGroup=true 的情况下的对应的组信息
     */
    @ApiModelProperty("haveClassGroup=true 的情况下的对应的组信息")
    private CommonClassGroupInfoVO commonClassGroupInfo =
            new CommonClassGroupInfoVO(0,0L,0,"","","", "");

    /**
     * 是否有工作群
     */
    @ApiModelProperty("是否有工作群")
    private Boolean haveWorkGroup = false;

    @ApiModelProperty("是否是老师")
    private Boolean isTeacher = false;

    @ApiModelProperty("是否班主任")
    private Boolean isClassTeacher = false;

    @ApiModelProperty("是否是副班主任")
    private Boolean isDeputyTeacher = false;

    /**
     * haveWorkGroup=true 的情况下的对应的组信息
     */
    @ApiModelProperty("haveWorkGroup=true 的情况下的对应的组信息")
    private WorkClassGroupInfoVO workClassGroupInfoVO = new WorkClassGroupInfoVO(0,0L,"");

    /**
     * 是否有查作业
     */
    @ApiModelProperty("是否可以查看作业")
    private Boolean lookWork = false;

    /**
     * 是否有老师回复
     */
    @ApiModelProperty("是否有老师回复")
    private Boolean haveTeacherReply = false;

    @ApiModelProperty("是否是辅导员")
    private Boolean isAssistantTeacher = false;
    /**
     * 班成员人数
     */
    @ApiModelProperty("班成员人数")
    private Integer classMemberNumber = 0;
    /**
     * 班成员人数
     */
    @ApiModelProperty("班级id")
    private Integer classGroupId;

    /**
     * 一共可以推荐作业数
     */
    @ApiModelProperty("一共可以推荐作业数")
    private Integer recommendNumber = 0;
    /**
     * 今日已推荐作业数
     */
    @ApiModelProperty("今日已推荐作业数")
    private Integer recommendedNumber = 0;

    /**
     * 推荐作业的日期
     */
    @ApiModelProperty("推荐作业的日期")
    private String doneDate;

    @ApiModelProperty("老师提问状态")
    private Boolean questionStatus;

    @ApiModelProperty(value = "是否可以移除学员开关（1是 0否）")
    private Integer removeMemberSwitch;

    @ApiModelProperty(value = "是否显示推荐作业开关（1是 0否）")
    private Integer showWorkSwitch;

    @ApiModelProperty(value = "是否显示老师回复开关（1是 0否）")
    private Integer showReplySwitch;

    @ApiModelProperty(value = "课程状态（0未开课 1开课中 2已结课）")
    private Integer courseState;

    @ApiModelProperty(value = "课程类型")
    private Integer courseType;

    @ApiModelProperty(value = "课程名称")
    private String courseTitle;
    @ApiModelProperty(value = "课程id")
    private Integer courseId;

}
