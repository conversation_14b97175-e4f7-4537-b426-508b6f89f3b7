package com.sibuqu.work.webclient.model.dto;

import com.sibuqu.base.common.page.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 用户课程所在班级小组信息搜索
 * @createTime 2021年10月13日 16:13:00
 */
@Data
public class CourseClassTeamDTO {

    @ApiModelProperty("企业ID")
    @NotNull(message = "企业ID不能为空")
    private Integer companyId;

    @ApiModelProperty("课程ID")
    @NotNull(message = "课程ID不能为空")
    private Integer courseId;

    @ApiModelProperty("用户ID")
    @NotNull(message = "用户ID不能为空")
    private Integer userId;

    private Integer sortMode;
}
