package com.sibuqu.work.webclient.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 课程配置表VO
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-29
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel(value = "CourseConfigVO", description = "课程配置表VO")
public class CourseConfigVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    @ApiModelProperty("课程ID")
    private Integer courseId;

    @ApiModelProperty("是否可以移除学员开关（1是 0否）")
    private Integer removeMemberSwitch;

    @ApiModelProperty("是否显示推荐作业开关（1是 0否）")
    private Integer showWorkSwitch;

    @ApiModelProperty("是否显示老师回复开关（1是 0否）")
    private Integer showReplySwitch;
    /**
     * 班级最小人数
     */
    @ApiModelProperty("班级最小人数")
    private Integer memberMinTotal;
    /**
     * 自动打卡规则（分钟）
     */
    @ApiModelProperty("自动打卡规则（分钟）")
    private Integer automaticClockRule;
    /**
     * 班号游标
     */
    @ApiModelProperty("班号游标")
    private Integer classesNoCursor;
    /**
     * 是否手动打卡开关（1是 0否）
     */
    @ApiModelProperty("是否手动打卡开关（1是 0否）")
    private Integer manualClockSwitch;
    /**
     * 是否延伸学习开关（1是 0否）
     */
    @ApiModelProperty("是否延伸学习开关（1是 0否）")
    private Integer extendStudySwitch;
    /**
     * 推荐作业类型（1指定数量推荐 2根据人数推荐）
     */
    @ApiModelProperty("推荐作业类型（1指定数量推荐 2根据人数推荐）")
    private Integer recommendWorkType;
    /**
     * 推荐作业数量限制
     */
    @ApiModelProperty("推荐作业数量限制")
    private Integer recommendWorkTotalLimit;
    /**
     * 推荐作业人数限制
     */
    @ApiModelProperty("推荐作业人数限制")
    private Integer recommendWorkUserLimit;
    /**
     * 延伸学习加分
     */
    @ApiModelProperty("延伸学习加分")
    private Integer extendStudyScore;
}
