package com.sibuqu.work.webclient.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/7/15-上午9:52
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ToDayWorkVO {

    @ApiModelProperty("姓名")
    private String username;

    @ApiModelProperty("听课状态 1已听课 0未听课")
    private Integer listenStatus;

    @ApiModelProperty("作业状态 1已提交 0未提交")
    private Integer doWorkStatus;

    @ApiModelProperty("分数")
    private Integer score;

    @ApiModelProperty("用户id")
    private Integer userId;

    @ApiModelProperty("总分")
    private Integer totalScore;

    @ApiModelProperty("是否展示新学员标识")
    private Boolean userFlag;

    @ApiModelProperty("作业模版标识（0-统一配置作业模版 1-区分老学员配置作业模版 2-分等级作业模板）")
    private Integer workModelFlag;

    @ApiModelProperty(value = "作业模板等级 a b c")
    private String level;

    @ApiModelProperty(value = "作业时间")
    private LocalDateTime workTime;

}
