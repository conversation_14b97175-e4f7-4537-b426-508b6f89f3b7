package com.sibuqu.work.webclient.model.vo;

import com.sibuqu.work.vo.api.TodayStudyUserTopVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ClassMemberRankVO {
    @ApiModelProperty(value = "日期")
    private String date;
    @ApiModelProperty(value = "用户排行榜")
    private List<TodayStudyUserTopVO> userTop;
}
