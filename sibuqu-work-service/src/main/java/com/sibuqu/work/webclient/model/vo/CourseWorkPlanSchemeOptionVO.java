package com.sibuqu.work.webclient.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

@Data
public class CourseWorkPlanSchemeOptionVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("听课人数")
    private Integer listenNum = 0;

    @ApiModelProperty("提交作业人数")
    private Integer doWorkNum = 0;

    @ApiModelProperty("分数")
    private Integer score = 0;

    @ApiModelProperty("课件名称")
    private String courseTimeTableName;

}