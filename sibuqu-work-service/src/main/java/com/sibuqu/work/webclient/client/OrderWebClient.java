package com.sibuqu.work.webclient.client;


import com.sibuqu.base.common.exception.BusinessException;
import com.sibuqu.work.webclient.model.vo.CourseConfigVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.List;

@Slf4j
@RefreshScope
@Component
public class OrderWebClient {
    @Value("${webclient.orderAddress:http://zlzgy-tetralogy-order}")
    private String orderAddress;

    public Integer workModelIdByCourseId(Integer courseId) {
        return WebClient.create().get()
                .uri(orderAddress + "/cloud/workModelIdByCourseId/" + courseId)
                .retrieve()
                .onStatus(ex -> ex.isError(), response -> {
                    return Mono.error(new BusinessException("课程尚未开始"));
                })
                .bodyToMono(new ParameterizedTypeReference<Integer>() {
                })
                .doOnError(Exception.class, error -> {
                    throw new BusinessException("课程尚未开始");
                }).block();
    }

    public CourseConfigVO getCourseConfig(Integer courseId) {
        return WebClient.create().get()
                .uri(orderAddress + "/cloud/getCourseConfig/" + courseId)
                .retrieve()
                .onStatus(ex -> ex.isError(), response -> {
                    return Mono.error(new BusinessException("课程尚未开始"));
                })
                .bodyToMono(CourseConfigVO.class)
                .doOnError(Exception.class, error -> {
                    throw new BusinessException("课程尚未开始");
                }).block();
    }

    public List courTimeTableIdsByWorkModelId(Integer modelId) {
        return WebClient.create().get()
                .uri(orderAddress + "/cloud/courTimeTableIdsByWorkModelId/" + modelId)
                .retrieve()
                .bodyToMono(List.class).block();
    }

}
