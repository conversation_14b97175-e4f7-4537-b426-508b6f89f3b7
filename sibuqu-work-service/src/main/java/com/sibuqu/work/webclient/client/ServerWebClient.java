package com.sibuqu.work.webclient.client;

import com.sibuqu.base.common.exception.BusinessException;
import com.sibuqu.base.common.page.PageInfoBT;
import com.sibuqu.classes.dto.CourseClassTeamSearchDTO;
import com.sibuqu.classes.vo.ClassesTeamDetailVO;
import com.sibuqu.course.dto.coursetimetable.CourseTimetableDetailDTO;
import com.sibuqu.course.vo.coursetimetable.CourseTimetableHistoryVO;
import com.sibuqu.work.bo.CourseTimetableUnlockDetailBO;
import com.sibuqu.work.dto.WorkRemindDTO;
import com.sibuqu.work.vo.api.AdditionalInfoVO;
import com.sibuqu.work.vo.api.JumpPageWorkRemindParamVO;
import com.sibuqu.work.vo.api.UserFlagInfoVO;
import com.sibuqu.work.webclient.model.dto.AdditionalInfoDTO;
import com.sibuqu.work.webclient.model.dto.ClassesTeamInfoByUserIdsDTO;
import com.sibuqu.work.webclient.model.dto.CourseClassTeamDTO;
import com.sibuqu.work.webclient.model.dto.UserByClassOrTeamDTO;
import com.sibuqu.work.webclient.model.vo.UserPhonesDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.List;

@Slf4j
@RefreshScope
@Component
public class ServerWebClient {

}
