package com.sibuqu.work.webclient.model.vo;

import com.sibuqu.work.vo.api.ClassSubRateVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ClassRankVO {
    @ApiModelProperty(value = "日期")
    private String date;
    @ApiModelProperty(value = "排行榜")
    private List<ClassSubRateVO> classRanking;
    @ApiModelProperty(value = "当前班级排名")
    private Integer currentRank;
}
