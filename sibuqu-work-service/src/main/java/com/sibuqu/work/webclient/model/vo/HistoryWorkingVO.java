package com.sibuqu.work.webclient.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;

@Data
@Builder
public class HistoryWorkingVO {

    @ApiModelProperty("所属日期")
    private LocalDate belongDate;

    @ApiModelProperty("当日人数")
    private Integer total;

    @ApiModelProperty("听课人数")
    private Integer listenNum;

    @ApiModelProperty("提交作业人数")
    private Integer doWorkNum;

    @ApiModelProperty("作业率")
    private String doWorkRate;

    @ApiModelProperty("听课率")
    private String listenRate;

    @ApiModelProperty("分数")
    private Integer score;

    @ApiModelProperty("班级号")
    private Integer classGroupId;

    @ApiModelProperty("课件id")
    private Integer courseTimeTableId;

    @ApiModelProperty("课件名称")
    private String courseTimeTableName;

    @ApiModelProperty(value = "课程id")
    private Integer courseId;

    @ApiModelProperty("是否有学习计划 1-是 0-否")
    private Integer planFlag;

}
