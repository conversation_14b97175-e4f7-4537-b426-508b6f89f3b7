package com.sibuqu.work.webclient.model.vo;

import com.alibaba.fastjson.JSONObject;
import com.sibuqu.classes.vo.MemberListByCourseIdClassesNoTeamIdVO;
import com.sibuqu.work.vo.api.UserFlagInfoVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/9-上午11:07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ClassHworkStatisticsVO {
    @ApiModelProperty(value = "总提交作业率")
    private String historySubRate;
    @ApiModelProperty(value = "今日提交作业率")
    private String todaySubRate;
    @ApiModelProperty(value = "今日班级未提交作业人数")
    private Integer classNoSubTotal;
    @ApiModelProperty(value = "今日未提交作业人数")
    private Integer noSubTotal;
    @ApiModelProperty(value = "没有提交作业的人名")
    private List<String> unsubUserName;
    @ApiModelProperty(value = "没有提交作业的人员列表")
    private List<MemberListByCourseIdClassesNoTeamIdVO> noSubUserInfos;
    @ApiModelProperty(value = "今日听课率")
    private String todayListenRate;
    @ApiModelProperty(value = "历史听课率")
    private String historyListenRate;
    @ApiModelProperty(value = "今日未听课人数")
    private Integer classNoListenTotal;
    @ApiModelProperty(value = "今日听课人数")
    private Integer listenTotal;
    @ApiModelProperty(value = "用户角色1老师2助理老师3班主任4学员")
    private Integer userRole;
    @ApiModelProperty(value = "排行榜列表")
    private List<JSONObject>  chartsList;
    @ApiModelProperty(value = "作业时间")
    private LocalDate doneDate;
    @ApiModelProperty(value = "截止日期")
    private LocalDate endDate;
    @ApiModelProperty(value = "截止当前时间 年月日时分秒")
    private LocalDateTime endTime;
    @ApiModelProperty(value = "提交作业开始时间 年月日时分秒")
    private LocalDateTime doWorkStartTime;
    @ApiModelProperty(value = "提交作业结束时间 年月日时分秒")
    private LocalDateTime doWorkEndTime;
    @ApiModelProperty(value = "课件标题")
    private String resTitle;
    @ApiModelProperty("提醒状态")
    private Boolean remindStatus = false;
    @ApiModelProperty("是否有学习计划 1-是 0-否")
    private Integer planFlag;
    @ApiModelProperty("多方案课件->用户所选方案")
    private String planSchemeOption;
    @ApiModelProperty("课件id")
    private Integer courseTimetableId;
}
