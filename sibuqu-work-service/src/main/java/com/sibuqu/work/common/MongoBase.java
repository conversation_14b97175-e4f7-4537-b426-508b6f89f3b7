package com.sibuqu.work.common;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Component
@RequiredArgsConstructor(onConstructor_ = @__(@Autowired))
public class MongoBase<T> implements Serializable {
    private final MongoTemplate mongoTemplate;
    /**
     * MongDb分页公共方法
     * @param clazz 实体类的class对象
     * @param pageSize 每页的数量
     * @param pageNum 当前的页数
     * @param query query是啥不知道赶紧去查下，相当于sql语句
     * @return
     */
    public IPage<T> pagination(Class<T> clazz, int pageSize, int pageNum, Query query) {
        long total = mongoTemplate.count(query, clazz);
        Integer pages = (int)Math.ceil((double)total / (double)pageSize);

        if (pageNum <= 0) {
            pageNum = 1;
        }
        if (pageNum > pages){
            IPage<T> pageResult = new Page<>();
            pageResult.setTotal(total);
            pageResult.setPages(pages);
            pageResult.setSize(pageSize);
            pageResult.setCurrent(pageNum);
            pageResult.setRecords(new ArrayList<>());
            return pageResult;
        }
        int skip = pageSize * (pageNum - 1);
        query.skip(skip).limit(pageSize);
        List<T> list = mongoTemplate.find(query, clazz);
        IPage<T> pageResult = new Page<>();
        pageResult.setTotal(total);
        pageResult.setPages(pages);
        pageResult.setSize(pageSize);
        pageResult.setCurrent(pageNum);
        pageResult.setRecords(list);
        return pageResult;
    }

}
